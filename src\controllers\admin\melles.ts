import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import { adminRole, buyRequestType, httpStatusCodes } from '../../utils/constants';
import { col, fn, Op } from 'sequelize';
import models from '../../models';

class Melle {
    /**
     * @api {get} /v1/auth/admin/melle-price-per-carat
     * @apiName listMellePricePerCarat
     * @apiGroup listMellePricePerCarat
     *
     *
     * @apiSuccess {Object} listMellePricePerCarat.
     */
    async getMellePricePerCarat(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!get price par carat function start!!!!!');
        try {
            const { shape, growth_type, color, clarity, sieve_size, milimeter, pointer } = req.body;

            const conditions: any = [];

            if (shape) {
                conditions.push({
                    shape: { [Op.iLike]: shape } // Case-insensitive match for shape
                });
            }

            if (growth_type) {
                conditions.push({
                    growth_type: { [Op.iLike]: growth_type } // Case-insensitive match for growth_type
                });
            }

            if (color) {
                conditions.push({
                    color: { [Op.iLike]: color } // Case-insensitive match for color
                });
            }

            if (clarity) {
                conditions.push({
                    clarity: { [Op.iLike]: clarity } // Case-insensitive match for clarity
                });
            }

            if (sieve_size) {
                conditions.push({
                    sieve_size: { [Op.iLike]: sieve_size } // Case-insensitive match for sieve_size
                });
            }

            if (milimeter) {
                conditions.push({
                    milimeter: { [Op.iLike]: milimeter } // Case-insensitive match for milimeter
                });
            }

            if (pointer) {
                conditions.push({
                    pointer: { [Op.iLike]: pointer } // Case-insensitive match for pointer
                });
            }

            const melle = await models.melles.findOne({ where: { [Op.and]: conditions } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'price_per_carat successfully listed',
                data: melle?.price_per_caret
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/melle
     * @apiName listMelle
     * @apiGroup listMelle
     *
     *
     * @apiSuccess {Object} listMelle.
     */
    async getMelleData(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!list return order function start!!!!!');
        try {
            const shape = req.query.shape;
            const growthType = req.query.growth_type;
            const color = req.query.color;
            const clarity = req.query.clarity;

            const melleData: any = {
                fancy_color: [
                    'yellow',
                    'pink',
                    'blue',
                    'red',
                    'green',
                    'purple',
                    'orange',
                    'violet',
                    'black',
                    'grey',
                    'brown',
                    'white',
                    'champagne',
                    'cognac',
                    'chameleon',
                    'others'
                ],
                fancy_intensity: [
                    'Faint',
                    'Very Light',
                    'Light',
                    'Fancy Light',
                    'Fancy',
                    'Fancy Intense',
                    'Fancy Dark',
                    'Fancy Deep',
                    'Fancy Vivid',
                    'Dark'
                ],
                fancy_overtone: [
                    'None',
                    'Greenish',
                    'Gray Greenish',
                    'Grayish',
                    'Brownish',
                    'Grayish Yellowish',
                    'Greenish Brownish',
                    'Grayish Greenish',
                    'Greenish Yellowish',
                    'Yellowish',
                    'Gray Yellowish',
                    'Pinkish',
                    'Orangish',
                    'Brownish Greenish',
                    'Yellowish Greenish',
                    'Brownish Yellowish'
                ],
                white_color: ['d', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'm+'],
                // seive_size: ['0-0.5', '1-1.5', '2-2.5', '3-3.5', '4-4.5', '5-5.5', '6-6.5', '7-7.5', '8-8.5', '9-9.5'],
                // milimeter: ['1.10', '1.20', '1.30', '1.40', '1.50', '1.60', '1.70', '1.80', '1.90', '2.00'],
                // pointer: ['1.00', '1.50', '2.00', '2.50', '3.00', '3.50', '4.00', '4.50', '5.00'],
                clarity: ['FL', 'IF', 'VVS1', 'VVS2', 'VS1', 'VS2', 'SI1', 'SI2', 'I1', 'I2', 'I3'],
                shape: [
                    'Round',
                    'Oval',
                    'Pear',
                    'Cushion',
                    'Cush Mod',
                    'Cush Brill',
                    'Emerald',
                    'Radiant',
                    'Princess',
                    'Asscher',
                    'Square',
                    'Marquise',
                    'Heart',
                    'Trilliant',
                    'Euro Cut',
                    'Old Miner',
                    'Briolette',
                    'Rose Cut',
                    'Lozenge',
                    'Baguette',
                    'T.Baguette',
                    'Half Moon',
                    'Flanders',
                    'Trapezoid',
                    'Bullets',
                    'Kite',
                    'Shield',
                    'Star',
                    'Pentagonal',
                    'Hexagonal',
                    'Octagonal',
                    'Other'
                ],
                max_quantity: 1000,
                per_diamond_caret: 0.07,
                measurements: []
            };

            // Fetch distinct colors
            const colors = await models.melles.findAll({
                attributes: [[fn('DISTINCT', fn('LOWER', col('color'))), 'color']],
                distinct: true,
                where: {
                    color: {
                        [Op.ne]: '',
                        [Op.not]: null
                    }
                },
                raw: true
            });

            // Fetch distinct clarity
            const clarities = await models.melles.findAll({
                attributes: [[fn('DISTINCT', fn('LOWER', col('clarity'))), 'clarity']],
                distinct: true,
                where: {
                    clarity: {
                        [Op.ne]: '',
                        [Op.not]: null
                    }
                },
                raw: true
            });

            // Fetch distinct shape
            const shapes = await models.melles.findAll({
                attributes: [[fn('DISTINCT', fn('LOWER', col('shape'))), 'shape']],
                distinct: true,
                where: {
                    shape: {
                        [Op.ne]: '',
                        [Op.not]: null
                    }
                },
                raw: true
            });

            if (shape && growthType && color && clarity) {
                // Fetch sieve size, mm, pointer
                const measurements = await models.melles.findAll({
                    attributes: ['sieve_size', 'milimeter', 'pointer'],
                    where: {
                        [Op.and]: [
                            shape ? { shape: { [Op.iLike]: `%${shape}%` } } : {},
                            growthType ? { growth_type: { [Op.iLike]: `%${growthType}%` } } : {},
                            color ? { color: { [Op.iLike]: `%${color}%` } } : {},
                            clarity ? { clarity: { [Op.iLike]: `%${clarity}%` } } : {}
                        ]
                    }
                });

                if (measurements.length) {
                    try {
                        // Helper to extract numeric value from sieve_size
                        const parseSieveSize = (sieveSize: any) => {
                            if (!sieveSize || sieveSize.trim() === '') {
                                return -1;
                            } // Treat empty values as smallest
                            const match = sieveSize.match(/([+-]?\d*\.?\d+)/g); // Extract all numeric parts
                            return match ? parseFloat(match[0]) : -1; // Use the first number for sorting
                        };

                        // Sort measurements by sieve_size
                        measurements.sort((a, b) => parseSieveSize(a.sieve_size) - parseSieveSize(b.sieve_size));

                        /// measurement
                        melleData.measurements = measurements;

                        /// remove sieve size if all blank
                        const allEmpty = measurements.every(
                            (item: any) => item.sieve_size === '' || item.sieve_size === null
                        );

                        if (allEmpty) {
                            melleData.measurements = measurements.map((item: any) => ({
                                milimeter: item?.milimeter,
                                pointer: item?.pointer
                            }));
                        }

                        // Function to remove duplicates based on all values (sieve_size, millimeter, pointer)
                        melleData.measurements = melleData.measurements.reduce((acc: any, current: any) => {
                            // Check if the exact object (all three properties) already exists in the accumulator
                            const isDuplicate = acc.some(
                                (item: any) =>
                                    item?.sieve_size === current?.sieve_size &&
                                    item?.millimeter === current?.millimeter &&
                                    item?.pointer === current?.pointer
                            );

                            // If it's not a duplicate, add it to the accumulator
                            if (!isDuplicate) {
                                acc.push(current);
                            }

                            return acc;
                        }, []);

                        ///
                    } catch (error: any) {
                        logger.error('sort seive size pointer eror');
                    }
                }
            }

            if (shapes.length) {
                melleData.shape = [...new Set([...shapes.map((item: any) => item?.shape).sort()])];
            }

            if (colors.length) {
                melleData.white_color = [
                    ...new Set(
                        [
                            // String(growthType).toLowerCase() === 'hpht' ? 'd' : 'e',
                            ...colors.map((item: any) => item?.color)
                        ].sort()
                    )
                ];
            }

            if (clarities.length) {
                melleData.clarity = clarities
                    .map((item: any) => item?.clarity)
                    .sort()
                    .reverse();
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Melle details successfully listed',
                data: melleData
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/admin/melle
     * @apiName AddMelle
     * @apiGroup AddMelle
     *
     *
     * @apiSuccess {Object} AddMelle.
     */
    async addMelle(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!add melle function start!!!!!');
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const { melle_array } = req.body;

            await models.melles.bulkCreate(
                melle_array.map((item) => ({
                    ...item,
                    price_per_caret: parseFloat(item.price_per_caret)
                }))
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'melles created successfully!!!!'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {delete} /v1/auth/admin/melle
     * @apiName deleteMelle
     * @apiGroup deleteMelle
     *
     *
     * @apiSuccess {Object} deleteMelle.
     */
    async deleteMelle(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!delete melle function start!!!!!');
        try {
            // const role = req[`role`];
            // const id = req[`id`];

            // remove all entries from table
            await models.melles.destroy({ truncate: true });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Melle deleted successfully!!!!!'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Melle();
