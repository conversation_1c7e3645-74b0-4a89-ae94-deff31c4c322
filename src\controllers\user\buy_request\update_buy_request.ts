import models, { sequelize } from '../../../models';
import {
    PaymentMode,
    adminRole,
    buyRequestStatus,
    calculateFinancialYear,
    getInvoiceNumber,
    isStocksAvailable,
    paymentType,
    unifiedOrderType,
    vendorOrderStatus
} from '../../../utils/constants';
import { Op, Transaction } from 'sequelize';
import authorizePayment from '../authorize_payment/authorize_payment';
import { logger } from '../../../utils/logger';

export const manageUpdatedAndPending = async (
    buyRequest: any,
    id: any,
    role: any,
    reqId: any,
    status: any,
    stock_ids: any,
    applePayResponse: any,
    reject_reason: any,
    transaction: Transaction
) => {
    logger.info('Calling manageUpdatedAndPending buy request function!!!!!!');
    logger.info('Update stocks status to available which are not available!!!!!!!!!!');
    /// get not available stock ids for updating the status to available as these stocks are not available and also not having any suggested stocks
    const updateStockToAvailableIds = stock_ids
        .filter((item: any) => !isStocksAvailable(item) && !item.suggested_stock)
        .map((item: any) => item.stock_id);

    if (updateStockToAvailableIds.length) {
        /// update stock status to available for not available stocks
        await models.stocks.update(
            { status: 'AVAILABLE' },
            { where: { id: { [Op.in]: updateStockToAvailableIds } }, transaction }
        );
    }

    logger.info('Filtering stocks');

    /// filter un available when user update
    if (adminRole.user === role) {
        stock_ids = stock_ids.filter((item: any) => isStocksAvailable(item));
    }

    /// filter un available if stock not suggested for admin or sub admin
    else if (adminRole.superAdmin === role || adminRole.subAdmin === role) {
        stock_ids = stock_ids.filter((item: any) => isStocksAvailable(item) || item.suggested_stock);
    }

    /// auto cancel order if stock_ids = []
    if (!stock_ids?.length) {
        logger.info('Auto cancelling buy request!!!!');
        await models.buy_requests.update(
            { status: buyRequestStatus.autoCanceled, reject_reason },
            { where: { id }, transaction }
        );

        /// restore credit limit
        if (buyRequest.payment_mode === PaymentMode.creditLimit) {
            logger.info('re-credit credit-limit!!!!!');
            /// get credit limit used
            const creditLimitUsed: any = await models.credit_histories.findOne({
                where: { buy_request_id: buyRequest.id, transaction_type: 'DEBIT' },
                attributes: ['credit'],
                order: [['createdAt', 'DESC']],
                transaction
            });

            if (creditLimitUsed?.credit) {
                await models.credit_histories.create(
                    {
                        user_id: buyRequest.user_id,
                        credit: parseFloat(parseFloat(creditLimitUsed.credit).toFixed(2)),
                        type: 'CANCEL-BUY-REQUEST',
                        transaction_type: 'CREDIT',
                        buy_request_id: buyRequest.id
                    },
                    { transaction }
                );
            }
        }
        /// void transaction for auto-cancelled
        else if (buyRequest.payment_mode === PaymentMode.creditCard) {
            await authorizePayment.voidTransaction(buyRequest.id, transaction, buyRequestStatus.autoCanceled);
        }
        /// refund transaction for apple pay
        else if (buyRequest.payment_mode === PaymentMode.applePay) {
            await authorizePayment.refundTransaction(buyRequest?.authorized_amount, buyRequest, transaction);
        }

        return false;
    }

    logger.info('Managing suggested stocks!!!');

    /// get suggested stock id
    const suggestedStockIds: any[] = stock_ids
        .filter((item: any) => item?.suggested_stock?.stock_id)
        .map((stockId: any) => stockId?.suggested_stock?.stock_id);

    /// put suggested stocks on hold
    if (suggestedStockIds && suggestedStockIds.length) {
        /// find all suggested stocks
        const suggestedStocks: any = await models.stocks.findAll({ where: { id: { [Op.in]: suggestedStockIds } } });

        /// refresh suggested stocks
        if (suggestedStockIds?.length !== suggestedStocks?.length) {
            throw new Error('Please refresh suggested stocks!!!');
        }

        /// change stock status to HOLD
        await models.stocks.update({ status: 'HOLD' }, { where: { id: { [Op.in]: suggestedStockIds } }, transaction });
    }

    logger.info('extracting vendor ids');

    /// extract vendor ids from stock_ids if includes vendors stock
    const vendor_ids = [
        ...new Set(
            stock_ids
                .map((item: any) => item.vendor_id)
                .filter((vendorId: any) => vendorId !== null && vendorId !== undefined)
        )
    ];

    /// fetch stock details
    const stocks = await models.stocks.findAll({
        where: { id: { [Op.in]: stock_ids.map((stockId: any) => stockId.stock_id) } },
        attributes: [
            'id',
            'stock_id',
            'price_per_caret',
            'final_price',
            'weight',
            'diamond_type',
            'is_lab_grown',
            'growth_type',
            'sku_number'
        ]
    });

    logger.info('Calculating total amount!!!');

    /// calculate total amount
    const totalAmount = stock_ids
        .filter((item: any) => isStocksAvailable(item))
        .map((item: any) => item.stock_id)
        .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price ?? 0)
        .reduce((a: any, b: any) => a + b, 0);

    const user = await models.users.findOne({ where: { id: buyRequest.user_id }, attributes: ['is_verified'] });

    /// update tax if not zero
    const taxPrice: number =
        user?.is_verified ?? false ? 0 : parseFloat(totalAmount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

    const grandTotal =
        parseFloat(totalAmount) + parseFloat(buyRequest.shipment_price) + parseFloat(taxPrice.toFixed(2));

    logger.info(`Updating buy requests with updated data!!!!`);

    /// check for vendor id margin approval
    const isMarginApprovedForAllVendorStones = stock_ids
        .filter((item: any) => item?.vendor_id)
        ?.every((stockId: any) => stockId?.is_margin_approved === true);

    /// update buy request
    await models.buy_requests.update(
        {
            status,
            stock_ids,
            vendor_ids,
            updated_by_id: reqId,
            amount: totalAmount,
            tax_price: taxPrice,
            grand_total: grandTotal,
            is_margin_approved: isMarginApprovedForAllVendorStones,
            is_capture_failed: false
        },
        { where: { id }, transaction }
    );

    /// update credit limit
    /// from user
    if (buyRequest.payment_mode === PaymentMode.creditLimit && role === adminRole.user) {
        logger.info('Checking available credit and manage credit limit');
        /// get credit limit used
        const creditLimitUsed: any = await models.credit_histories.findOne({
            where: { buy_request_id: buyRequest.id, transaction_type: 'DEBIT' },
            attributes: ['credit'],
            order: [['createdAt', 'DESC']],
            transaction
        });

        if (creditLimitUsed?.credit) {
            /// check amount changed
            if (
                parseFloat(parseFloat(creditLimitUsed.credit).toFixed(2)) !==
                parseFloat(parseFloat(grandTotal.toString()).toFixed(2))
            ) {
                /// create old amount
                await models.credit_histories.create(
                    {
                        user_id: buyRequest.user_id,
                        credit: parseFloat(parseFloat(creditLimitUsed.credit).toFixed(2)),
                        type: 'UPDATE-BUY-REQUEST',
                        transaction_type: 'CREDIT',
                        buy_request_id: buyRequest.id
                    },
                    { transaction }
                );

                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: buyRequest.user_id
                    },
                    transaction
                });

                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: buyRequest.user_id
                    },
                    transaction
                });

                const availableCredit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                if (
                    parseFloat(parseFloat(grandTotal.toString()).toFixed(2)) >
                    parseFloat(parseFloat(availableCredit.toString()).toFixed(2))
                ) {
                    throw new Error('Not enough credit');
                }

                /// debit new amount
                await models.credit_histories.create(
                    {
                        user_id: buyRequest.user_id,
                        credit: parseFloat(parseFloat(grandTotal.toString()).toFixed(2)),
                        type: 'UPDATE-BUY-REQUEST',
                        transaction_type: 'DEBIT',
                        buy_request_id: buyRequest.id
                    },
                    { transaction }
                );
            }
        }
    }
    /////// reauthorize
    else if (buyRequest.payment_mode === PaymentMode.creditCard && role === adminRole.user) {
        logger.info('re-authorizing on update buy request when amount is larger!!!!!!!!');
        /// handle amount variation
        if (
            parseFloat(parseFloat(grandTotal.toString()).toFixed(2)) >
            parseFloat(parseFloat(buyRequest.authorized_amount).toFixed(2))
        ) {
            /// void previously authorized amount
            await authorizePayment.voidTransaction(buyRequest.id, transaction, buyRequestStatus.accepted);

            buyRequest.grand_total = grandTotal;
            /// reauthorize credit card
            await authorizePayment.authorizeCreditCard(
                true,
                buyRequest.card_number,
                buyRequest.card_holder_name,
                buyRequest.exp_date,
                '',
                buyRequest.user_id,
                buyRequest,
                transaction,
                buyRequestStatus.accepted
            );
        }
    }
    /////// recapture apple pay
    else if (buyRequest.payment_mode === PaymentMode.applePay && role === adminRole.user) {
        logger.info('Refunding and create apple pay when amount is larger!!!!!!');
        /// handle amount variation
        if (
            parseFloat(parseFloat(grandTotal.toString()).toFixed(2)) >
            parseFloat(parseFloat(buyRequest.authorized_amount).toFixed(2))
        ) {
            /// refund transaction
            await authorizePayment.refundTransaction(buyRequest?.authorized_amount, buyRequest, transaction);
            /// create apple pay transaction
            await authorizePayment.createApplePayTransaction(
                buyRequest.id,
                applePayResponse,
                buyRequest.user_id,
                transaction
            );
        }
    }
};

export const manageAcceptedStatus = async (
    req: any,
    buyRequest: any,
    stock_ids: any,
    reqId: any,
    transaction: Transaction
) => {
    logger.info('Accepting buy request!!!');
    /// check for actions taken
    stock_ids.map((item: any) => {
        if (!item.is_action_taken) {
            throw new Error(`couldn't sell, Action required from vendor \n ${JSON.stringify(item)}`);
        }
    });

    /// fetch user for user details
    const user = await models.users.findOne({
        where: { id: buyRequest.user_id },
        attributes: { exclude: ['password', 'otp', 'fcm_token'] },
        transaction
    });

    /// fetch stocks for final price
    // const stocks = await models.stocks.findAll({
    //     where: { id: { [Op.in]: stock_ids.map((item: any) => item.stock_id) } },
    //     attributes: ['final_price']
    // });

    // /// calculate total amount
    // const total_amount = stocks.reduce((acc: any, item: any) => {
    //     return acc + item.final_price;
    // }, 0);

    /// fetch latest generate invoice number
    const lastInvoice: any = await models.orders.findOne({
        order: [['createdAt', 'DESC']],
        attribute: ['invoice_number', 'financial_year']
    });

    /// generate invoice number
    const invoiceNumber: string = `${process.env.INVOICE_PREFIX}${getInvoiceNumber(
        lastInvoice
    )}${calculateFinancialYear().replace('-', '')}`;

    /// order data
    const orderData: any = {
        user_id: buyRequest.user_id,
        buy_request_id: buyRequest.id,
        amount: buyRequest.amount,
        shipment_id: buyRequest.shipment_id,
        shipment_price: buyRequest.shipment_price,
        tax_price: buyRequest.tax_price,
        grand_total: buyRequest.grand_total,
        payment_status: 'PAID',
        order_code: buyRequest.order_code,
        order_status: 'PENDING',
        billing_address: buyRequest.billing_address,
        shipping_address: buyRequest.shipping_address,
        payment_mode: buyRequest.payment_mode,
        user_details: user,
        invoice_number: invoiceNumber,
        financial_year: calculateFinancialYear(),
        unified_order_type: unifiedOrderType.DIAMONDS,
        authorize_payment_details:
            buyRequest?.payment_mode === PaymentMode.creditLimit
                ? null
                : {
                      trans_id: buyRequest?.trans_id,
                      ref_id: buyRequest?.ref_id,
                      ref_trans_id: buyRequest?.ref_trans_id,
                      authorize_transaction_payload: JSON.stringify(buyRequest?.authorize_transaction_payload),
                      card_number: buyRequest?.card_number,
                      card_holder_name: buyRequest?.card_holder_name,
                      exp_date: buyRequest?.exp_date,
                      card_type: buyRequest?.card_type,
                      authorized_amount: parseFloat(parseFloat(buyRequest?.authorized_amount ?? 0).toFixed(2))
                  }
    };

    logger.info('Creating order!!!!');

    /// create order
    const order = await models.orders.create(orderData, { transaction });

    /// capture amount for accepted buy request
    if (buyRequest.payment_mode === PaymentMode.creditCard) {
        logger.info('Capturing authorized amount for accepted buy request');
        /// handle capture failed from user side
        if (req[`role`] === adminRole.user) {
            /// check for customer profile id exists
            if (!req[`user`]?.customer_profile_id) {
                /// create customer profile
                req.body.billing_address = buyRequest.billing_address;
                await authorizePayment.createCustomerProfile(req, transaction);
            }

            /// authorize credit card
            await authorizePayment.authorizeCreditCard(
                false,
                buyRequest.card_number,
                buyRequest.card_holder_name,
                buyRequest.exp_date,
                buyRequest.cvv,
                req[`id`],
                buyRequest,
                transaction,
                buyRequestStatus.accepted
            );

            /// capture amount
            await authorizePayment.capturePreviouslyAuthorizedAmount(
                buyRequest.id,
                transaction,
                buyRequestStatus.accepted
            );

            /////
        } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(req[`role`])) {
            /// capture reauthorized amount
            await authorizePayment.capturePreviouslyAuthorizedAmount(
                buyRequest.id,
                transaction,
                buyRequestStatus.accepted
            );
        }
    }
    /// refund apple pay if amount is less than authorized amount
    else if (buyRequest.payment_mode === PaymentMode.applePay) {
        logger.info('Refunding apple pay if amount is less than authorized amount');
        const authorizedAmount = parseFloat(parseFloat(String(buyRequest.authorized_amount)).toFixed(2));
        const grandTotal = parseFloat(parseFloat(String(buyRequest.grand_total)).toFixed(2));
        const refundAmount = authorizedAmount - grandTotal;
        if (refundAmount > 0) {
            await authorizePayment.refundTransaction(refundAmount, buyRequest, transaction);
        }
    }

    /// fetch order for order trail
    const orderPayload = await models.orders.findOne({ where: { id: order.id }, transaction });

    logger.info('creating order trails!!!!');

    /// create order trail
    await models.order_trails.create(
        {
            order_id: orderPayload.id,
            user_id: orderPayload.user_id,
            buy_request_id: orderPayload.buy_request_id,
            updated_by_id: reqId,
            payload: JSON.stringify(orderPayload),
            payment_status: orderPayload.payment_status, // pending, paid, failed, canceled
            order_status: orderPayload.order_status // pending, processing, shipped, delivered, canceled
        },
        { transaction }
    );

    /// vendor order items
    const vendorStocks: any[] = stock_ids
        .filter((item: any) => item.vendor_id)
        .map((item: any) => {
            const stockMargin = buyRequest?.stock_ids?.find((stockId: any) => stockId.stock_id === item.stock_id);
            return {
                ...item,
                order_id: order.id,
                approved_margin: stockMargin,
                buy_request_id: order.buy_request_id,
                status: vendorOrderStatus.pending
            };
        });

    logger.info('Creating vendor orders!!!!!!');

    /// create vendor orders
    await models.vendor_orders.bulkCreate(vendorStocks, { transaction });

    /// fetch vendor orders to create vendor order trails
    const vendorOrders = await models.vendor_orders.findAll({ where: { order_id: order.id }, transaction });

    /// vendor order trails items
    const vendorOrdersTrailsData: any[] = vendorOrders.map((vendorOrder: any) => ({
        vendor_order_id: vendorOrder.id,
        order_id: vendorOrder.order_id,
        buy_request_id: vendorOrder.buy_request_id,
        vendor_id: vendorOrder.vendor_id,
        approved_margin: vendorOrder.approved_margin,
        updated_by_id: reqId,
        payload: JSON.stringify(vendorOrder),
        status: vendorOrder.status // pending, shipped, paid
    }));

    logger.info('Creating vendor order trails!!!!');

    /// create vendor order trail
    await models.vendor_order_trails.bulkCreate(vendorOrdersTrailsData, { transaction });

    logger.info('Putting stocks on SOLD!!!');

    /// update stock status to SOLD
    await models.stocks.update(
        { status: 'SOLD' },
        { where: { id: { [Op.in]: stock_ids.map((item: any) => item.stock_id) } }, transaction }
    );

    /// update user order count
    await models.users.update(
        { order_count: sequelize.literal('order_count + 1') },
        { where: { id: reqId }, transaction }
    );

    /// extract vendor ids from stock_ids if includes vendors stock
    const vendor_ids = [
        ...new Set(
            stock_ids
                .map((item: any) => item.vendor_id)
                .filter((vendorId: any) => vendorId !== null && vendorId !== undefined)
        )
    ];

    /// update order count in vendors
    if (vendor_ids) {
        await models.vendors.update(
            { order_count: sequelize.literal('order_count + 1') },
            { where: { id: { [Op.in]: vendor_ids } }, transaction }
        );
    }
};

export const manageCanceledStatus = async (buyRequest: any, stock_ids: any, transaction: Transaction) => {
    logger.info('Cancelling buy request!!!');

    logger.info('Putting stock Available!!!');
    /// update stock AVAILABLE if canceled
    await models.stocks.update(
        { status: 'AVAILABLE' },
        { where: { id: { [Op.in]: stock_ids.map((item: any) => item.stock_id) } }, transaction }
    );

    /// restore credit limit
    if (buyRequest.payment_mode === PaymentMode.creditLimit) {
        logger.info('re-credit credit-limit');
        /// get credit limit used
        const creditLimitUsed: any = await models.credit_histories.findOne({
            where: { buy_request_id: buyRequest.id, transaction_type: 'DEBIT' },
            attributes: ['credit'],
            order: [['createdAt', 'DESC']],
            transaction
        });

        if (creditLimitUsed?.credit) {
            await models.credit_histories.create(
                {
                    user_id: buyRequest.user_id,
                    credit: parseFloat(parseFloat(creditLimitUsed?.credit).toFixed(2)),
                    type: 'CANCEL-BUY-REQUEST',
                    transaction_type: 'CREDIT',
                    buy_request_id: buyRequest.id
                },
                { transaction }
            );
        }
    }

    /// void transaction for credit card
    else if (buyRequest.payment_mode === PaymentMode.creditCard) {
        await authorizePayment.voidTransaction(buyRequest.id, transaction, buyRequestStatus.cancelled);
    }
    /// void transaction for apple pay
    else if (buyRequest.payment_mode === PaymentMode.applePay) {
        await authorizePayment.refundTransaction(buyRequest?.authorized_amount, buyRequest, transaction);
    }

    ////
};
