import { NextFunction, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import { BannerAttributes } from '../../models/banners';
import models from '../../models/index';
import { Op } from 'sequelize';
import moment from 'moment';

class Banner {
    /**
     * @api {get} /v1/auth/admin/banner
     * @apiName ListBanner
     * @apiGroup AdminBanner
     *
     *
     * @apiSuccess {Object} Admin.
     */

    /// list Banner
    async listBanner(req: Request, res: Response, next: NextFunction) {
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const placement = req.query.placement;
            const type = req.query.type;
            const isActive = req.query.is_active;

            const conditions: any = [];

            if (isActive) {
                conditions.push({
                    is_active: isActive
                });
            }

            if (placement) {
                conditions.push({ placement });
            }

            if (type) {
                conditions.push({ type });
            }

            /// fetch entries which are not expired
            conditions.push({
                [Op.or]: [{ expiresAt: null }, { expiresAt: { [Op.gt]: moment.utc() } }]
            });

            const { rows, count } = await models.banners.findAndCountAll({
                where: { [Op.and]: conditions },
                order: [['order', 'DESC']],
                offset: skip,
                limit
            });

            return res.json({
                status: 200,
                message: 'Banners listed successfully',
                rows,
                count
            });
        } catch (error: any) {
            logger.error('error:listBanner', error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/admin/banner
     * @apiName AddBanner
     * @apiGroup AdminBanner
     *
     *
     * @apiSuccess {Object} Admin.
     */

    /// add Banner
    async addBanner(req: Request, res: Response, next: NextFunction) {
        try {
            const { banner_image } = req.body;
            const dataObject: BannerAttributes = req.body;

            for (const key in req.body) {
                if (req.body[key]) {
                    dataObject[key] = req.body[key];
                }
            }

            if (banner_image) {
                dataObject.banner_image_url = `${process.env.BASE_URL}/banner/${banner_image}`;
            }

            await models.banners.create(dataObject);

            return res.json({
                status: 200,
                message: 'Banner created successfully'
            });
        } catch (error: any) {
            logger.error('error:addBanner', error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/banner
     * @apiName UpdateBanner
     * @apiGroup AdminBanner
     *
     *
     * @apiSuccess {Object} Admin.
     */

    /// update Banner
    async updateBanner(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, banner_image } = req.body;
            const dataObject: BannerAttributes = req.body;

            const banner = await models.banners.findOne({ where: { id } });

            if (!banner) {
                throw new Error('banner not found');
            }

            for (const key in req.body) {
                if (req.body[key]) {
                    dataObject[key] = req.body[key];
                }
            }

            if (banner_image) {
                dataObject.banner_image_url = `${process.env.BASE_URL}/banner/${banner_image}`;
            }

            await models.banners.update(dataObject, { where: { id } });

            return res.json({
                status: 200,
                message: 'Banner updated successfully'
            });
        } catch (error: any) {
            logger.error('error:updateBanner', error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/banner/status
     * @apiName UpdateBannerStatus
     * @apiGroup AdminBanner
     *
     *
     * @apiSuccess {Object} Admin.
     */

    /// change banner status
    async changeBannerStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_active } = req.body;

            const banner = await models.banners.findOne({ where: { id } });

            if (!banner) {
                throw new Error('banner not found');
            }

            await models.banners.update({ is_active }, { where: { id } });

            return res.json({
                status: 200,
                message: 'Banner status changed successfully'
            });
        } catch (error: any) {
            logger.error('error:changeBannerStatus', error);
            next(error);
        }
    }

    /**
     * @api {delete} /v1/auth/admin/banner
     * @apiName DeleteBannerStatus
     * @apiGroup AdminBanner
     *
     *
     * @apiSuccess {Object} Admin.
     */

    /// delete banner
    async deleteBanner(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;

            const banner = await models.banners.findOne({ where: { id } });

            if (!banner) {
                throw new Error('banner not found');
            }

            await models.banners.update({ _deleted: true }, { where: { id } });

            return res.json({
                status: 200,
                message: 'Banner deleted successfully'
            });
        } catch (error: any) {
            logger.error('error:deleteBanner', error);
            next(error);
        }
    }
}

export default new Banner();
