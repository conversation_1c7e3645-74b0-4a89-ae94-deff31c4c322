import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface VendorAdminFileHistoryAttributes {
    id?: string;
    vendor_id: string;
    admin_id: string;
    file_name: string;
    vendor_type?: string; // FTP, UPLOAD, API
    payload?: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface VendorAdminFileHistoryCreationAttributes extends Optional<VendorAdminFileHistoryAttributes, 'id'> {}

interface VendorAdminFileHistoryInstance
    extends Model<VendorAdminFileHistoryAttributes, VendorAdminFileHistoryCreationAttributes>,
        VendorAdminFileHistoryAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type VendorAdminFileHistoryStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => VendorAdminFileHistoryInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const vendor_admin_file_histories = sequelize.define<VendorAdminFileHistoryInstance>(
        'vendor_admin_file_histories',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            file_name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            payload: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            vendor_type: {
                type: DataTypes.ENUM(DataTypes.STRING),
                values: ['API', 'UPLOAD', 'FTP'],
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as VendorAdminFileHistoryStatic;

    // TODO: make common function to sync
    // await vendor_admin_file_histories.sync({ alter: true });

    return vendor_admin_file_histories;
};
