import { IRouter, Request, Response, NextFunction, Router } from 'express';
import userBuyRequest from '../../../controllers/user/user_buy_request';
import { fieldsValidator } from '../../../middlewares/validator';
import { acceptBuyRequestSchema } from '../../../utils/schema/buy_request_schema';

const routes: IRouter = Router();

// accept buy request
routes.put(
    '/accept-buy-request',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, acceptBuyRequestSchema),
    userBuyRequest.acceptBuyRequest
);

// decline buy request
routes.put('/decline-buy-request', userBuyRequest.declineBuyRequest);

// get updated buy request count
routes.get('/updated-buy-request-count', userBuyRequest.updatedBuyRequestCount);

export default routes;
