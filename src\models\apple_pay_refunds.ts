import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface ApplePayRefundAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    buy_request_id: string;
    parent_buyrequest_id: string;
    refund_status: string;
    transaction_details: string;
    refund_amount: number;
    payment_details: object;
    trans_id: string;
    ref_id: string;
    order_id: string;
    ref_trans_id: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface ApplePayRefundCreationAttributes extends Optional<ApplePayRefundAttributes, 'id'> { }

interface ApplePayRefundInstance
    extends Model<ApplePayRefundAttributes, ApplePayRefundCreationAttributes>,
    ApplePayRefundAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type ApplePayRefundStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => ApplePayRefundInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const apple_pay_refunds = sequelize.define<ApplePayRefundInstance>(
        'apple_pay_refunds',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            parent_buyrequest_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            refund_status: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: ['PENDING', 'SUCCESS', 'ERROR']
            },
            transaction_details: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            refund_amount: {
                type: DataTypes.DOUBLE,
                allowNull: false
            },
            payment_details: {
                type: DataTypes.JSONB,
                allowNull: false
            },
            trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            ref_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            ref_trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as ApplePayRefundStatic;
    //
    // await apple_pay_refunds.sync({ alter: true })

    return apple_pay_refunds;
};
