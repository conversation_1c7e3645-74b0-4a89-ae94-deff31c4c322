import { Router, IRouter, Request, Response, NextFunction } from 'express';
import melle from '../../../controllers/admin/melles';
import { fieldsValidator } from '../../../middlewares/validator';
import { getMellePricePerCaratSchema } from '../../../utils/schema/melle_schema';

const router: IRouter = Router();

router.post(
    '/melle-price-per-carat',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, getMellePricePerCaratSchema),
    melle.getMellePricePerCarat
);

export default router;
