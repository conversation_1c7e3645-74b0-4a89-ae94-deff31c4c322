import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface ApplePayTrailsAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    token: string;
    buy_request_id: string;
    parent_buyrequest_id: string;
    buy_request_status: string;
    status: string;
    amount: number;
    card_details: string;
    trans_id: string;
    ref_id: string;
    order_id: string;
    ref_trans_id: string;
    customer_profile_id: string;
    apple_pay_response: string;
    authorize_payload: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface ApplePayTrailsCreationAttributes extends Optional<ApplePayTrailsAttributes, 'id'> { }

interface ApplePayTrailsInstance
    extends Model<ApplePayTrailsAttributes, ApplePayTrailsCreationAttributes>,
    ApplePayTrailsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type ApplePayTrailsStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => ApplePayTrailsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const apple_pay_trails = sequelize.define<ApplePayTrailsInstance>(
        'apple_pay_trails',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            token: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            parent_buyrequest_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            buy_request_status: {
                type: DataTypes.ENUM,
                allowNull: true,
                values: ['PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED']
            },
            status: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: [
                    'CREATE-CUSTOMER',
                    'AUTHORIZE',
                    'RE-AUTHORIZE',
                    'CAPTURE',
                    'VOID',
                    'REFUND',
                    'CREATE-APPLE-PAY',
                    'CREATE-APPLE-PAY-ERROR',
                    'AUTHORIZE-ERROR',
                    'RE-AUTHORIZE-ERROR',
                    'CAPTURE-ERROR',
                    'VOID-ERROR',
                    'REFUND-ERROR',
                    'CREATE-CUSTOMER-ERROR'
                ]
            },
            amount: {
                type: DataTypes.DOUBLE,
                allowNull: true
            },
            customer_profile_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            card_details: {
                type: DataTypes.STRING,
                allowNull: true
            },
            trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            ref_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            ref_trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            apple_pay_response: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            authorize_payload: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as ApplePayTrailsStatic;
    //
    // await apple_pay_trails.sync({ alter: true })

    return apple_pay_trails;
};
