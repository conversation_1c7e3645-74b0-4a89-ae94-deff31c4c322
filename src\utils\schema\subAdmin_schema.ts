import Joi from 'joi';
import { isPhoneNumber } from '../../utils/validators';

export const createSubAdminSchema = Joi.object({
    first_name: Joi.string().trim().required(),
    last_name: Joi.string().trim().required(),
    email: Joi.string().email().trim().optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    password: Joi.string().min(4).trim().required(),
    permission: Joi.object().required()
});

export const updateSubAdminSchema = Joi.object({
    id: Joi.string().id().required(),
    first_name: Joi.string().trim().optional(),
    last_name: Joi.string().trim().optional(),
    email: Joi.string().email().trim().optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    permission: Joi.object().optional()
});
export const activeInActiveSubAdminSchema = Joi.object({
    id: Joi.string().id().required(),
    isActive: Joi.boolean().required()
});
