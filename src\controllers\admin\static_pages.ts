import { NextFunction, Request, Response } from 'express';
import models from '../../models';
import { adminRole, httpStatusCodes } from '../../utils/constants';
import { logger } from '../../utils/logger';
import { Op } from 'sequelize';

class StaticPages {
    /*
        --------------------------------------------------------------------------------
        Static Pages functions
    */

    /**
     *  @api {post} /v1/auth/admin/static-page
     *  @apiName addStaticPages
     *  @apiGroup StaticPages
     *
     *  @apiSuccess {Object} StaticPages
     */
    async addStaticPages(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!AddStaticPages function start!!!!!');
        try {
            const { slug, title, content } = req.body;

            const result: any = await models.static_pages.findOne({
                where: { slug: slug.toString().toLowerCase().trim() }
            });

            if (result) {
                throw new Error(`already exists!!!`);
            }

            const staticPage: any = await models.static_pages.create({
                slug: slug.toString().toLowerCase().trim(),
                title,
                content
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Static page created successfully!',
                data: staticPage
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/static-page
     * @apiName listStaticPages
     * @apiGroup StaticPages
     *
     *
     * @apiSuccess {Object} StaticPages.
     */
    async listStaticPages(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!listStaticPages function start!!!!!');
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            const isActive = req.query.is_active;

            const q = req.query.q;
            const keyword = `%${q}%`;
            let whereClause: any = {};

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (q) {
                whereClause = {
                    [Op.or]: [
                        { slug: { [Op.iLike]: keyword } },
                        { title: { [Op.iLike]: keyword } },
                        { content: { [Op.iLike]: keyword } }
                    ]
                };
            }
            if (isActive) {
                whereClause.is_active = isActive;
            }

            const { rows, count } = await models.static_pages.findAndCountAll({
                where: whereClause,
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Static pages successfully listed',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/auth/admin/static-page
     *  @apiName updateStaticPages
     *  @apiGroup StaticPages
     *
     *  @apiSuccess {Object} StaticPages
     */
    async updateStaticPages(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!UpdateStaticPages function start!!!!!');
        try {
            const dataObject: any = {};
            const role = req[`role`];
            const id = req.body.id;
            const { slug, title, content } = req.body;

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('Unauthorized Access');
            }

            const result: any = await models.static_pages.findOne({ where: { id } });

            if (!result) {
                throw new Error(`Static page Not found!!!`);
            }

            if (slug) {
                dataObject.slug = slug.toString().toLowerCase().trim();
            }

            if (title) {
                dataObject.title = title;
            }

            if (content) {
                dataObject.content = content;
            }

            const staticPage: any = await models.static_pages.update(dataObject, {
                where: { id },
                returning: true,
                plain: true
            });

            const updatedStaticPage = JSON.parse(JSON.stringify(staticPage[1]));

            delete updatedStaticPage.password;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Static page updated successfully!',
                data: updatedStaticPage
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/admin/static-page-details
     *  @apiName getStaticPages
     *  @apiGroup StaticPages
     *
     *  @apiSuccess {Object} StaticPages
     */
    async getStaticPage(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!UpdateStaticPages function start!!!!!');
        try {
            const slug: any = req.query.slug;

            if (!slug) {
                throw new Error(`slug required!!`);
            }

            const staticPage: any = await models.static_pages.findOne({
                where: { slug: slug.toString().toLowerCase().trim() }
            });

            if (!staticPage) {
                throw new Error(`Not Found!!`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Static page listed successfully!',
                data: staticPage
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {delete} /v1/auth/admin/static-page
     *  @apiName deleteStaticPages
     *  @apiGroup StaticPages
     *
     *  @apiSuccess {Object} StaticPages
     */
    async deleteStaticPages(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!DeleteStaticPages function start!!!!!');
        try {
            const id = req.query.id;

            if (!id) {
                throw new Error('id required!!');
            }

            const staticPage = await models.static_pages.findOne({
                where: { id, _deleted: false }
            });

            if (!staticPage) {
                throw new Error('Static page not found');
            }

            await models.static_pages.update(
                {
                    _deleted: true
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Static pages deleted successfully!'
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/static-page-status
     *  @apiName changeStaticPageStatus
     *  @apiGroup StaticPages
     *
     *  @apiSuccess {Object} StaticPages
     */
    async changeStaticPageStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_active } = req.body;

            const staticPage = await models.static_pages.findOne({
                where: { id, _deleted: false }
            });

            if (!staticPage) {
                throw new Error('Static page not found');
            }

            await models.static_pages.update(
                {
                    is_active
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Updated Static pages status'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new StaticPages();
