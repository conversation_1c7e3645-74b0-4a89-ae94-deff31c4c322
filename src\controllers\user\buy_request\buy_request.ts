import { NextFunction, Request, Response } from 'express';
import {
    NotificationType,
    PaymentMode,
    adminRole,
    buyRequestStatus,
    buyRequestType,
    getNotificationType,
    httpStatusCodes,
    isStocksAvailable,
    makeUniqueKey,
    paymentType,
    stockStatus,
    vendorRejectedHoldRequestStatus
} from '../../../utils/constants';
import models, { sequelize } from '../../../models';
import { Op, Sequelize } from 'sequelize';
import { BuyRequestAttributes } from '../../../models/buy_requests';
import { VendorAttributes } from '../../../models/vendors';
import { StockAttributes } from '../../../models/stocks';
import { UserAttributes } from '../../../models/users';
import { manageAcceptedStatus, manageCanceledStatus, manageUpdatedAndPending } from './update_buy_request';
import { logger } from '../../../utils/logger';
import authorizePayment from '../authorize_payment/authorize_payment';
import userNotification from '../user_notifications/user_notification';
import { FilterOrderBy, getTestUserIds } from '../../../utils/filter_order';

class BuyRequest {
    /**
     * @api {get} /v1/auth/vendor-buy-request
     * @apiName listVendorBuyRequest
     * @apiGroup VendorBuyRequest
     *
     *
     * @apiSuccess {Object} VendorBuyRequest
     */
    async listVendorBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling list vendor buy request!!!!');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const reqId = req[`id`];
            const conditions: any[] = [];

            /// list rejected stocks with reasons
            if (status === buyRequestStatus.cancelled) {
                const { rows, count } = await models.vendor_rejected_hold_requests.findAndCountAll({
                    where: { [Op.and]: [{ vendor_id: reqId }, { status: vendorRejectedHoldRequestStatus.declined }] },
                    include: [
                        {
                            model: models.stocks,
                            attributes: [
                                'id',
                                'stock_id',
                                'status',
                                'weight',
                                'color',
                                'clarity',
                                'shape',
                                'cut',
                                'polish',
                                'symmetry',
                                'fluorescence_intensity',
                                'discounts',
                                'discounts_ori',
                                'price_per_caret',
                                'price_per_caret_ori',
                                'final_price',
                                'final_price_ori',
                                'admin_id',
                                'vendor_id',
                                'sku_number',
                                'stock_margin'
                            ]
                        }
                    ],
                    order: [['updatedAt', 'DESC']],
                    offset: skip,
                    limit
                });

                /// update approved margin
                const vendorBuyRequestsFiltered = JSON.parse(JSON.stringify(rows))
                    ?.filter((row: any) => row?.stock)
                    ?.map((vendorBuyRequest: any) => ({
                        ...vendorBuyRequest,
                        stock: {
                            ...(vendorBuyRequest?.stock ?? {}),
                            ...(vendorBuyRequest?.approved_margin?.stock ?? {})
                        }
                    }));

                res.send({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: `Buy request listed successfully`,
                    data: vendorBuyRequestsFiltered,
                    count: vendorBuyRequestsFiltered?.length
                });

                return;
            }

            /// list by status
            if (status) {
                conditions.push({ status: status.toString().toUpperCase() });
            }

            /// list buy request where vendors stock included
            conditions.push({ vendor_ids: { [Op.contains]: [reqId] } });

            conditions.push({ type: 'NORMAL' });

            /// list margin approved buy request
            /// conditions.push({ is_margin_approved: true });

            /// fetch buy requests
            const buyRequest = await models.buy_requests.findAndCountAll({
                where: { [Op.and]: conditions },
                order: [['updatedAt', 'DESC']],
                offset: skip,
                limit
            });

            /// show only logged in vendors stock
            buyRequest.rows = buyRequest.rows.map((row: any) =>
                row.stock_ids
                    .map((stock: any) => ({ ...stock, buy_request_id: row.id }))
                    .filter((stock: any) => stock.vendor_id === reqId)
                    .filter((stock: any) =>
                        status === buyRequestStatus.pending ? stock?.is_action_taken === false : true
                    )
            );

            buyRequest.rows = buyRequest.rows.reduce(
                (accumulator: any, currentArray: any) => accumulator.concat(currentArray),
                []
            );

            const stockIds = buyRequest.rows.map((item: any) => item.stock_id);

            /// fetch stocks
            const stocks: StockAttributes[] = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'discounts_ori',
                    'price_per_caret',
                    'price_per_caret_ori',
                    'final_price',
                    'final_price_ori',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type',
                    'admin_id',
                    'vendor_id',
                    'sku_number',
                    'stock_margin'
                ]
            });

            /// list margin approved hold requests
            const vendorBuyRequests = buyRequest.rows
                .map((item: any) => {
                    const stock = stocks?.find((stockData: any) => stockData?.id === item?.stock_id);
                    if (stock) {
                        return { ...item, stock: { ...JSON.parse(JSON.stringify(stock)), ...item?.stock } };
                    }
                })
                .filter((row: any) => row?.stock && row?.is_margin_approved !== false);

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Buy request listed successfully`,
                data: vendorBuyRequests,
                count: vendorBuyRequests?.length
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/vendor-stock-approval
     * @apiName VendorStockApproval
     * @apiGroup VendorBuyRequest
     *
     *
     * @apiSuccess {Object} VendorBuyRequest
     */
    async vendorStockApproval(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling vendor stock approval!!!!');
        /// transactions
        const transaction = await sequelize.transaction();

        try {
            const { buy_request_id, stock_id, vendor_id, reject_reason } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];
            const is_available = (req.body?.is_available ?? false).toString().toLowerCase() === 'true';

            /// double check vendors stock
            if (role === adminRole.vendor) {
                if (reqId !== vendor_id) {
                    throw Error(`Unauthorized access`);
                }
            }

            /// fetch buy requests
            const buyRequest = await models.buy_requests.findOne({ where: { id: buy_request_id } });

            if (!buyRequest) {
                throw Error(`Buy request not found`);
            }

            /// already accepted or cancelled
            if (
                [buyRequestStatus.accepted, buyRequestStatus.autoCanceled, buyRequestStatus.cancelled].includes(
                    buyRequest.status
                )
            ) {
                throw new Error(`This buy request is already ${buyRequest.status}.`);
            }

            if (!is_available && !reject_reason) {
                throw new Error(`reject_reason required.`);
            }

            /// update is_action_taken
            buyRequest.stock_ids = buyRequest.stock_ids.map((item: any) => {
                const data =
                    item.stock_id === stock_id
                        ? {
                            ...item,
                            is_action_taken: true,
                            is_admin_action_taken: [adminRole.superAdmin, adminRole.subAdmin].includes(role),
                            is_available
                        }
                        : item;
                return data;
            });

            logger.info('Updating stock_ids into buy requests!!!!');

            /// update buy request
            await models.buy_requests.update(
                { stock_ids: buyRequest.stock_ids },
                { where: { id: buyRequest.id }, transaction }
            );

            logger.info('Creating entries in vendor rejected hold requests table for rejected stocks');
            /// create vendor rejected hold rejected
            await models.vendor_rejected_hold_requests.create(
                {
                    buy_request_id: buyRequest.id,
                    vendor_id,
                    stock_id,
                    approved_margin: buyRequest?.stock_ids?.find((item: any) => item?.stock_id === stock_id),
                    reject_reason,
                    rejected_by: reqId,
                    status: is_available
                        ? vendorRejectedHoldRequestStatus.accepted
                        : vendorRejectedHoldRequestStatus.declined
                },
                { transaction }
            );

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `stock status updated successfully`
            });

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/buy-request
     * @apiName listBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async listBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling list buy request');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const reqId = req[`id`];
            const role = req[`role`];
            const conditions: any[] = [];
            const is_sandbox = String(req.query.is_sandbox).toLowerCase() === 'true';
            const filter_by = req.query.filter_by;
            const filter_keyword = req.query.filter_keyword;
            const isMarginApproved = req.query.is_margin_approved;

            /// list by status
            if (status) {
                const statusData = status
                    .toString()
                    .split(',')
                    .map((item: string) => item.toLocaleUpperCase());

                if (statusData.length === 1) {
                    conditions.push({ status: statusData[0] });
                } else {
                    conditions.push({
                        status: {
                            [Op.in]: statusData
                        }
                    });
                }
            }

            /// list users buy request
            if (role === adminRole.user) {
                conditions.push({ user_id: reqId });
            }

            /// list buy request where vendors stock included
            if (role === adminRole.vendor) {
                conditions.push({ vendor_ids: { [Op.contains]: [reqId] } });
            }

            /// list normal buy requests if not user
            // if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
            //     conditions.push({ type: 'NORMAL' });
            // } else if (adminRole.user === role) {
            //     conditions.push({
            //         [Op.or]: [{ type: 'OFFER', status: buyRequestStatus.accepted }, { type: 'NORMAL' }]
            //     });
            // }

            /// list normal buy request for only
            conditions.push({ type: 'NORMAL' });

            if (filter_by && filter_keyword) {
                /// add filter conditions to buy request
                await BuyRequest.buyRequestFilters(filter_by, filter_keyword, conditions, is_sandbox);
            }

            /// sandbox conditions are for admin
            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                /// fetch only is_test_user buy requests for sandbox
                /// get test user ids from getTestUserIds function
                const testUserIds = await getTestUserIds();

                /// add test user ids into conditions
                conditions.push({ user_id: is_sandbox ? { [Op.in]: testUserIds } : { [Op.notIn]: testUserIds } });
            }

            /// add margin approved condition default false
            if (isMarginApproved) {
                /// check if is_margin_approved is true or false
                conditions.push({ is_margin_approved: isMarginApproved });

                /// vendor_ids should not be empty
                conditions.push(Sequelize.literal(`array_length("vendor_ids", 1) > 0`));
            }

            /// fetch buy requests
            const buyRequest = await models.buy_requests.findAndCountAll({
                where: { [Op.and]: conditions },
                order: [['updatedAt', 'DESC']],
                offset: skip,
                limit
            });

            /// show only logged in vendors stock
            if (role === adminRole.vendor) {
                buyRequest.rows = buyRequest.rows.map((row: any) => ({
                    ...JSON.parse(JSON.stringify(row)),
                    stock_ids: row.stock_ids.filter((stock: any) => stock.vendor_id === reqId)
                }));
            }

            /// extract user ids
            const userIds = buyRequest.rows.map((row: any) => row.user_id);

            /// fetch users
            const users = await models.users.findAll({
                where: { id: { [Op.in]: userIds } },
                attributes: ['id', 'first_name', 'last_name', 'email', 'legal_registered_name']
            });

            /// extract all stock ids
            const stockIds = [
                ...new Set(
                    [].concat(...buyRequest.rows.map((row: any) => row.stock_ids.map((item: any) => item.stock_id)))
                )
            ];

            /// fetch stock details
            const stocks = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'discounts',
                    'discounts_ori',
                    'price_per_caret',
                    'price_per_caret_ori',
                    'final_price',
                    'final_price_ori',
                    'weight',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type',
                    'admin_id',
                    'vendor_id',
                    'sku_number',
                    'stock_margin'
                ]
            });

            const buyRequestResults: any[] = [];

            for (const buyRequestItem of buyRequest.rows) {
                if (buyRequestItem?.type === buyRequestType.offer) {
                    /// fetch offers for buy request
                    const stockOffers: any = await models.stock_offers.findAll({
                        where: {
                            stock_id: { [Op.in]: buyRequestItem.stock_ids.map((offerItem: any) => offerItem.stock_id) }
                        }
                    });

                    /// set offer price for stock
                    for (const stockOffer of stockOffers) {
                        const stock: any = stocks.find((stockItem: any) => stockItem.id === stockOffer.stock_id);

                        if (stock) {
                            /// set offer price
                            stock.final_price = stockOffer?.offer_price;
                            stock.price_per_caret = stockOffer?.offer_price / (stock?.weight ?? 0);
                        }
                    }
                }

                const totalCTS = stocks.length
                    ? buyRequestItem.stock_ids
                          .filter((item: any) => isStocksAvailable(item))
                          .map((item: any) => item.stock_id)
                          .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.weight ?? 0)
                          .reduce((a: any, b: any) => a + b, 0)
                    : 0;

                const totalAmount = stocks.length
                    ? buyRequestItem.stock_ids
                          .filter((item: any) => isStocksAvailable(item))
                          .map((item: any) => item.stock_id)
                          .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price ?? 0)
                          .reduce((a: any, b: any) => a + b, 0)
                    : 0;

                buyRequestResults.push({
                    ...JSON.parse(JSON.stringify(buyRequestItem)),
                    total_cts: totalCTS,
                    amount: parseFloat(totalAmount),
                    total_amount: parseFloat(totalAmount),
                    grand_total: parseFloat(
                        (
                            parseFloat(totalAmount) +
                            parseFloat(buyRequestItem.shipment_price) +
                            parseFloat(buyRequestItem.tax_price)
                        ).toFixed(2)
                    ),
                    price_per_carat: parseFloat((parseFloat(totalAmount) / parseFloat(totalCTS)).toFixed(2)),
                    user: users.find((user: any) => user.id === buyRequestItem.user_id)
                });
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Buy request listed successfully`,
                data: buyRequestResults,
                count: buyRequest.count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/buy-request
     * @apiName createBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    // deprecated using unified order instead
    async createBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling create buy request!!!');
        const transaction = await sequelize.transaction();
        try {
            const userId = req[`id`];
            const role = req[`role`];
            const userData: any = req[`user`];

            const newBody = {
                ...req.body,
                stock_ids: req.body.stock_ids.map((stockItem: any) => ({
                    ...stockItem,
                    is_available: String(stockItem.is_available) === 'true',
                    is_action_taken: String(stockItem.is_action_taken) === 'true'
                }))
            };

            const {
                card_number,
                card_holder_name,
                exp_date,
                cvv,
                payment_mode,
                stock_ids,
                total_amount,
                shipment_id,
                shipment_price,
                apple_pay_response
            } = newBody;

            /// create buy req by user ony
            if (role !== adminRole.user) {
                throw new Error('Unauthorized access');
            }

            /// check user is blocked
            if (userData.is_blocked) {
                throw new Error('Please contact admin. User is blocked!!!');
            }

            const taxPrice: number = userData?.is_verified
                ? 0
                : parseFloat(total_amount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

            const grandTotal = parseFloat(total_amount) + parseFloat(shipment_price) + taxPrice;

            if (payment_mode === PaymentMode.creditLimit) {
                logger.info(`Calculating available credit:::`);
                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: userId
                    }
                });

                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: userId
                    }
                });

                const availableCreditLimit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                if (parseFloat(grandTotal.toFixed(2)) > availableCreditLimit) {
                    throw new Error('Not enough credit');
                }
            }

            const availableStocks = await models.stocks.findAll({
                where: { id: { [Op.in]: stock_ids.map((stockIds: any) => stockIds.stock_id) }, status: 'AVAILABLE' }
            });

            if (availableStocks.length !== stock_ids.length) {
                throw new Error('Please refresh stock list');
            }

            // extract vendors ids if includes vendor stocks
            const vendor_ids = [
                ...new Set(
                    stock_ids
                        .map((item: any) => item.vendor_id)
                        .filter((vendorId: any) => vendorId !== null && vendorId !== undefined)
                )
            ];

            const vendorsList = await models.vendors.findAll({
                where: { id: { [Op.in]: vendor_ids }, is_blacklisted: true }
            });

            /// check vendors blacklisted
            if (vendorsList && vendorsList.length) {
                throw new Error('Please refresh your stock list');
            }

            /// generate order code
            const orderCode = makeUniqueKey(10);

            logger.info('Creating buy request!!!!');

            /// create buy request with PENDING status
            /// is_available true
            const buyRequest = await models.buy_requests.create(
                {
                    ...req.body,
                    user_id: userId,
                    stock_ids: stock_ids.map((item: any) => ({
                        ...item,
                        is_available: true,
                        is_action_taken: item.vendor_id ? false : true,
                        is_margin_approved: item.vendor_id ? false : true
                    })),
                    vendor_ids,
                    status: 'PENDING',
                    updated_by_id: userId,
                    order_code: orderCode,
                    amount: total_amount,
                    shipment_id,
                    shipment_price,
                    tax_price: parseFloat(taxPrice.toFixed(2)),
                    grand_total: grandTotal
                },
                { transaction }
            );

            if (!buyRequest) {
                throw new Error(`Something went wrong!!`);
            }

            ////////////////// MAKE PAYMENT //////////////////////////
            if (payment_mode === PaymentMode.creditCard) {
                logger.info('Calling authorize payment!!!!');

                /// check for customer profile id exists
                if (!req[`user`].customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }

                /// authorize credit card
                await authorizePayment.authorizeCreditCard(
                    false,
                    card_number,
                    card_holder_name,
                    exp_date,
                    cvv,
                    userId,
                    buyRequest,
                    transaction,
                    buyRequestStatus.pending
                );
            }
            /// apple pay
            else if (payment_mode === PaymentMode.applePay) {
                /// check for customer profile id exists
                if (!req[`user`].customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }
                /// ApplePay
                await authorizePayment.createApplePayTransaction(
                    buyRequest.id,
                    apple_pay_response,
                    userId,
                    transaction
                );
            }
            /// credit limit
            else if (payment_mode === PaymentMode.creditLimit) {
                logger.info('Debiting credit limit!!!!');
                /// debit credit limit
                await models.credit_histories.create(
                    {
                        user_id: buyRequest.user_id,
                        credit: parseFloat(parseFloat(buyRequest.grand_total).toFixed(2)),
                        type: 'CREATE-BUY-REQUEST',
                        transaction_type: 'DEBIT',
                        buy_request_id: buyRequest.id
                    },
                    { transaction }
                );
            }

            const updatedBuyRequest: any = await models.buy_requests.findOne({
                where: { id: buyRequest.id },
                transaction
            });

            logger.info('Calling create buy request trails!!!');

            /// add to trail
            await models.buy_request_trails.create(
                {
                    user_id: userId,
                    buy_request_id: updatedBuyRequest.id,
                    status: updatedBuyRequest.status,
                    updated_by_id: userId,
                    payload: JSON.stringify(updatedBuyRequest)
                },
                { transaction }
            );

            logger.info('Putting stocks on HOLD!!!!');

            /// change stock status to HOLD
            await models.stocks.update(
                { status: 'HOLD' },
                { where: { id: { [Op.in]: stock_ids.map((item: any) => item.stock_id) } }, transaction }
            );

            /// update user buy_request_count
            await models.users.update(
                { buy_request_count: sequelize.literal('buy_request_count + 1') },
                { where: { id: userId }, transaction }
            );

            /// update vendor buy_request_count
            if (vendor_ids) {
                await models.vendors.update(
                    { buy_request_count: sequelize.literal('buy_request_count + 1') },
                    { where: { id: { [Op.in]: vendor_ids } }, transaction }
                );
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Buy request created successfully`,
                data: buyRequest
            });

            if (updatedBuyRequest?.stock_ids?.length) {
                /// send notification for stock status change
                try {
                    userNotification.stockStatusChangeNotification(
                        updatedBuyRequest.stock_ids.map((item: any) => item.stock_id)
                    );
                } catch (error: any) {
                    logger.error(`!!!error sending notification for stock status!!!!! ${error}`);
                }
            }

            /// notify admin/vendor
            try {
                userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                    NotificationType.buyRequestCreated,
                    updatedBuyRequest.id
                );
            } catch (error: any) {
                logger.error(`!!!error sending notification to admin and user!!!!! ${error}`);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/buy-request
     * @apiName updateBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async updateBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!! updating buy request');
        const transaction = await sequelize.transaction();
        try {
            const reqId = req[`id`];
            const role = req[`role`];
            let result: boolean | undefined;
            const newBody = {
                ...req.body,
                stock_ids: req.body.stock_ids.map((stockItem: any) => ({
                    ...stockItem,
                    is_available: String(stockItem.is_available) === 'true',
                    is_action_taken: String(stockItem.is_action_taken) === 'true',
                    suggested_stock: stockItem?.suggested_stock
                        ? {
                              ...stockItem.suggested_stock,
                              is_available: String(stockItem.suggested_stock.is_available) === 'true',
                              is_action_taken: String(stockItem.suggested_stock.is_action_taken) === 'true'
                          }
                        : undefined
                }))
            };
            const { status, reject_reason, id, card_number, card_holder_name, exp_date, cvv, apple_pay_response } =
                newBody;

            let stock_ids = newBody.stock_ids;

            /// check user is blocked
            if (role === adminRole.user) {
                if (req[`user`].is_blocked) {
                    throw new Error('Please contact admin. User is blocked!!!');
                }
            }

            /// vendor can not update
            if (role === adminRole.vendor) {
                throw new Error(`Unauthorized access.`);
            }

            /// users status can be pending and cancelled only
            if (
                role === adminRole.user &&
                ![buyRequestStatus.pending, buyRequestStatus.cancelled, buyRequestStatus.accepted].includes(status)
            ) {
                throw new Error(`Invalid status ${status}.`);
            }

            /// admins status can not be pending
            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role) && status === buyRequestStatus.pending) {
                throw new Error(`Invalid status ${status}.`);
            }

            /// check if buy request exists
            const buyRequestResult: any = await models.buy_requests.findOne({ where: { id } });

            const buyRequest = JSON.parse(JSON.stringify(buyRequestResult));

            if (!buyRequest) {
                throw new Error(`No such buy request found.`);
            }

            if (role === adminRole.user) {
                if (reqId !== buyRequest?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            /// already accepted or cancelled
            if (
                [buyRequestStatus.accepted, buyRequestStatus.autoCanceled, buyRequestStatus.cancelled].includes(
                    buyRequest.status
                )
            ) {
                throw new Error('This buy request is already accepted or cancelled.');
            }

            // /// auto cancelled if no stock_ids or stocks not available
            // if (!stock_ids) {
            //     await manageAutoCanceledStatus(id, reqId, buyRequest, transaction);
            // }

            // /// auto cancelled if no stock_ids or stocks not available
            // if (stock_ids) {
            //     if (
            //         stock_ids.length === 0 ||
            //         stock_ids.filter((stockId: any) => isStocksAvailable(stockId)).length === 0
            //     ) {
            //         await manageAutoCanceledStatus(id, reqId, buyRequest, transaction);
            //     }
            // }

            /// update margin approval fields into request stock_ids from db if not provided in request
            stock_ids = stock_ids?.map((item: any) => {
                const stockId = buyRequest?.stock_ids?.find((stock: any) => stock?.stock_id === item?.stock_id);
                if (stockId) {
                    return {
                        ...stockId,
                        ...item
                    };
                }
                return item;
            });

            logger.info(`Calling function for status: ${status}`);

            /// update buy request when pending or updated
            if ([buyRequestStatus.pending, buyRequestStatus.updated].includes(status)) {
                /// place order by user when capture failed
                buyRequest.card_number = card_number;
                buyRequest.card_holder_name = card_holder_name;
                buyRequest.exp_date = exp_date;
                buyRequest.stock_ids = stock_ids;

                result = await manageUpdatedAndPending(
                    buyRequest,
                    id,
                    role,
                    reqId,
                    status,
                    buyRequest.stock_ids,
                    reject_reason,
                    apple_pay_response,
                    transaction
                );
            }

            /// order accepted
            else if (status === buyRequestStatus.accepted) {
                /// card details required for user in case capture failed
                if (role === adminRole.user) {
                    ///
                    if (!buyRequest.is_capture_failed) {
                        throw new Error(`unauthorized access!!!`);
                    }

                    if (!card_number && !card_holder_name && !exp_date && !cvv) {
                        throw new Error('please provide card details!!!.');
                    }

                    /// add card data into buy request object
                    buyRequest.card_number = card_number;
                    buyRequest.card_holder_name = card_holder_name;
                    buyRequest.exp_date = exp_date;
                    buyRequest.cvv = cvv;
                }

                await manageAcceptedStatus(req, buyRequest, stock_ids, reqId, transaction);
            }

            /// order canceled
            else if (status === buyRequestStatus.cancelled) {
                await manageCanceledStatus(buyRequest, stock_ids, transaction);
            }

            /// update buy request status
            /// In case of auto canceled status will not be updated from here it will be updated within manageUpdatedAndPending function
            /// In manageUpdatedAndPending function if buy request is not auto cancelling then its return nothing so result will be undefined in that case and status will be updated from here
            if (result === undefined) {
                await models.buy_requests.update({ status, reject_reason }, { where: { id }, transaction });
            }

            /// fetch updated buy request payload
            const updatedBuyRequest = await models.buy_requests.findOne({ where: { id }, transaction });

            logger.info('Creating buy request trails!!!!!');

            /// add to trail
            await models.buy_request_trails.create(
                {
                    user_id: updatedBuyRequest.user_id,
                    buy_request_id: updatedBuyRequest.id,
                    status: updatedBuyRequest.status,
                    updated_by_id: reqId,
                    payload: JSON.stringify(updatedBuyRequest)
                },
                { transaction }
            );

            /// when buy request cancelled then create vendor rejected hold rejected entry for vendors stocks
            if ([buyRequestStatus.cancelled].includes(updatedBuyRequest.status)) {
                if (updatedBuyRequest.vendor_ids.length) {
                    /// create vendor rejected hold rejected entry for vendors stocks
                    const vendorRejectedHoldRequests = updatedBuyRequest.stock_ids
                        .filter((stockIds: any) => stockIds?.vendor_id)
                        .map((item: any) => {
                            return {
                                buy_request_id: updatedBuyRequest.id,
                                vendor_id: item?.vendor_id,
                                stock_id: item?.stock_id,
                                approved_margin: updatedBuyRequest?.stock_ids.find(
                                    (stockItem: any) => stockItem?.stock_id === item?.stock_id
                                ),
                                reject_reason: updatedBuyRequest?.reject_reason ?? 'Cancelled by admin',
                                rejected_by: reqId
                            };
                        });

                    /// bulk crate vendors declined stocks
                    await models.vendor_rejected_hold_requests.bulkCreate(vendorRejectedHoldRequests, { transaction });
                }
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: result === false ? `Buy request canceled successfully` : `Buy request updated successfully`
            });

            /// notifications
            try {
                if (
                    status === buyRequestStatus.updated ||
                    status === buyRequestStatus.accepted ||
                    status === buyRequestStatus.cancelled
                ) {
                    try {
                        if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                            /// send notifications to users when buy request updated
                            userNotification.sendNotification(getNotificationType(status), null, updatedBuyRequest);

                            if (status === buyRequestStatus.accepted) {
                                /// send notification to vendor regarding raise invoice
                                userNotification.sendRaiseInvoiceEmailToVendor(
                                    NotificationType.raiseInvoice,
                                    buyRequest?.id
                                );

                                /// send order created notification to admin
                                userNotification.sendOderCreatedNotifications(
                                    NotificationType.orderCreated,
                                    buyRequest?.user_id,
                                    null,
                                    buyRequest?.id
                                );
                            }
                        }
                    } catch (error: any) {
                        logger.error(
                            `update buy request notification to user ${status} error ${JSON.stringify(error, null, 2)}`
                        );
                    }

                    /// buy request cancelled notifications to vendor and admin
                    try {
                        if (
                            [buyRequestStatus.cancelled, buyRequestStatus.autoCanceled].includes(
                                updatedBuyRequest.status
                            )
                        ) {
                            userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                                NotificationType.buyRequestRejected,
                                updatedBuyRequest.id
                            );
                        }
                    } catch (error: any) {
                        logger.error(error);
                    }

                    ////
                } else if (status === buyRequestStatus.pending) {
                    try {
                        /// notify admin/vendor
                        userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                            NotificationType.buyRequestUpdated,
                            updatedBuyRequest.id
                        );
                    } catch (error: any) {
                        logger.error(
                            `update buy request notification to user ${status} error ${JSON.stringify(error, null, 2)}`
                        );
                    }
                }

                /// cancelled and auto-cancelled
                if (buyRequestStatus.cancelled === status || result === false) {
                    try {
                        if (buyRequest.payment_mode === PaymentMode.creditLimit) {
                            /// send notification when credit added
                            userNotification.sendCreditLimitNotifications(
                                NotificationType.creditLimitAdded,
                                buyRequest.user_id
                            );
                        }
                    } catch (error: any) {
                        logger.error(`credit limit added notification to user error ${JSON.stringify(error, null, 2)}`);
                    }
                }

                try {
                    const stockStatusChangeIds: any[] = [];

                    if (stock_ids?.length) {
                        for (const stockId of stock_ids) {
                            if (stockId?.stock_id) {
                                stockStatusChangeIds.push(stockId?.stock_id);
                            }
                            if (stockId?.suggested_stock) {
                                if (stockId?.suggested_stock?.stock_id) {
                                    stockStatusChangeIds.push(stockId?.suggested_stock?.stock_id);
                                }
                            }
                        }
                    }

                    if (buyRequest?.stock_ids?.length) {
                        for (const stockId of buyRequest?.stock_ids) {
                            if (stockId?.stock_id) {
                                stockStatusChangeIds.push(stockId?.stock_id);
                            }
                            if (stockId?.suggested_stock) {
                                if (stockId?.suggested_stock?.stock_id) {
                                    stockStatusChangeIds.push(stockId?.suggested_stock?.stock_id);
                                }
                            }
                        }
                    }

                    if (stockStatusChangeIds.length) {
                        const stockStatusChangeUniqueIds = [...new Set(stockStatusChangeIds)];

                        /// send notification for stock status change
                        userNotification.stockStatusChangeNotification(stockStatusChangeUniqueIds);
                    }
                } catch (error: any) {
                    logger.error(`stock status change notification to user error ${JSON.stringify(error, null, 2)}`);
                }

                //// catch
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/buy-request-details
     * @apiName listBuyRequestDetails
     * @apiGroup BuyRequestDetails
     *
     *
     * @apiSuccess {Object} BuyRequestDetails
     */
    async buyRequestDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling buy request details!!!!');
        try {
            const id = req.query.id;
            const role = req[`role`];
            const reqId = req[`id`];

            /// fetch buy request
            const buyRequest: BuyRequestAttributes = await models.buy_requests.findOne({ where: { id } });

            if (!buyRequest) {
                throw Error(`Buy request not found!!!`);
            }

            if (role === adminRole.user) {
                if (reqId !== buyRequest?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            /// only vendors stock
            if (role === adminRole.vendor) {
                buyRequest.stock_ids = buyRequest.stock_ids.filter((item: any) => item.vendor_id === reqId);
            }

            /// fetch vendors
            if (buyRequest.vendor_ids && buyRequest.vendor_ids.length) {
                const vendors: VendorAttributes[] = await models.vendors.findAll({
                    where: { id: { [Op.in]: buyRequest.vendor_ids } },
                    attributes: ['id', 'first_name', 'last_name', 'email']
                });
                if (vendors.length) {
                    /// update vendor details
                    buyRequest.stock_ids = buyRequest.stock_ids.map((item: any) => ({
                        ...item,
                        vendor: vendors.find((vendor: any) => vendor.id === item.vendor_id)
                    }));
                }
            }

            /// extract stockIds
            const stockIds = buyRequest.stock_ids.map((item: any) => item.stock_id);

            /// suggested stock ids
            const suggestedStockIds = buyRequest.stock_ids.map((item: any) => {
                if (item.suggested_stock) {
                    return item.suggested_stock.stock_id;
                }
            });

            /// fetch stocks
            const stocks: StockAttributes[] = await models.stocks.findAll({
                where: { id: { [Op.in]: [...stockIds, ...suggestedStockIds] } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'discounts_ori',
                    'price_per_caret',
                    'price_per_caret_ori',
                    'final_price',
                    'final_price_ori',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type',
                    'admin_id',
                    'vendor_id',
                    'admin_id',
                    'vendor_id',
                    'sku_number',
                    'stock_margin'
                ]
            });

            /// replace stock final price with offer price
            if (buyRequest.type === buyRequestType.offer) {
                for (const stock of stocks) {
                    const offer: any = await models.stock_offers.findOne({ where: { stock_id: stock.id } });

                    /// set offer price for user
                    stock.final_price = offer?.offer_price;
                    stock.price_per_caret = offer?.offer_price / (stock?.weight ?? 0);
                }
            }

            /// fetch user data
            const userData: UserAttributes = await models.users.findOne({
                where: { id: buyRequest.user_id },
                attributes: ['id', 'first_name', 'last_name', 'email', 'legal_registered_name', 'is_verified']
            });

            /// total amount
            const totalAmount = buyRequest.stock_ids
                .filter((item: any) => isStocksAvailable(item))
                .map((item: any) => item.stock_id)
                .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price ?? 0)
                .reduce((a: any, b: any) => a + b, 0);

            /// total cts
            const totalCTS = buyRequest.stock_ids
                .filter((item: any) => isStocksAvailable(item))
                .map((item: any) => item.stock_id)
                .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.weight ?? 0)
                .reduce((a: any, b: any) => a + b, 0);

            /// update stock details user data
            const buyRequestData = {
                ...JSON.parse(JSON.stringify(buyRequest)),
                user: userData,
                stock_ids: buyRequest.stock_ids.map((item: any) => {
                    if (item.suggested_stock) {
                        return {
                            ...item,
                            stock: stocks.length ? stocks.find((stockData: any) => stockData.id === item.stock_id) : {},
                            suggested_stock: {
                                ...item.suggested_stock,
                                stock: stocks.length
                                    ? stocks.find((stockData: any) => stockData.id === item.suggested_stock.stock_id)
                                    : {}
                            }
                        };
                    }

                    const stock: any = stocks?.find((stockData: any) => stockData?.id === item?.stock_id);
                    return {
                        vendor_margin: 0,
                        ...item,
                        stock: { ...JSON.parse(JSON.stringify(stock ?? {})), ...item.stock }
                    };
                }),
                total_cts: totalCTS,
                total_amount: parseFloat(totalAmount),
                amount: parseFloat(totalAmount),
                grand_total: parseFloat(totalAmount) + buyRequest.shipment_price + buyRequest.tax_price,
                price_per_carat: parseFloat((parseFloat(totalAmount) / parseFloat(totalCTS)).toFixed(2)),
                tax: userData?.is_verified ?? false ? 0 : parseFloat(process.env.TAX_PERCENT || '0')
            };

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Buy request details listed successfully`,
                data: buyRequestData
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/new-buy-request
     * @apiName createNewBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async createNewBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling function create new buy request only!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const reqId = req[`id`];
            const role = req[`role`];

            const newBody = {
                ...req.body,
                stock_ids: req.body.stock_ids.map((stockItem: any) => ({
                    ...stockItem,
                    is_available: String(stockItem.is_available) === 'true',
                    is_action_taken: String(stockItem.is_action_taken) === 'true'
                }))
            };

            const { buy_request_id } = newBody;

            let stock_ids = newBody.stock_ids;

            /// admin can not create new buy request
            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error(`Unauthorized access.`);
            }

            const buyRequest: any = await models.buy_requests.findOne({ where: { id: buy_request_id } });

            if (!buyRequest) {
                throw new Error(`No such buy request found.`);
            }

            /// check authorized amount
            if (buyRequest?.authorized_amount) {
                throw new Error(`Can not create new order with zero authorized amount.`);
            }

            /// can not create order if there is only one stock in buyrequest
            if (buyRequest?.stock_ids?.length === 1) {
                throw new Error(`Can not create new order with single stock.`);
            }

            /// extract stock_id from stock_ids
            const newStockIds = stock_ids.map((item: any) => item.stock_id);

            /// fetch new stocks
            const newStocks = await models.stocks.findAll({
                where: { id: { [Op.in]: newStockIds }, status: stockStatus.hold }
            });

            if (newStocks.length !== newStockIds.length) {
                throw new Error(`Please refresh stock list.`);
            }

            /// get user data
            const userData: any = await models.users.findOne({
                where: { id: buyRequest.user_id },
                attributes: ['id', 'is_verified']
            });

            if (!userData) {
                throw new Error(`No such user found.`);
            }

            /// calculate total amount from new stocks based on final_price
            const totalAmount = newStockIds
                .map((stockId: any) => newStocks.find((item: any) => item.id === stockId)?.final_price ?? 0)
                .reduce((a: any, b: any) => a + b, 0);

            const taxPrice: number = userData?.is_verified
                ? 0
                : parseFloat(totalAmount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

            const grandTotal = parseFloat(totalAmount) + parseFloat(buyRequest?.shipment_price) + taxPrice;

            /// extract vendor_id from stock_ids
            const vendorIds = [
                ...new Set(stock_ids.filter((item: any) => item?.vendor_id).map((item: any) => item?.vendor_id))
            ];

            const vendorsList = await models.vendors.findAll({
                where: { id: { [Op.in]: vendorIds }, is_blacklisted: true }
            });

            /// check vendors blacklisted
            if (vendorIds && vendorsList.length) {
                throw new Error('Please refresh your stock list');
            }

            /// update margin approval fields into request stock_ids from db if not provided in request
            stock_ids = stock_ids?.map((item: any) => {
                const stockId = buyRequest?.stock_ids?.find((stock: any) => stock?.stock_id === item?.stock_id);
                if (stockId) {
                    return {
                        ...stockId,
                        ...item
                    };
                }
                return item;
            });

            /// generate order code
            const orderCode = makeUniqueKey(10);

            logger.info('Creating new buy requests!!!!');
            /// create new buy request with ACCEPTED status
            const newBuyRequestData: any = await models.buy_requests.create(
                {
                    ...(({ id, createdAt, updatedAt, ...rest }) => rest)(JSON.parse(JSON.stringify(buyRequest))),
                    user_id: buyRequest.user_id,
                    stock_ids: stock_ids.map((item: any) => ({
                        ...item,
                        is_available: true,
                        is_action_taken: item?.vendor_id ? false : true,
                        is_margin_approved: item?.vendor_id ? false : true
                    })),
                    vendor_ids: vendorIds,
                    status: buyRequestStatus.accepted,
                    updated_by_id: reqId,
                    order_code: orderCode,
                    amount: totalAmount,
                    tax_price: parseFloat(taxPrice.toFixed(2)),
                    grand_total: grandTotal,
                    parent_buyrequest_id: buyRequest.id
                },
                { transaction }
            );

            const newBuyRequest = JSON.parse(JSON.stringify(newBuyRequestData));

            if (!newBuyRequest) {
                throw new Error(`Something went wrong!!`);
            }

            /// remove new stock_ids from old stock_ids
            const oldStockIds = buyRequest.stock_ids.filter((item: any) => !newStockIds.includes(item.stock_id));

            /// fetch old stocks
            const oldStocks = await models.stocks.findAll({
                where: { id: { [Op.in]: oldStockIds.map((item: any) => item.stock_id) } },
                attributes: ['id', 'stock_id', 'status', 'final_price']
            });

            /// calulate total amount from old stocks based on final_price
            const oldTotalAmount = oldStockIds
                .map((stockId: any) => oldStocks.find((item: any) => item.id === stockId?.stock_id)?.final_price ?? 0)
                .reduce((a: any, b: any) => a + b, 0);

            /// calculate tax price
            const oldTaxPrice: number = userData?.is_verified
                ? 0
                : parseFloat(oldTotalAmount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

            /// calculate grand total
            const oldGrandTotal = parseFloat(oldTotalAmount) + parseFloat(buyRequest?.shipment_price) + oldTaxPrice;

            /// update old buy request stockids and amounts
            /// test for authorized_amount
            await models.buy_requests.update(
                {
                    stock_ids: oldStockIds,
                    amount: oldTotalAmount,
                    tax_price: parseFloat(oldTaxPrice.toFixed(2)),
                    grand_total: oldGrandTotal,
                    authorized_amount: 0,
                    is_capture_failed: true,
                    trans_id: null,
                    ref_trans_id: null,
                    ref_id: null,
                    authorize_transaction_payload: null,
                    capture_void_transaction_payload: null,
                    card_holder_name: null,
                    card_number: null,
                    card_type: null,
                    exp_date: null
                },
                { where: { id: buyRequest.id }, transaction }
            );

            /// fetch updated old buy request
            const updatedOldBuyRequest = await models.buy_requests.findOne({
                where: { id: buyRequest.id },
                transaction
            });

            /// create old buy request update trail
            await models.buy_request_trails.create(
                {
                    user_id: buyRequest.user_id,
                    buy_request_id: updatedOldBuyRequest.id,
                    status: updatedOldBuyRequest.status,
                    updated_by_id: reqId,
                    parent_buyrequest_id: buyRequest.id,
                    payload: JSON.stringify(updatedOldBuyRequest),
                    notes: 'old buy request updated amounts and stock ids'
                },
                { transaction }
            );

            logger.info('Creating new buy request trail');

            /// new buy request trail
            await models.buy_request_trails.create(
                {
                    user_id: newBuyRequest.user_id,
                    buy_request_id: newBuyRequest.id,
                    status: newBuyRequest.status,
                    updated_by_id: reqId,
                    parent_buyrequest_id: buyRequest.id,
                    payload: JSON.stringify(newBuyRequest),
                    notes: 'new buy request created'
                },
                { transaction }
            );

            logger.info('Putting stocks on HOLD');
            /// change stock status to HOLD
            await models.stocks.update({ status: 'HOLD' }, { where: { id: { [Op.in]: newStockIds } }, transaction });

            /// call accept buy request function and generate order
            await manageAcceptedStatus(req, newBuyRequest, stock_ids, reqId, transaction);

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `New Buy request and order created successfully`,
                data: newBuyRequest
            });

            /// send notification for stock status change
            try {
                if (newBuyRequest?.stock_ids?.length) {
                    userNotification.stockStatusChangeNotification(
                        newBuyRequest.stock_ids.map((item: any) => item.stock_id)
                    );
                }
            } catch (error: any) {
                logger.error(`!!!error sending notification for stock status!!!!! ${error}`);
            }

            /// notify admin/vendor
            try {
                userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                    NotificationType.buyRequestCreated,
                    newBuyRequest.id
                );
            } catch (error: any) {
                logger.error(`!!!error sending notification buy request created!!!!! ${error}`);
            }

            /// send notifications to users when buy request updated
            try {
                userNotification.sendNotification(NotificationType.orderCreated, null, newBuyRequest);
            } catch (error: any) {
                logger.error(`!!!error sending notification to user regarding buy request updated!!!!! ${error}`);
            }

            /// send notification to vendor regarding raise invoice after order created
            try {
                userNotification.sendRaiseInvoiceEmailToVendor(NotificationType.raiseInvoice, newBuyRequest?.id);
            } catch (error: any) {
                logger.error(`!!!error sending notification to vendor regarding raise invoice!!!!! ${error}`);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(`create new buy request error ${error}`);
            next(error);
        }
    }

    /// add filter conditions to buy request
    static async buyRequestFilters(filter_by: any, filter_keyword: any, conditions: any, is_sandbox: boolean) {
        if (filter_by && filter_keyword) {
            /// fetch orders
            switch (filter_by) {
                /// filter by order code
                case FilterOrderBy.ORDER_CODE:
                    /// filter by order code
                    conditions.push({ order_code: { [Op.iLike]: `%${filter_keyword}%` } });

                    break;
                case FilterOrderBy.CERTIFICATE_NO:
                    /// fetch stocks using certificate no
                    const stocksDataList = await models.stocks.findAll({
                        where: { certificate_number: { [Op.iLike]: `%${filter_keyword}%` } }
                    });

                    // Convert stockIds array to a string format for SQL IN clause
                    const stockIdList = JSON.parse(JSON.stringify(stocksDataList))
                        .map((stock: any) => `'${stock.id}'`)
                        .join(',');

                    /// add sequelize literal into conditions
                    conditions.push(
                        stocksDataList?.length
                            ? Sequelize.literal(`
                                EXISTS (
                                    SELECT 1 FROM jsonb_array_elements(to_jsonb(stock_ids)) AS stock
                                    WHERE (stock->>'stock_id')::uuid IN (${stockIdList})
                                )
                            `)
                            : Sequelize.literal(`FALSE`)
                    );

                    break;
                case FilterOrderBy.VENDOR_NAME:
                    /// fetch vendor using vendor name
                    const vendors = await models.vendors.findAll({
                        where: {
                            [Op.and]: [
                                {
                                    [Op.or]: [
                                        { first_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { last_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { phone: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { email: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { company_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        ...(filter_keyword.split(' ').length > 1
                                            ? [
                                                  {
                                                      [Op.and]: [
                                                          {
                                                              first_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[0]}%`
                                                              }
                                                          },
                                                          {
                                                              last_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[1]}%`
                                                              }
                                                          }
                                                      ]
                                                  }
                                              ]
                                            : [])
                                    ]
                                },
                                { is_test_vendor: is_sandbox }
                            ]
                        }
                    });

                    conditions.push({ vendor_ids: { [Op.contains]: vendors.map((vendor: any) => vendor?.id) } });

                    break;
                case FilterOrderBy.USER_NAME:
                    /// fetch users using user name
                    const usersData = await models.users.findAll({
                        where: {
                            [Op.and]: [
                                {
                                    [Op.or]: [
                                        { first_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { last_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { phone: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { email: { [Op.iLike]: `%${filter_keyword}%` } },
                                        ...(filter_keyword.split(' ').length > 1
                                            ? [
                                                  {
                                                      [Op.and]: [
                                                          {
                                                              first_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[0]}%`
                                                              }
                                                          },
                                                          {
                                                              last_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[1]}%`
                                                              }
                                                          }
                                                      ]
                                                  }
                                              ]
                                            : [])
                                    ]
                                },
                                { is_test_user: is_sandbox }
                            ]
                        }
                    });

                    conditions.push({ user_id: { [Op.in]: usersData?.map((user: any) => user?.id) } });

                    break;
                case FilterOrderBy.STOCK_ID:
                    /// fetch stocks using stock id
                    const stocksData = await models.stocks.findAll({
                        where: { stock_id: { [Op.iLike]: `%${filter_keyword}%` } },
                        attributes: ['id']
                    });

                    // Convert stockIds array to a string format for SQL IN clause
                    const stockIdsList = JSON.parse(JSON.stringify(stocksData))
                        .map((stock: any) => `'${stock.id}'`)
                        .join(',');

                    /// add sequelize literal into conditions
                    conditions.push(
                        stocksData?.length
                            ? Sequelize.literal(`
                                EXISTS (
                                    SELECT 1 FROM jsonb_array_elements(to_jsonb(stock_ids)) AS stock
                                    WHERE (stock->>'stock_id')::uuid IN (${stockIdsList})
                                )
                            `)
                            : Sequelize.literal(`FALSE`)
                    );

                    break;
                default:
                    break;
            }
        }
    }
}

export default new BuyRequest();
