import { logger } from '../../../utils/logger';
import { RedisClient } from './redisClient';

export interface StockItem {
    stockId: string;
    weight: number;
    color: string;
    clarity: string;
    shape: string;
    pricePerCaret: number;
    final_price: number;
    discount: string | number;
    ref_per_caret?: number;
}

interface PriceItem {
    low_size: number;
    high_size: number;
    color: string;
    clarity: string;
    caratprice: number;
}

export const roundAlias = {
    round: ['B', 'BR', 'RB', 'RD', 'RBC', 'RND', 'ROUND', 'BRILLIANT', 'ROUND BRILLIANT'],
    pear: ['P', 'PS', 'PSH', 'PB', 'PMB', 'PEAR'],
    emerald: ['Emerald', 'E', 'EM', 'EC', 'SquareEmerald', 'SqEmerald', 'SQEM', 'SX'],
    trilliant: ['trilliant'],
    princess: ['PRN', 'PR', 'Princess', 'PRIN', 'PN', 'PC', 'MDSQ<PERSON>', 'SMB'],
    marquise: ['Marquise', 'MQ<PERSON>', 'M', 'MQ'],
    asscher: ['<PERSON><PERSON>', 'A', 'CSS', '<PERSON>SC', 'AC'],
    cushion: [
        'CushionBrilliant',
        'CB',
        'CUBR',
        'CushionModified',
        'C',
        'CUX',
        'CU',
        'CMB',
        'CUSH',
        'CUS',
        'RCRMB',
        'CRC',
        'CSC',
        'CX',
        'RCSB',
        'SCMB',
        'SCX'
    ],
    heart: ['Heart ', 'H', 'HS', 'HT', 'MHRC'],
    oval: ['Oval', 'O', 'OV', 'OMB'],
    radiant: ['Radiant', 'R', 'RAD', 'RA', 'RC', 'RDN', 'CRB', 'RCRB', 'SquareRadiant', 'SqRadiant', 'SQR', 'CCSMB']
};

export class PriceNetService {
    private static instance: PriceNetService; // Singleton instance
    // Private class-level variables for round and pear data
    private priceRoundData: PriceItem[] = [];
    private pricePearData: PriceItem[] = [];

    // Private constructor to prevent direct instantiation
    private constructor() {}

    // Public method to get the singleton instance
    public static getInstance(): PriceNetService {
        if (!PriceNetService.instance) {
            PriceNetService.instance = new PriceNetService();
        }
        return PriceNetService.instance;
    }

    public async getPriceNetJsonForRound(): Promise<void> {
        logger.info('!!!!!!!!!!!!!!!!!!!getPriceNetJsonForRound function start!!!!!!!!!!!!!!!!!');
        try {
            const redisClient = new RedisClient();
            const value = await redisClient.get('priceShaperound');
            const parsedValue = value ? JSON.parse(value as string) : { data: [] };
            this.priceRoundData = parsedValue?.data || [];
        } catch (error) {
            throw error;
        }
    }

    public async getPriceNetJsonForPear(): Promise<void> {
        logger.info('!!!!!!!!!!!!!!getPriceNetJsonForPear function start!!!!!!!!!!!!!!!!');
        try {
            const redisClient = new RedisClient();
            const value = await redisClient.get('priceShapepear');
            const parsedValue = value ? JSON.parse(value as string) : { data: [] };
            this.pricePearData = parsedValue?.data || [];
        } catch (error) {
            throw error;
        }
    }

    public async getPriceNetJsonForRoundWithInsetData(stockItem: StockItem): Promise<StockItem> {
        logger.info('getPriceNetJsonForRoundWithInsetData function start');
        try {
            let bodyData: PriceItem[] = [];

            // Use class-level variable or fetch data
            if (!this.priceRoundData.length) {
                logger.info('!!!!!!Calling Price Net API!!!!!!!!');
                await this.getPriceNetJsonForRound(); // Fetch round data
                bodyData = this.priceRoundData;
            } else {
                logger.info('!!!!!!Getting From Class Variable!!!!!!!!');
                bodyData = this.priceRoundData;
            }

            let priceStockItem: PriceItem[] = [];

            if (stockItem.weight >= 10) {
                priceStockItem = bodyData.filter(
                    (priceItem) =>
                        priceItem.low_size >= 10 &&
                        priceItem.high_size <= 10.99 &&
                        priceItem.color.toLowerCase() === stockItem.color.toLowerCase() &&
                        priceItem.clarity.toLowerCase() === stockItem.clarity.toLowerCase()
                );
            } else if (stockItem.weight >= 5.99 && stockItem.weight <= 9.99) {
                priceStockItem = bodyData.filter(
                    (priceItem) =>
                        priceItem.low_size <= 5.5 &&
                        priceItem.high_size >= 5.5 &&
                        priceItem.color.toLowerCase() === stockItem.color.toLowerCase() &&
                        priceItem.clarity.toLowerCase() === stockItem.clarity.toLowerCase()
                );
            } else {
                priceStockItem = bodyData.filter(
                    (priceItem) =>
                        priceItem.low_size <= stockItem.weight &&
                        priceItem.high_size >= stockItem.weight &&
                        priceItem.color.toLowerCase() === stockItem.color.toLowerCase() &&
                        priceItem.clarity.toLowerCase() === stockItem.clarity.toLowerCase()
                );
            }

            const updatedItem = { ...stockItem };
            let isUpdated = false;

            // Update based on missing discount
            if (priceStockItem.length > 0 && !stockItem.discount) {
                isUpdated = true;

                if (stockItem.pricePerCaret) {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.discount = parseFloat(((stockItem.pricePerCaret / newRef - 1) * 100).toFixed(2));
                } else if (stockItem.final_price && stockItem.weight) {
                    const pricePerCaret = stockItem.final_price / stockItem.weight;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = pricePerCaret;
                    updatedItem.discount = parseFloat(((pricePerCaret / newRef - 1) * 100).toFixed(2));
                } else {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.discount = 0;
                    updatedItem.pricePerCaret = newRef;
                    updatedItem.final_price = newRef * (stockItem.weight || 1);
                }
            }

            // Update based on missing pricePerCaret
            if (priceStockItem && priceStockItem.length > 0 && !stockItem.pricePerCaret) {
                isUpdated = true;

                if (stockItem.discount) {
                    const countedDiscount = (100 - parseFloat(stockItem.discount as string) * -1) / 100;
                    const pricePerCaret = countedDiscount * priceStockItem[0].caratprice;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = pricePerCaret;
                } else if (stockItem.final_price && stockItem.weight) {
                    const pricePerCaret = stockItem.final_price / stockItem.weight;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = pricePerCaret;
                } else {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = newRef;
                    updatedItem.final_price = newRef * (stockItem.weight || 1);
                }
            }

            // Update based on missing final_price
            if (priceStockItem && priceStockItem.length > 0 && !stockItem.final_price) {
                isUpdated = true;

                if (stockItem.pricePerCaret && stockItem.weight) {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.final_price = stockItem.pricePerCaret * stockItem.weight;
                } else if (stockItem.discount && stockItem.weight) {
                    const countedDiscount = (100 - parseFloat(stockItem.discount as string) * -1) / 100;
                    const pricePerCaret = countedDiscount * priceStockItem[0].caratprice;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.final_price = pricePerCaret * stockItem.weight;
                } else {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = newRef;
                    updatedItem.final_price = newRef * (stockItem.weight || 1);
                }
            }

            return updatedItem;
        } catch (error) {
            throw error;
        }
    }

    public async getPriceNetJsonForPearWithInsetData(stockItem: StockItem): Promise<StockItem> {
        logger.info('getPriceNetJsonForPearWithInsetData function start');
        try {
            let bodyData: PriceItem[] = [];

            // Fetch or use cached data
            if (!this.pricePearData.length) {
                logger.info('!!!!!!Calling Peice Net API for Pear data!!!!!!!!');
                await this.getPriceNetJsonForPear(); // Fetch pear data
                bodyData = this.pricePearData;
            } else {
                logger.info('!!!!!!Getting Pear data from Class Variable!!!!!!!!');
                bodyData = this.pricePearData;
            }

            let priceStockItem: PriceItem[] = [];
            if (stockItem.weight >= 10) {
                priceStockItem = bodyData.filter(
                    (priceItem) =>
                        priceItem.low_size >= 10 &&
                        priceItem.high_size <= 10.99 &&
                        priceItem.color.toLowerCase() === stockItem.color.toLowerCase() &&
                        priceItem.clarity.toLowerCase() === stockItem.clarity.toLowerCase()
                );
            } else if (stockItem.weight >= 5.99 && stockItem.weight <= 9.99) {
                priceStockItem = bodyData.filter(
                    (priceItem) =>
                        priceItem.low_size <= 5.5 &&
                        priceItem.high_size >= 5.5 &&
                        priceItem.color.toLowerCase() === stockItem.color.toLowerCase() &&
                        priceItem.clarity.toLowerCase() === stockItem.clarity.toLowerCase()
                );
            } else {
                priceStockItem = bodyData.filter(
                    (priceItem) =>
                        priceItem.low_size <= stockItem.weight &&
                        priceItem.high_size >= stockItem.weight &&
                        priceItem.color.toLowerCase() === stockItem.color.toLowerCase() &&
                        priceItem.clarity.toLowerCase() === stockItem.clarity.toLowerCase()
                );
            }

            const updatedItem = { ...stockItem };
            let isUpdated = false;

            // Discount update logic
            if (priceStockItem.length > 0 && !stockItem.discount) {
                isUpdated = true;

                if (stockItem.pricePerCaret) {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.discount = parseFloat(((stockItem.pricePerCaret / newRef - 1) * 100).toFixed(2));
                } else if (stockItem.final_price && stockItem.weight) {
                    const pricePerCaret = stockItem.final_price / stockItem.weight;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = pricePerCaret;
                    updatedItem.discount = parseFloat(((pricePerCaret / newRef - 1) * 100).toFixed(2));
                } else {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.discount = 0;
                    updatedItem.pricePerCaret = newRef;
                    updatedItem.final_price = newRef * (stockItem.weight || 1);
                }
            }

            // PricePerCaret update logic
            if (priceStockItem.length > 0 && !stockItem.pricePerCaret) {
                isUpdated = true;

                if (stockItem.discount) {
                    const countedDiscount = (100 - parseFloat(stockItem.discount as string) * -1) / 100;
                    const pricePerCaret = countedDiscount * priceStockItem[0].caratprice;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = pricePerCaret;
                } else if (stockItem.final_price && stockItem.weight) {
                    const pricePerCaret = stockItem.final_price / stockItem.weight;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = pricePerCaret;
                } else {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = newRef;
                    updatedItem.final_price = newRef * (stockItem.weight || 1);
                }
            }

            // Final Price update logic
            if (priceStockItem.length > 0 && !stockItem.final_price) {
                isUpdated = true;

                if (stockItem.pricePerCaret && stockItem.weight) {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.final_price = stockItem.pricePerCaret * stockItem.weight;
                } else if (stockItem.discount && stockItem.weight) {
                    const countedDiscount = (100 - parseFloat(stockItem.discount as string) * -1) / 100;
                    const pricePerCaret = countedDiscount * priceStockItem[0].caratprice;
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.final_price = pricePerCaret * stockItem.weight;
                } else {
                    const newRef = priceStockItem[0].caratprice;
                    updatedItem.ref_per_caret = newRef;
                    updatedItem.pricePerCaret = newRef;
                    updatedItem.final_price = newRef * (stockItem.weight || 1);
                }
            }

            return updatedItem;
        } catch (error) {
            logger.error('Error in getPriceNetJsonForPearWithInsetData:', error);
            throw error;
        }
    }
}
