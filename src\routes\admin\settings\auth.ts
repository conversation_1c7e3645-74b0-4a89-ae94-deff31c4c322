import settings from '../../../controllers/admin/settings';
import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    changeEmailSchema,
    changePasswordSchema,
    updateSystemSettingsSchema
} from '../../../utils/schema/settings_schema';

const router: IRouter = Router();

// change email
router.put(
    '/change-email',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeEmailSchema),
    settings.changeEmail
);

// change password
router.put(
    '/change-password',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changePasswordSchema),
    settings.changePassword
);

// update system settings
router.put(
    '/update-system-settings',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateSystemSettingsSchema),
    settings.updateSystemSettings
);

// vendor change FTP password
router.put(
    '/change-ftp-password',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changePasswordSchema),
    settings.changeFTPVendorPassword
);

export default router;
