import { Router, IRouter, Request, Response, NextFunction } from 'express';
import admin from '../../../controllers/admin/admin';
import { fieldsValidator } from '../../../middlewares/validator';
import { loginAdminSchema } from '../../../utils/schema/admin_schema';

const router: IRouter = Router();

router.post(
    '/login',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, loginAdminSchema),
    admin.adminLogin
);

router.get('/alias-config', admin.getAliasConfig);

router.get('/restart-server', admin.restartServer);

export default router;
