import { NextFunction, Request, Response } from 'express';
import {
    NotificationType,
    PaymentMode,
    adminRole,
    buyRequestType,
    getNotificationType,
    httpStatusCodes,
    orderStatus,
    paymentType,
    stockStatus,
    unifiedOrderType,
    vendorOrderStatus
} from '../../utils/constants';
import models, { sequelize } from '../../models';
import { Op, or } from 'sequelize';
import { logger } from '../../utils/logger';
import { OrderAttributes } from '../../models/orders';
import { StockAttributes } from '../../models/stocks';
import { BuyRequestAttributes } from '../../models/buy_requests';
import userNotification from '../../controllers/user/user_notifications/user_notification';
import authorizePayment from '../user/authorize_payment/authorize_payment';

class Order {
    /**
     * @api {get} /v1/auth/admin/order
     * @apiName ListAdminOrder
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} Orders.
     */
    async listOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list admin order');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.order_status;
            const paymentStatus = req.query.payment_status;
            const conditions: any = [];

            /// order status
            if (status) {
                conditions.push({ order_status: status.toString().toUpperCase() });
            }

            /// payment status
            if (paymentStatus) {
                conditions.push({ payment_status: paymentStatus.toString().toUpperCase() });
            }

            /// fetch only diamond orders
            conditions.push({
                unified_order_type: {
                    [Op.or]: [{ [Op.eq]: null }, { [Op.eq]: unifiedOrderType.DIAMONDS }]
                }
            });

            /// fetch orders
            const { rows, count } = await models.orders.findAndCountAll({
                where: { [Op.and]: conditions },
                offset: skip,
                limit,
                order: [['createdAt', 'DESC']]
            });

            /// fetch all return orders
            const returnOrders = await models.return_orders.findAll({
                where: { order_id: { [Op.in]: rows?.map((order: any) => order?.id) } }
            });

            /// extract buy request ids
            const buyRequestIds = rows.map((order: any) => order.buy_request_id);

            /// fetch buy requests
            const buyRequests = await models.buy_requests.findAll({
                where: { id: { [Op.in]: buyRequestIds } }
            });

            /// add buy request details in result
            const result = rows
                .map((order: any) => ({
                    ...JSON.parse(JSON.stringify(order)),
                    buy_request_details: JSON.parse(JSON.stringify(buyRequests)).find(
                        (item: any) => item.id === order.buy_request_id
                    ),
                    return_orders: returnOrders?.find((item: any) => item?.order_id === order?.id)
                }))
                .filter((order: any) => order?.buy_request_details);

            /// extract all stock ids
            const stockIds = [
                ...new Set(
                    [].concat(
                        ...result.map((row: any) =>
                            row?.buy_request_details.stock_ids?.map((item: any) => item?.stock_id)
                        )
                    )
                )
            ];

            /// fetch stock details
            const stocks = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'price_per_caret',
                    'final_price',
                    'weight',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type'
                ]
            });

            /// update users details
            const updatedResult = result.map((row: any) => {
                /// total cts = sum of weight
                const totalCTS = stocks.length
                    ? row.buy_request_details.stock_ids
                        .map((item: any) => item.stock_id)
                        .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.weight)
                        .reduce((a: any, b: any) => a + b, 0)
                    : 0;

                // /// total amount = sum of final price
                // const totalAmount = stocks
                //     ? row.buy_request_details.stock_ids
                //           .map((item: any) => item.stock_id)
                //           .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price)
                //           .reduce((a: any, b: any) => a + b, 0)
                //     : 0;

                return {
                    ...JSON.parse(JSON.stringify(row)),
                    buy_request_details: {
                        ...JSON.parse(JSON.stringify(row.buy_request_details)),
                        total_cts: totalCTS,
                        total_amount: row.amount,
                        price_per_carat: parseFloat((row.amount / totalCTS).toFixed(2))
                    }
                };
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order listed successfully`,
                data: updatedResult,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/order-details
     * @apiName AdminOrderDetails
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} Orders.
     */
    async orderDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling admin orderDetails');
        try {
            const id = req.query.id;
            const role = req[`role`];

            const order: OrderAttributes = await models.orders.findOne({
                where: { id },
            });

            if (!order) {
                throw new Error(`Order not found!!`);
            }

            const buyRequest: BuyRequestAttributes = await models.buy_requests.findOne({
                where: { id: order.buy_request_id }
            });

            const vendorIds = buyRequest.stock_ids.map((stock: any) => stock.vendor_id);

            const vendors = await models.vendors.findAll({
                where: { id: { [Op.in]: vendorIds } }
            });

            /// fetch return orders
            const returnOrders = await models.return_orders.findAll({ where: { order_id: id } });

            const stockIds = buyRequest.stock_ids.map((item: any) => item.stock_id);

            /// fetch stocks
            const stocks: StockAttributes[] = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'price_per_caret',
                    'final_price',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type',
                    'admin_id',
                    'vendor_id',
                    'sku_number',
                    'stock_margin'
                ]
            });

            /// replace stock final price with offer price
            if (order.type === buyRequestType.offer) {
                for (const stock of stocks) {
                    const offer: any = await models.stock_offers.findOne({ where: { stock_id: stock.id } });
                    if (role === adminRole.vendor) {
                        stock.final_price = offer?.updated_offer_price;
                        stock.price_per_caret = offer?.updated_offer_price / (stock?.weight ?? 0);
                        stock.final_price_ori = offer?.updated_offer_price;
                        stock.price_per_caret_ori = offer?.updated_offer_price / (stock?.weight ?? 0);
                    } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                        stock.final_price = offer?.offer_price;
                        stock.price_per_caret = offer?.offer_price / (stock?.weight ?? 0);
                    }
                }
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order details listed successfully`,
                data: {
                    ...JSON.parse(JSON.stringify(order)),
                    buy_request_details: {
                        ...JSON.parse(JSON.stringify(buyRequest)),
                        stock_ids: buyRequest.stock_ids.map((item: any) => ({
                            ...item,
                            stock: stocks.find((stock: any) => stock.id === item.stock_id),
                            vendor: vendors.find((vendor: any) => vendor.id === item.vendor_id)
                        }))
                    },
                    return_orders: returnOrders?.find((item: any) => item?.order_id === id)
                }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/order-status
     * @apiName UpdateOrderStatus
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} UpdateOrderStatus.
     */
    async updateOrderStatus(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update order status');

        const transaction = await sequelize.transaction();

        try {
            const { ids, status, courier_company, awb_number } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];

            const updateObject: any = {};

            const orders = await models.orders.findAll({
                where: { id: { [Op.in]: ids } },
                include: [
                    {
                        model: models.buy_requests,
                        required: false,
                        attributes: ['id', 'payment_mode']
                    }
                ]
            });

            if (orders.length !== ids.length) {
                throw new Error('Order not found');
            }

            if (status) {
                updateObject.order_status = status;
                if (status === orderStatus.delivered) {
                    updateObject.payment_status = 'PAID';
                } else if (status === orderStatus.shipped) {
                    if (!courier_company || !awb_number) {
                        throw new Error('required courier_company and awb_number');
                    }

                    updateObject.courier_company = courier_company;
                    updateObject.awb_number = awb_number;
                }
            }

            /// check all vendor orders are shipped
            if (status === orderStatus.shipped) {
                /// fetch vendor orders
                const vendorOrders = await models.vendor_orders.findAll({
                    where: { order_id: { [Op.in]: ids } },
                    transaction
                });

                /// client order can move to shipped if vendor order shipped, paid or delivered
                ids.map((orderId: string) => {
                    const vendorOrder = vendorOrders.find((order: any) => (order.order_id = orderId));
                    if (vendorOrder) {
                        if (
                            ![vendorOrderStatus.shipped, vendorOrderStatus.paid, vendorOrderStatus.delivered].includes(
                                vendorOrder.status
                            )
                        ) {
                            throw new Error('Vendor orders are not shipped!!!!!');
                        }
                    }
                });
            }

            /// check all vendor orders are paid
            // if (status === orderStatus.delivered) {
            //     const vendorOrders = await models.vendor_orders.findAll({
            //         where: { order_id: { [Op.in]: ids } },
            //         transaction
            //     });

            //     ids.map((orderId: string) => {
            //         const vendorOrder = vendorOrders.find((order: any) => (order.order_id = orderId));
            //         if (vendorOrder) {
            //             if (vendorOrder.status !== vendorOrderStatus.paid) {
            //                 throw new Error('Vendor orders are not paid!!!!!');
            //             }
            //         }
            //     });
            // }

            logger.info(`Updating order status:${status}!!!!`);
            /// update order status
            await models.orders.update(updateObject, { where: { id: { [Op.in]: ids } }, transaction });

            /// fetch orders for order trail
            const orderPayloads = await models.orders.findAll({ where: { id: { [Op.in]: ids } }, transaction });

            logger.info('Creating order trails when status changed!!!');
            /// order trail bulk data
            const orderTrailData = orderPayloads.map((order: any) => ({
                order_id: order.id,
                user_id: order.user_id,
                buy_request_id: order.buy_request_id,
                updated_by_id: reqId,
                payload: JSON.stringify(order),
                payment_status: order.payment_status, // pending, paid, failed, canceled
                order_status: order.order_status // pending, processing, shipped, delivered, canceled
            }));

            /// create order trail
            await models.order_trails.bulkCreate(orderTrailData, { transaction });

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order updated successfully`
            });

            try {
                if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    if (
                        status === orderStatus.canceled ||
                        status === orderStatus.delivered
                        //  status === orderStatus.shipped
                    ) {
                        /// send notifications to users when order updated
                        userNotification.sendNotification(getNotificationType(status), null, null, orderPayloads);

                        ////
                    }
                }
            } catch (error: any) {
                logger.error(`order update notification error ${JSON.stringify(error, null, 2)}`);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/cancel-order
     * @apiName CancelOrder
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} CancelOrder.
     */
    async cancelOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling cancel order');

        const transaction = await sequelize.transaction();

        try {
            const { id } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];

            const order = await models.orders.findOne({
                where: { id },
                include: [
                    {
                        model: models.buy_requests,
                        required: false,
                        attributes: [
                            'id',
                            'status',
                            'user_id',
                            'stock_ids',
                            'payment_mode',
                            'trans_id',
                            'ref_trans_id',
                            'card_number',
                            'exp_date',
                            'authorized_amount',
                            'type'
                        ]
                    }
                ]
            });

            if (!order) {
                throw new Error('Order not found');
            }

            /// check all vendor orders are shipped
            const vendorOrders = await models.vendor_orders.findAll({ where: { order_id: id }, transaction });

            if (vendorOrders.length) {
                /// check vendor order status
                const isAllVendorOrdersPending: boolean = vendorOrders?.every(
                    (vendorOrder: any) => vendorOrder.status === vendorOrderStatus.pending
                );

                /// check all vendor orders are pending
                if (!isAllVendorOrdersPending) {
                    throw new Error(`Couldn't cancel order, vendor orders are shipped or paid!!!!!`);
                }
            }

            logger.info('Updating order status to cancelled');
            /// update order status
            await models.orders.update({ order_status: orderStatus.canceled }, { where: { id }, transaction });

            logger.info('Updating vendor orders to cancelled');
            /// update vendor order status
            await models.vendor_orders.update(
                { status: orderStatus.canceled },
                { where: { order_id: id }, transaction }
            );

            /// fetch orders for order trail
            const orderPayload = await models.orders.findOne({ where: { id } }, { transaction });

            logger.info('Creating order trails!!!');
            /// create order trail
            await models.order_trails.create(
                {
                    order_id: orderPayload.id,
                    user_id: orderPayload.user_id,
                    buy_request_id: orderPayload.buy_request_id,
                    updated_by_id: reqId,
                    payload: JSON.stringify(orderPayload),
                    payment_status: orderPayload.payment_status, // pending, paid, failed, canceled
                    order_status: orderPayload.order_status // pending, processing, shipped, delivered, canceled
                },
                { transaction }
            );

            /// fetch orders for vendor order trail
            const vendorOrderPayloads = await models.vendor_orders.findAll({ where: { order_id: id }, transaction });

            const vendorOrderBulkCreateObject = vendorOrderPayloads.map((vendorOrderPayload: any) => ({
                vendor_order_id: vendorOrderPayload.id,
                order_id: vendorOrderPayload.order_id,
                buy_request_id: vendorOrderPayload.buy_request_id,
                vendor_id: vendorOrderPayload.vendor_id,
                updated_by_id: reqId,
                payload: JSON.stringify(vendorOrderPayload),
                status: vendorOrderPayload.status // pending, shipped, paid, canceled
            }));

            logger.info('Creating vendor order trails!!!');
            /// create vendor order trail
            await models.vendor_order_trails.bulkCreate(vendorOrderBulkCreateObject, { transaction });

            logger.info('Initiating refund amount!!');
            logger.info(`authorized_amount: ${order?.buy_request?.authorized_amount}`);
            logger.info(`grand_total: ${order?.grand_total}`);
            /// initiate refund
            /// void transaction
            if (order?.buy_request?.payment_mode === PaymentMode.creditCard) {
                /// check refund amount is less than authorized amount
                if (
                    parseFloat(parseFloat(order?.buy_request?.authorized_amount).toFixed(2)) >=
                    parseFloat(parseFloat(order?.grand_total).toFixed(2))
                ) {
                    /// refund transaction
                    await authorizePayment.refundTransaction(order?.grand_total, order?.buy_request, transaction);
                } else {
                    throw new Error('Refund amount exceeded authorized amount!!!');
                }
            } /// restore credit limit
            else if (order?.buy_request?.payment_mode === PaymentMode.creditLimit) {
                logger.info('re-credit credit limit on return order!!!!!!');
                /// credit refund amount when return order accepted
                if (order?.grand_total) {
                    /// make credit entry
                    await models.credit_histories.create(
                        {
                            user_id: order?.buy_request?.user_id,
                            credit: parseFloat(parseFloat(order?.grand_total).toFixed(2)),
                            type: 'RETURN-ORDER',
                            transaction_type: 'CREDIT',
                            buy_request_id: order?.buy_request?.id
                        },
                        { transaction }
                    );
                }
            } else if (order?.buy_request?.payment_mode === PaymentMode.applePay) {
                /// check refund amount is less than authorized amount
                if (
                    parseFloat(parseFloat(order?.buy_request?.authorized_amount).toFixed(2)) >=
                    parseFloat(parseFloat(order?.grand_total).toFixed(2))
                ) {
                    /// refund transaction
                    await authorizePayment.refundTransaction(
                        order?.buy_request?.authorized_amount,
                        order?.buy_request,
                        transaction
                    );
                } else {
                    throw new Error('Refund amount exceeded authorized amount!!!');
                }
            }

            logger.info('Making stocks available!!!!');

            /// make stock available
            await models.stocks.update(
                { status: stockStatus.available },
                {
                    where: {
                        id: { [Op.in]: order?.buy_request?.stock_ids?.map((stockId: any) => stockId?.stock_id) }
                    },
                    transaction
                }
            );

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order updated successfully`
            });

            try {
                if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    /// send notifications to users when order updated
                    userNotification.sendNotification(NotificationType.orderCanceled, null, null, [orderPayload]);
                }
            } catch (error: any) {
                logger.error(`order update notification error ${JSON.stringify(error, null, 2)}`);
            }

            try {
                /// send credit limit added notifications when order cancelled
                if (order?.buy_request?.payment_mode === PaymentMode.creditLimit) {
                    /// send notification when credit added
                    userNotification.sendCreditLimitNotifications(
                        NotificationType.creditLimitAdded,
                        order?.buy_request?.user_id
                    );
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/update-invoice
     * @apiName UpdateOrderInvoice
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} UpdateOrderInvoice.
     */
    async updateOrderInvoice(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update order invoice');

        try {
            const { id, file_name } = req.body;
            const role = req[`role`];

            const updateObject: any = {};

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error('unauthorized!!');
            }

            const order: any = await models.orders.findOne({ where: { id } });

            if (!order) {
                throw new Error('Order not found!!');
            }

            if (file_name) {
                updateObject.invoice_file_name = file_name;

                // updateObject.is_invoice_sent = true;

                updateObject.invoice_url = `${process.env.BASE_URL}/invoice/${file_name}`;
            }

            /// update order status
            await models.orders.update(updateObject, { where: { id } });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice updated successfully`
            });

            try {
                /// invoice available notification
                userNotification.sendInvoiceAvailableNotifications(NotificationType.invoiceAvailable, order.id);
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Order();
