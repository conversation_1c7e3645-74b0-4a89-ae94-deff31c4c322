import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface CartAttributes {
    id?: string; // id is an auto-generated UUID
    user_id?: string;
    type?: string;
    diamond_id?: string;
    jewellery_id?: string;
    melee_id?: string;
    data?: string;
    quantity: number;
    is_active?: boolean;
    _deleted?: boolean;
    expiresAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    // Association Fields
}

interface CartCreationAttributes extends Optional<CartAttributes, 'id'> { }

interface CartInstance extends Model<CartAttributes, CartCreationAttributes>, CartAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type CartStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => CartInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const carts = sequelize.define<CartInstance>(
        'carts',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            type: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: ['diamond', 'jewellery', 'melee']
            },
            diamond_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            jewellery_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            melee_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            data: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            quantity: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 1
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as CartStatic;

    // TODO: make common function to sync
    // await carts.sync({ alter: true });

    return carts;
};
