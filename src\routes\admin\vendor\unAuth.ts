import { Router, IRouter, Request, Response, NextFunction } from 'express';
import vendor from '../../../controllers/admin/vendor';
import settings from '../../../controllers/admin/settings';
import { fieldsValidator } from '../../../middlewares/validator';
import { createDRCVendorSchema, createVendorSchema } from '../../../utils/schema/vendor_schema';
import { resetPasswordSchema, sendVerificationOtpSchema } from '../../../utils/schema/settings_schema';
import { changeUserEmailVerificationStatusSchema } from '../../../utils/schema/user_schema';

const router: IRouter = Router();

// add vendor
router.post(
    '/vendor',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createVendorSchema),
    vendor.addVendor
);

// vendor send otp
router.put(
    '/vendor/send-verification-otp',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, sendVerificationOtpSchema),
    settings.sendVendorVerificationOtp
);

// vendor verify email
router.put(
    '/vendor/verify-email',
    (req: Request, res: Response, next: NextFunction) =>
        fieldsValidator(req, res, next, changeUserEmailVerificationStatusSchema),
    settings.verifyVendorEmail
);

// vendor reset password
router.put(
    '/vendor/reset-password',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, resetPasswordSchema),
    settings.resetVendorPassword
);

// create DRC vendor
router.post(
    '/drc-vendor',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createDRCVendorSchema),
    vendor.createDRCVendor
);

export default router;
