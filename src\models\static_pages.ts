import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface StaticPagesAttributes {
    id: string;
    slug: string;
    title: string;
    content: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface StaticPagesCreationAttributes extends Optional<StaticPagesAttributes, 'id'> {}

interface StaticPagesInstance
    extends Model<StaticPagesAttributes, StaticPagesCreationAttributes>,
        StaticPagesAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type StaticPagesStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => StaticPagesInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const static_pages = sequelize.define<StaticPagesInstance>(
        'static_pages',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            slug: {
                type: DataTypes.STRING,
                allowNull: false
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false
            },
            content: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as StaticPagesStatic;

    // TODO: make common function to sync
    // await static_pages.sync({ alter: true });

    return static_pages;
};
