import axios from 'axios';
import { logger } from '../../../utils/logger';

export function axiosPost(requestData: any) {
    logger.info(`authorize request data: ${JSON.stringify(requestData, null, 2)}`);

    const url =
        process.env.PAYMENT_VALIDATION_MODE === 'liveMode'
            ? process.env.AUTHORIZE_PROD_URL
            : process.env.AUTHORIZE_DEV_URL;
    logger.info(`!!!!!!!!!URL!!!!!!!!!!!!!! ${url}`);

    return new Promise((resolve, reject) => {
        axios
            .post(`${url}`, requestData, {
                headers: { 'Content-Type': 'application/json' }
            })
            .then(async (response: any) => {
                logger.info(`authorize response data: ${JSON.stringify(response.data, null, 2)}`);
                if (response.data.messages.resultCode === 'Error') {
                    response.data.messages.message.forEach((message: any) => {
                        if (message.code === 'E00124') {
                            // Handle the error (e.g., refresh token, log the error, alert the user)
                            /// await handleInvalidToken();
                        } else if (message.code === 'E00039') {
                            // A duplicate record with ID 519534262 already exists
                            /// await handleInvalidToken();
                        }
                    });
                    reject(response.data);
                } else if (
                    response.data.transactionResponse?.errors &&
                    response.data.transactionResponse.errors.length
                ) {
                    response.data.transactionResponse?.errors.forEach((error: any) => {
                        if (error?.errorCode === '30') {
                            // "errorCode": "30",
                            // "errorText": "The configuration with processor is invalid. Call Merchant Service Provider."
                        }
                    });
                    reject(response.data);
                }
                resolve(response.data);
            })
            .catch((error) => {
                logger.error(`authorize error: ${error}`);
                reject(error);
            });
    });
}
