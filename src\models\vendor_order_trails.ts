import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface VendorOrderTrailsAttributes {
    id: string; // id is an auto-generated UUID
    vendor_order_id: string;
    order_id: string;
    buy_request_id: string;
    vendor_id: string;
    updated_by_id: string;
    payload: string;
    status: string; // pending, shipped, paid, DELIVERED
    approved_margin: object;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface VendorOrderTrailsCreationAttributes extends Optional<VendorOrderTrailsAttributes, 'id'> {}

interface VendorOrderTrailsInstance
    extends Model<VendorOrderTrailsAttributes, VendorOrderTrailsCreationAttributes>,
        VendorOrderTrailsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type VendorOrderTrailsStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => VendorOrderTrailsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const vendor_order_trails = sequelize.define<VendorOrderTrailsInstance>(
        'vendor_order_trails',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            vendor_order_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            updated_by_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            status: {
                type: DataTypes.ENUM('PENDING', 'SHIPPED', 'PAID', 'CANCELED', 'DELIVERED'),
                allowNull: false
            },
            payload: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            approved_margin: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as VendorOrderTrailsStatic;
    //
    // await vendor_order_trails.sync({ alter: true })

    return vendor_order_trails;
};
