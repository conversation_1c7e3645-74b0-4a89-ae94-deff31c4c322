import { NextFunction, raw, Request, Response } from 'express';
import models, { sequelize } from '../../models';
import {
    adminRole,
    generateStrongPassword,
    httpStatusCodes,
    makeUniqueKey,
    NotificationType,
    OfferStatus,
    stockStatus
} from '../../utils/constants';
import { logger } from '../../utils/logger';
import bcrypt from 'bcrypt';
import { Op, Sequelize } from 'sequelize';
import { VendorAttributes } from '../../models/vendors';
import axios from 'axios';
import { decryptBody, encryptBody } from '../../utils/encrypt';
import userNotifications from '../user/user_notifications/user_notification';
import mailServices from '../user/user_notifications/mail_services';
import FormData from 'form-data';
import whatsapp from '../common/whatsapp';
import { getStockModalFields } from './stock/getStockModelFields';
import stock from './stock/stock';

class Vendor {
    /*
        --------------------------------------------------------------------------------
        Vendor functions
    */

    /**
     * @api {get} /v1/auth/admin/vendor
     * @apiName listVendors
     * @apiGroup AdminVendors
     *
     *
     * @apiSuccess {Object} Vendors.
     */
    async listVendors(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!listVendors function start!!!!!');
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            const isActive = req.query.is_active;
            const isVerified = req.query.is_verified;
            const lastInventoryUpdate = req.query.last_inventory_update;

            const q = req.query.q;
            const keyword = `%${String(q).toLowerCase().trim()}%`;
            const conditions: any = [];

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (q) {
                conditions.push({
                    [Op.or]: [
                        { first_name: { [Op.iLike]: keyword } },
                        { last_name: { [Op.iLike]: keyword } },
                        { phone: { [Op.iLike]: keyword } },
                        { email: { [Op.iLike]: keyword } },
                        { company_name: { [Op.iLike]: keyword } },
                        ...(keyword.split(' ').length > 1
                            ? [
                                {
                                    [Op.and]: [
                                        { first_name: { [Op.iLike]: `%${keyword.split(' ')[0]}%` } },
                                        { last_name: { [Op.iLike]: `%${keyword.split(' ')[1]}%` } }
                                    ]
                                }
                            ]
                            : [])
                    ]
                });
            }

            if (isActive) {
                conditions.push({ is_active: isActive });
            }

            if (isVerified) {
                conditions.push({ is_verified: isVerified });
            }

            if (lastInventoryUpdate) {
                conditions.push({ last_inventory_update: { [Op.gte]: lastInventoryUpdate } });
            }

            const { rows, count } = await models.vendors.findAndCountAll({
                where: {
                    [Op.and]: conditions
                },
                order: [lastInventoryUpdate ? ['last_inventory_update', 'DESC'] : ['createdAt', 'DESC']],
                offset: skip,
                limit,
                attributes: { exclude: ['password', 'otp'] }
            });

            const resultData: any[] = [];

            for (const item of rows) {
                let decryptedPassword: any;
                if (item?.ftp_password) {
                    decryptedPassword = await decryptBody(item?.ftp_password);
                }
                resultData.push({
                    ...JSON.parse(JSON.stringify(item)),
                    ftp_password: item?.ftp_password ? decryptedPassword : null
                });
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendors successfully listed',
                data: resultData,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor
     *  @apiName createDRCVendor
     *  @apiGroup DRCVendor
     *
     *  @apiSuccess {Object} DRCVendor
     */
    async createDRCVendor(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!Calling create vendor from DRC function start!!!!!');
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const existingVendors = await models.vendors.findAll({
                where: { _deleted: false },
                attributes: ['id', 'first_name', 'last_name', 'email']
            });

            const { vendors } = req.body;

            const credentialsForNewVendors: any[] = [];
            const vendorsData: any[] = [];
            const ignoredExistingVendors: any[] = [];

            for (const item of vendors) {
                const isExist = existingVendors.find((existingItem) => existingItem?.email === item?.email);

                if (isExist) {
                    logger.info(`!!!!!!Vendor Already Registered!!!!!!!! ${item?.email}`);
                    ignoredExistingVendors.push({
                        email: item?.email,
                        id: isExist?.id
                    });

                    return;
                }

                /// email credentials to vendor
                const vendorPassword: any = generateStrongPassword(8);
                const hashPassword = await bcrypt.hash(vendorPassword, 12);

                credentialsForNewVendors.push({
                    email: String(item?.email).toLowerCase().trim(),
                    password: vendorPassword,
                    first_name: item?.first_name,
                    last_name: item?.last_name,
                    phone: item?.phone
                });

                vendorsData.push({
                    email: String(item?.email).toLowerCase().trim(),
                    company_name: item?.company_name,
                    first_name: item?.first_name,
                    last_name: item?.last_name,
                    phone: item?.phone,
                    document_url: item?.document_url,
                    api_url: item?.api_url,
                    vendor_type: 'API',
                    headers: JSON.stringify({ Authorization: item?.api_key }),
                    api_type: 'get',
                    is_from_DRC: true,
                    is_verified: true,
                    kyc_uploaded_true: true,
                    password: hashPassword
                });
            }

            logger.info(`!!!!!!!!!!Total Vendors onboarded!!!!!!!! ${vendorsData.length}`);

            const createdVendors = await models.vendors.bulkCreate(vendorsData, {
                ignoreDuplicates: true,
                returning: true
            });

            logger.info(`!!!!!!!!!!Total Vendors Ignored!!!!!!!! ${ignoredExistingVendors.length}`);

            const createdVendorsEmail: any[] = createdVendors.map((item: any) => item?.email);

            logger.info(`Successfully created DRC vendors!!!!! ${createdVendorsEmail}`);

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendor from DRC created successfully!',
                createdVendorsEmail
            });

            /// send login credentials to DRC vendor
            try {
                if (createdVendorsEmail.length) {
                    /// created vendors email
                    for (const email of createdVendorsEmail) {
                        /// created vendors credentials
                        const credentials: any = credentialsForNewVendors.find((item: any) => item?.email === email);

                        if (credentials) {
                            /// email body
                            const message = `
We are pleased to inform you that you have been auto-enrolled as a trusted supplier for the Diamond Company portal.<br><br>

As part of the Diamond Company family, we aim to help you get started quickly and seamlessly:<br><br>

1. Log in to the Vendor panel.<br>
2. Read and accept the Terms & Conditions—this will securely share your KYC documents with Diamond Company.<br><br>

Once the Terms & Conditions are accepted, your inventory will automatically be shared with Diamond Company.<br><br>

---<br><br>

Kindly find your login credentials below:<br><br>

<b>Email:</b> ${credentials?.email}<br>
<b>Password:</b> ${credentials?.password}<br><br>

<b>Access Links:</b><br>
- <b>Website:</b> <a href="https://vendor.diamondcompany.com/" target="_blank">Vendor Panel</a><br>
- <b>Android App:</b> <a href="https://play.google.com/store/apps/details?id=com.diamondcompany.app" target="_blank">Download from Google Play</a><br>
- <b>iOS App:</b> <a href="https://apps.apple.com/us/app/diamond-company/id6503249317" target="_blank">Download from App Store</a><br><br>

---<br>

If you have any questions, feel free to reach out:<br>
<b>Phone:</b> +917359746706<br><br>`;

                            /// send mail
                            try {
                                mailServices.send({
                                    to: String(credentials?.email).toLowerCase().trim(),
                                    subject: 'Login Credentials',
                                    data: { name: `${credentials?.first_name} ${credentials?.last_name}`, message }
                                });
                                logger.info(`!!!!!!!!!!Mail Sent SuccessFully!!!!!!!!!!!`);
                            } catch (error: any) {
                                logger.error(`!!!!!!!!!Error sending mail!!!!!!!!!!!! ${error}`);
                            }

                            try {
                                const sendPhone = `+${credentials.phone.split('-').join('')}`;
                                whatsapp.sendWhatsappMessage(sendPhone, 'new_user');
                                logger.info(`!!!!!!!!!!Whatsapp Sent SuccessFully!!!!!!!!!!!`);
                            } catch (error: any) {
                                logger.error(`!!!!!!!!!Error sending whatsapp!!!!!!!!!!!! ${error}`);
                            }
                        }
                    }
                }

                ////
            } catch (error) {
                logger.error(`Send email to drc vendor error ${error}`);
            }

            /////
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor
     *  @apiName addVendor
     *  @apiGroup Vendor
     *
     *  @apiSuccess {Object} Vendor
     */
    async addVendor(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!AddVendor function start!!!!!');
        try {
            const vendorsData = req.body;
            let email = req.body.email;
            const phone = req.body.phone;
            const vendor_type = req.body.vendor_type;
            const password = req.body.password;

            const { document_name } = req.body;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            // if (vendor_type === 'API' && !req.body.api_url) {
            //     throw new Error('api_url is required');
            // }

            const emailOrPhoneExists = await models.vendors.unscoped().findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
            });

            if (emailOrPhoneExists) {
                throw new Error('Vendor already registered  with this Email or phone');
            }

            const hashPassword = await bcrypt.hash(password, 12);

            vendorsData.password = hashPassword;

            if (email) {
                vendorsData.email = email;
            }

            if (document_name) {
                vendorsData.is_kyc_uploaded = true;
                vendorsData.document_url = `${process.env.BASE_URL}/vendor/document/${document_name}`;
            }

            const vendor: VendorAttributes = await models.vendors.create(vendorsData);

            const vendorData = JSON.parse(JSON.stringify(vendor));

            delete vendorData.password;
            delete vendorData.otp;

            const encryptedPassword = encryptBody(password);

            const result = await new Promise(async (resolve, reject) => {
                try {
                    /// post request
                    await axios.post(
                        process.env.CREATE_FTP_URL ?? '',
                        {
                            ftp_username: email,
                            ftp_password: password,
                            vendor_id: vendorData?.id
                        },
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                authorization: process.env.ADD_STOCK_KEY
                            }
                        }
                    );
                    resolve(true);
                    ///
                } catch (error) {
                    logger.error(error);
                    // reject(error);
                    resolve(false);
                }
            });

            if (result) {
                await models.vendors.update(
                    { ftp_username: email, ftp_password: encryptedPassword },
                    { where: { id: vendorData.id } }
                );
                vendorData.ftp_username = email;
                vendorData.ftp_password = await decryptBody(encryptedPassword);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendor created successfully!',
                data: vendorData
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor
     *  @apiName updateVendor
     *  @apiGroup Vendor
     *
     *  @apiSuccess {Object} Vendor
     */
    async updateVendor(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!UpdateVendor function start!!!!!');
        try {
            const id: any = req.body.id;
            const dataObject: any = {};
            const role = req[`role`];
            const reqId = req[`id`];
            const { phone, document_name, margin_percentage } = req.body;

            let email = req.body.email;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            const vendor_type = req.body.vendor_type;

            if (role === adminRole.vendor) {
                if (id !== reqId) {
                    throw new Error('Unauthorized Access');
                }
            }

            // if (vendor_type === 'API' && !req.body.api_url) {
            //     throw new Error('api_url is required');
            // }

            for (const key in req.body) {
                if (req.body[key]) {
                    dataObject[key] = req.body[key];
                }
            }

            const vendorExists = await models.vendors.findOne({
                where: { id, _deleted: false }
            });

            if (!vendorExists) {
                throw new Error('Vendor not found');
            }

            const emailOrPhoneExists = await models.vendors.findOne({
                where: {
                    [Op.or]: [email ? { email } : {}, phone ? { phone } : {}],
                    id: { [Op.ne]: id },
                    _deleted: false
                }
            });

            if (emailOrPhoneExists) {
                throw new Error('Vendor already registered  with this Email or phone');
            }

            if (email) {
                dataObject.email = email;
            }

            if (document_name) {
                dataObject.is_kyc_uploaded = true;
                dataObject.document_url = `${process.env.BASE_URL}/vendor/document/${document_name}`;
            }

            const vendor = await models.vendors.update(dataObject, {
                where: { id },
                returning: true,
                plain: true
            });

            const updatedVendor = JSON.parse(JSON.stringify(vendor[1]));

            delete updatedVendor.password;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendor updated successfully!',
                data: updatedVendor
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor
     *  @apiName deleteVendor
     *  @apiGroup Vendor
     *
     *  @apiSuccess {Object} Vendor
     */
    async deleteVendor(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!DeleteVendor function start!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const id = req.query.id;

            const vendorExists = await models.vendors.findOne({
                where: { id, _deleted: false }
            });

            if (!vendorExists) {
                throw new Error('Vendor not found');
            }

            await models.vendors.update(
                {
                    _deleted: true
                },
                {
                    where: { id },
                    transaction
                }
            );

            // delete all available stock of this vendor
            await models.stocks.update({ _deleted: true }, { where: { vendor_id: id }, transaction });

            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendor deleted successfully!'
            });
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor/status
     *  @apiName changeVendorStatus
     *  @apiGroup Vendor
     *
     *  @apiSuccess {Object} Vendor
     */
    async changeVendorStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_active } = req.body;

            const vendorExists = await models.vendors.findOne({
                where: { id, _deleted: false }
            });

            if (!vendorExists) {
                throw new Error('Vendor not found');
            }

            await models.vendors.update(
                {
                    is_active
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Updated vendor status'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor/black-list
     *  @apiName changeVendorBlackList
     *  @apiGroup VendorBlackList
     *
     *  @apiSuccess {Object} VendorBlackList
     */
    async blackListVendor(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!BlackListVendor function start!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const { id, is_blacklisted } = req.body;

            const role = req[`role`];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role as adminRole)) {
                throw new Error('Unauthorized access');
            }

            const vendorExists = await models.vendors.findOne({
                where: { id, _deleted: false }
            });

            if (!vendorExists) {
                throw new Error('Vendor not found');
            }

            const vendorData = await models.vendors.update(
                {
                    is_blacklisted
                },
                {
                    where: { id },
                    transaction
                }
            );

            if (vendorData && is_blacklisted) {
                // fetch stones with offer created from stock offers
                await new Vendor().deleteBlacklistedStocks(id, transaction);
            }

            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: is_blacklisted ? 'Vendor blacklisted successfully' : 'Vendor removed from blacklist'
            });

            return;
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    async deleteBlacklistedStocks(id: any, transaction: any) {
        logger.info('!!!!!!BlacklistVendor function start!!!!!');
        try {
            const stockOffers = await models.stock_offers.findAll({
                where: {
                    [Op.and]: [
                        { vendor_id: id },
                        { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                    ]
                },
                attributes: ['id', 'stock_id'],
                raw: true,
                transaction
            });

            /// extract stock ids
            const stockIds = stockOffers?.map((item: any) => item?.stock_id).filter((item: any) => !!item);

            // delete all available stock of this vendor
            await models.stocks.update(
                { _deleted: true },
                {
                    where: {
                        [Op.and]: [
                            { vendor_id: id },
                            { status: stockStatus.available },
                            stockIds?.length ? { id: { [Op.notIn]: stockIds } } : {}
                        ]
                    },
                    transaction
                }
            );

            /// inactive policy violations
            await models.policy_violations.update({ is_active: false }, { where: { vendor_id: id }, transaction });

            ///
        } catch (error: any) {
            logger.error(error);
            throw error;
        }
    }

    /**
     * @api {get} /v1/auth/vendor/details
     * @apiName vendorDetails
     * @apiGroup Vendor
     *
     *
     * @apiSuccess {Object} Vendors.
     */
    async vendorDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!Vendor details function start!!!!!');
        try {
            const id: any = req[`id`];

            const vendor = await models.vendors.findOne({
                where: { id, _deleted: false },
                attributes: { exclude: ['password', 'otp'] }
            });

            if (!vendor) {
                throw new Error(`Vendor not found`);
            }

            if (vendor?.ftp_password) {
                const decryptedPassword = await decryptBody(vendor?.ftp_password);
                vendor.ftp_password = decryptedPassword;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendor details successfully listed',
                data: vendor
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor/verify
     *  @apiName VerifyVendor
     *  @apiGroup Vendor
     *
     *  @apiSuccess {Object} Vendor
     */
    async verifyVendor(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelize.transaction();

        try {
            const { id, is_verified, kyc_reject_reason, margin_percentage } = req.body;

            const dataObject: any = {};

            const vendor: any = await models.vendors.findOne({
                where: { id, _deleted: false }
            });

            if (!vendor) {
                throw new Error('Vendor not found');
            }

            dataObject.is_verified = is_verified;

            if (is_verified.toString().toLowerCase() === 'true' && vendor.is_verified) {
                throw new Error('User already verified!!!');
            }

            if (is_verified.toString().toLowerCase() === 'false' && !kyc_reject_reason) {
                throw new Error('Reject reason required!!!');
            }

            if (kyc_reject_reason) {
                dataObject.kyc_reject_reason = kyc_reject_reason;
                dataObject.document_name = null;
            }

            dataObject.margin_percentage = margin_percentage || 0;

            /// TODO update stocks when margin percentage updated

            await models.vendors.update(dataObject, {
                where: { id },
                transaction
            });

            // const vendorData = JSON.parse(JSON.stringify(vendor));

            // delete vendorData.id;

            // await models.user_details_trails.create({ ...vendorData, ...dataObject, user_id: id }, { transaction });

            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Vendor verification status updated'
            });

            try {
                /// send notification when KYC accepted
                if (is_verified.toString().toLowerCase() === 'true') {
                    userNotifications.sendKYCNotifications(NotificationType.KYCAccepted, null, id);
                } else if (is_verified.toString().toLowerCase() === 'false') {
                    userNotifications.sendKYCNotifications(NotificationType.KYCRejected, null, id);
                }
            } catch (error: any) {
                logger.error(error);
            }

            return;
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/vendor/fetch-api-stocks
     *  @apiName FetchAPIStocksVendor
     *  @apiGroup Vendor
     *
     *  @apiSuccess {Object} Vendor
     */
    async fetchAPIStocks(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;

            let resultData: any;

            if (!id) {
                throw new Error('id required!!!!');
            }

            const vendor: any = await models.vendors.findOne({
                where: { id, _deleted: false }
            });

            if (!vendor) {
                throw new Error('Vendor not found');
            }

            const isFetched: boolean = await new Promise(async (resolve, reject) => {
                try {
                    const url = vendor?.api_url;
                    logger.info(`!!!URL!!! ${url}`);

                    if (!url) {
                        throw new Error('API URL Not Found');
                    }

                    const config = {
                        headers: vendor?.headers ? JSON.parse(vendor?.headers) : {}
                    };

                    const data = typeof vendor?.body === 'string' ? JSON.parse(vendor?.body) : vendor?.body;

                    logger.info(`!!!config!!! ${JSON.stringify(config, null, 2)}`);
                    logger.info(`!!!data!!! ${JSON.stringify(data, null, 2)}`);
                    logger.info(`!!!Type!!! ${vendor?.api_type}`);

                    if (vendor?.is_formdata_request) {
                        logger.info(`!!!!!!!!!FormData Call!!!!!!!!!1`);
                        const formData = new FormData();
                        for (const item of Object.keys(data)) {
                            formData.append(item, data[item]);
                        }

                        const response = await axios.request({
                            url,
                            method: vendor?.api_type || 'post',
                            data: formData,
                            headers: config.headers
                        });

                        resultData = response?.data;
                        resolve(true);
                        return;
                    }

                    let apiRes;
                    switch (String(vendor?.api_type).toLowerCase()) {
                        case 'get':
                            apiRes = await axios.get(url, config);
                            break;
                        case 'post':
                            apiRes = await axios.post(url, data, config);
                            break;
                        default:
                            apiRes = await axios.get(url, config);
                            break;
                    }

                    resultData = apiRes?.data;
                    resolve(true);
                } catch (error) {
                    resultData = 'Data not found!!!';
                    logger.error(error);
                    resolve(false);
                }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock fetch from API successfully',
                data: resultData
            });

            if (!isFetched) {
                const message: any = `I hope this message finds you well. I am reaching out to inform you that we are currently experiencing an issue with the API that fetches stock data.<br><br>
<strong>API URL:</strong> ${vendor.api_url}<br><br>
The API is currently not responding, and we are unable to retrieve stock information. Could you please investigate the issue and provide an update? Your prompt assistance is greatly appreciated.<br><br>
Thank you.`;

                /// send mail
                mailServices.send({
                    to: String(vendor?.email).toLowerCase().trim(),
                    subject: 'Immediate Attention Required: Stock Fetch API Not Responding',
                    data: { name: `${vendor?.first_name} ${vendor?.last_name}`, message }
                });
            }

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Vendor();
