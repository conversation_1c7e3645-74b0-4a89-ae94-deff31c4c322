import { NextFunction, Request, Response } from 'express';
import models, { sequelize } from '../../models';
import { NotificationType, httpStatusCodes } from '../../utils/constants';
import { logger } from '../../utils/logger';
import bcrypt from 'bcrypt';
import { Op } from 'sequelize';
import { UserAttributes } from '../../models/users';
import userNotifications from '../user/user_notifications/user_notification';

class User {
    /*
        --------------------------------------------------------------------------------
        User functions
    */

    /**
     * @api {get} /v1/auth/admin/user
     * @apiName listUsers
     * @apiGroup AdminUsers
     *
     *
     * @apiSuccess {Object} Users.
     */
    async listUsers(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!listUsers function start!!!!!');
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            const isActive = req.query.is_active;
            const isVerified = req.query.is_verified;

            const q = req.query.q;
            const keyword = `%${q}%`;
            let whereClause: any = {};

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (q) {
                whereClause = {
                    [Op.or]: [
                        { first_name: { [Op.iLike]: keyword } },
                        { last_name: { [Op.iLike]: keyword } },
                        { email: { [Op.iLike]: keyword } },
                        { phone: { [Op.iLike]: keyword } }
                    ],
                    _deleted: false
                };
            } else {
                whereClause = {
                    _deleted: false
                };
            }
            if (isActive) {
                whereClause.is_active = isActive;
            }
            if (isVerified) {
                whereClause.is_verified = isVerified;
            }

            const { rows, count } = await models.users.findAndCountAll({
                where: whereClause,
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit,
                attributes: { exclude: ['password', 'otp'] }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Users successfully listed',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/user/details
     * @apiName userDetails
     * @apiGroup AdminUsers
     *
     *
     * @apiSuccess {Object} Users.
     */
    async userDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!listUsers function start!!!!!');
        try {
            const id: any = req.query.id;

            const user = await models.users.findOne({
                where: { id, _deleted: false },
                order: [['createdAt', 'DESC']],
                attributes: { exclude: ['password', 'otp'] }
            });

            if (!user) {
                throw new Error(`User not found`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User details successfully listed',
                data: user
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user
     *  @apiName addUser
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async addUser(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!AddUser function start!!!!!');
        try {
            const usersData = req.body;

            let email = req.body.email;
            const phone = req.body.phone;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            const userExists = await models.users.findOne({
                where: {
                    [Op.or]: [email ? { email } : {}, phone ? { phone } : {}],
                    _deleted: false
                }
            });

            if (userExists) {
                throw new Error('User already exists');
            }

            const hashPassword = await bcrypt.hash(req.body.password, 12);

            usersData.password = hashPassword;

            if (email) {
                usersData.email = email;
            }

            const user: UserAttributes = await models.users.create(usersData);

            const userData = JSON.parse(JSON.stringify(user));

            delete userData.password;
            delete userData.otp;

            if (user) {
                res.json({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: 'User created successfully!',
                    data: userData
                });
            }
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user
     *  @apiName updateUser
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async updateUser(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!UpdateUser function start!!!!!');
        try {
            const { id, first_name, last_name, username, phone, profile_image, address, state, city, country } =
                req.body;

            let email = req.body.email;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            const userExists = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!userExists) {
                throw new Error('User not found');
            }

            const dataObject: any = {};

            if (first_name) {
                dataObject.first_name = first_name;
            }
            if (last_name) {
                dataObject.last_name = last_name;
            }
            if (email) {
                dataObject.email = email;
            }
            if (username) {
                dataObject.username = username;
            }
            if (phone) {
                dataObject.phone = phone;
            }
            if (profile_image) {
                dataObject.profile_image = profile_image;
            }
            if (address) {
                dataObject.address = address;
            }
            if (state) {
                dataObject.state = state;
            }
            if (city) {
                dataObject.city = city;
            }
            if (country) {
                dataObject.country = country;
            }

            await models.users.update(dataObject, {
                where: { id }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User updated successfully!'
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user
     *  @apiName deleteUser
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async deleteUser(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!DeleteUser function start!!!!!');
        try {
            const id = req.query.id;

            const userExists = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!userExists) {
                throw new Error('User not found');
            }

            await models.users.update(
                {
                    _deleted: true
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User deleted successfully!'
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user/status
     *  @apiName changeUserStatus
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async changeUserStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_active } = req.body;

            const userExists = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!userExists) {
                throw new Error('User not found');
            }

            await models.users.update(
                {
                    is_active
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Updated User Status'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user/block
     *  @apiName blockUser
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async blockUser(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_blocked } = req.body;

            const userExists = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!userExists) {
                throw new Error('User not found');
            }

            await models.users.update(
                {
                    is_blocked
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: is_blocked ? 'User blocked successfully' : 'User unblocked successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user/verify
     *  @apiName VerifyUser
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async verifyUser(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelize.transaction();

        try {
            const { id, is_verified, credit_limit, kyc_reject_reason } = req.body;

            const dataObject: any = {};

            const user: any = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!user) {
                throw new Error('User not found');
            }

            dataObject.is_verified = is_verified;

            if (is_verified.toString().toLowerCase() === 'true' && user.is_verified) {
                throw new Error('User already verified!!!');
            }

            if (is_verified.toString().toLowerCase() === 'true') {
                if (credit_limit) {
                    if (!user?.trade_references?.length) {
                        throw new Error('Trade references not provided!!!');
                    }
                    dataObject.credit_limit = credit_limit;
                }
            }

            if (is_verified.toString().toLowerCase() === 'false' && !kyc_reject_reason) {
                throw new Error('Reject reason required!!!');
            }

            if (kyc_reject_reason) {
                dataObject.kyc_reject_reason = kyc_reject_reason;
                dataObject.document_name = null;
            }

            await models.users.update(dataObject, {
                where: { id },
                transaction
            });

            if (credit_limit && is_verified.toString().toLowerCase() === 'true') {
                /// total credit
                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: id
                    },
                    transaction
                });

                /// total debit
                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: id
                    },
                    transaction
                });

                /// available credit
                const availableCredit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                if (parseFloat(parseFloat(credit_limit).toFixed(2)) + parseFloat(availableCredit.toFixed(2)) < 0) {
                    throw new Error('Credit limit can not be less than zero!!');
                }

                await models.credit_histories.create(
                    {
                        user_id: id,
                        credit: credit_limit,
                        type: 'VERIFY',
                        transaction_type: 'CREDIT'
                    },
                    { transaction }
                );
            }

            const userData = JSON.parse(JSON.stringify(user));

            delete userData.id;

            await models.user_details_trails.create({ ...userData, ...dataObject, user_id: id }, { transaction });

            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User verification status updated'
            });

            try {
                /// send notification when KYC accepted
                if (is_verified.toString().toLowerCase() === 'true') {
                    userNotifications.sendKYCNotifications(NotificationType.KYCAccepted, id);

                    if (credit_limit) {
                        /// send notification when credit added
                        userNotifications.sendCreditLimitNotifications(NotificationType.creditLimitAdded, id);
                    }
                } else if (is_verified.toString().toLowerCase() === 'false') {
                    userNotifications.sendKYCNotifications(NotificationType.KYCRejected, id);
                }
            } catch (error: any) {
                logger.error(error);
            }

            return;
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user/update-credit-limit
     *  @apiName UpdateCreditLimit
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async updateCreditLimit(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelize.transaction();

        try {
            const { id, credit_limit } = req.body;

            const user = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (!user.is_verified) {
                throw new Error('User is not verified');
            }

            /// total credit
            const totalCredit = await models.credit_histories.sum('credit', {
                where: {
                    transaction_type: 'CREDIT',
                    user_id: id
                },
                transaction
            });

            /// total debit
            const totalDebit = await models.credit_histories.sum('credit', {
                where: {
                    transaction_type: 'DEBIT',
                    user_id: id
                },
                transaction
            });

            /// available credit
            const availableCredit =
                parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

            if (parseFloat(parseFloat(credit_limit).toFixed(2)) + parseFloat(availableCredit.toFixed(2)) < 0) {
                throw new Error('Credit limit can not be less than zero!!');
            }

            await models.users.update({ credit_limit: availableCredit }, { where: { id }, transaction });

            await models.credit_histories.create(
                {
                    user_id: id,
                    credit: credit_limit,
                    type: 'UPDATE',
                    transaction_type: 'CREDIT'
                },
                { transaction }
            );

            await transaction.commit();

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Credit limit updated successfully'
            });

            try {
                /// send notification when credit added
                userNotifications.sendCreditLimitNotifications(NotificationType.creditLimitAdded, id);
            } catch (error: any) {
                logger.error(error);
            }

            return;
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/user/list-users-details
     *  @apiName ListUsersDetails
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */
    async listUsersDetails(req: Request, res: Response, next: NextFunction) {
        try {
            const { ids } = req.body;

            if (!ids.length) {
                return res.json({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: 'User details listed successfully',
                    data: [],
                    count: 0
                });
            }

            const { rows, count } = await models.users.findAndCountAll({
                where: { id: { [Op.in]: ids } },
                order: [['createdAt', 'DESC']],
                attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
                exclude: ['password', 'otp']
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User details listed successfully',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new User();
