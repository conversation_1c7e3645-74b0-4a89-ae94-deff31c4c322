import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface WishlistStockAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    stock_id: string;
    final_price: number;
    status: string;
    vendor_id: string;
    admin_id: string;
    updatedAt?: Date;
    _deleted: boolean;
    is_active: boolean;
}

interface WishlistStockCreationAttributes extends Optional<WishlistStockAttributes, 'id'> {}

interface WishlistStockInstance
    extends Model<WishlistStockAttributes, WishlistStockCreationAttributes>,
        WishlistStockAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type WishlistStockStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => WishlistStockInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const wishlistStock = sequelize.define<WishlistStockInstance>(
        'wishlist_stocks',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            stock_id: {
                type: DataTypes.STRING,
                allowNull: false
            },
            final_price: {
                type: DataTypes.DOUBLE,
                allowNull: false
            },
            status: {
                type: DataTypes.ENUM('AVAILABLE', 'HOLD', 'SOLD', 'MEMO'),
                allowNull: false
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            }
        },
        {
            freezeTableName: true
        }
    ) as WishlistStockStatic;

    wishlistStock.associate = (models) => {
        wishlistStock.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        wishlistStock.belongsTo(models.stocks, {
            foreignKey: 'stock_id',
            targetKey: 'stock_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    //
    // await wishlistStock.sync({alter: true});

    return wishlistStock;
};
