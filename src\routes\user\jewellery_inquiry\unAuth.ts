import { IRouter, NextFunction, Request, Response, Router } from 'express';
import jewelleryInquiry from '../../../controllers/admin/jewellery_inquiry';
import { fieldsValidator } from '../../../middlewares/validator';
import { createJewelleryInquirySchema } from '../../../utils/schema/jewellery_inquiry_schema';

const routes: IRouter = Router();

// create inquiry
routes.post(
    '/create-inquiry',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createJewelleryInquirySchema),
    jewelleryInquiry.createInquiry
);

export default routes;
