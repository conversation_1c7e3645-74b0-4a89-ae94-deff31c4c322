import staticPages from '../../../controllers/admin/static_pages';
import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    changeStaticPageStatusSchema,
    createStaticPageSchema,
    updateStaticPageSchema
} from '../../../utils/schema/static_pages_schema';

const router: IRouter = Router();

// list static page
router.get('/static-page', staticPages.listStaticPages);

// get static page
router.get('/static-page-details', staticPages.getStaticPage);

// add static page
router.post(
    '/static-page',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createStaticPageSchema),
    staticPages.addStaticPages
);

// change static page
router.put(
    '/static-page',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateStaticPageSchema),
    staticPages.updateStaticPages
);

// change static page status
router.put(
    '/static-page-status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeStaticPageStatusSchema),
    staticPages.changeStaticPageStatus
);

// delete static page
router.delete('/static-page', staticPages.deleteStaticPages);

export default router;
