import models from '../../../models';
import { logger } from '../../../utils/logger';
import { Op } from 'sequelize';
import mailServices from './mail_services';

class CustomEmails {
    /// send email for stocks not uploaded
    async sendEmailToVendorForZeroPriceStocks(
        zeroPriceStocks: any,
        stocksWithoutCertificate: any,
        stocksWithoutLab: any,
        stocksWithNaturalDiamond: any
    ) {
        /// send email to vendor to raise invoice
        try {
            /// vendor ids

            const totalFailedStocks = [...zeroPriceStocks, ...stocksWithoutCertificate, ...stocksWithoutLab, stocksWithNaturalDiamond];
            const totalVendorIds = totalFailedStocks?.map((item: any) => item?.vendor_id);
            logger.info(`!!!!!!totalVendorIds!!!!!!!!! ${totalVendorIds}`);

            const uniqueVendorIds: any = [];
            for (const vendorIdItem of totalVendorIds) {
                if (!uniqueVendorIds.some((ut) => ut === vendorIdItem)) {
                    uniqueVendorIds.push(vendorIdItem);
                }
            }

            logger.info(`!!!!!!uniqueVendorIds!!!!!!!! ${JSON.stringify(uniqueVendorIds, null, 2)}`);

            const vendors: any = await models.vendors.findAll({
                where: { id: { [Op.in]: uniqueVendorIds } }
            });

            for (const vendor of vendors) {
                /// vendor stocks
                const vendorStocksWithZero: any = zeroPriceStocks.filter(
                    (stock: any) => stock?.vendor_id === vendor?.id
                );
                const vendorStocksWithNoCertificate: any = stocksWithoutCertificate.filter(
                    (stock: any) => stock?.vendor_id === vendor?.id
                );
                const vendorStocksWithNoLab: any = stocksWithoutLab.filter(
                    (stock: any) => stock?.vendor_id === vendor?.id
                );
                const vendorStocksWithNaturalDiamond: any = stocksWithNaturalDiamond.filter(
                    (stock: any) => stock?.vendor_id === vendor?.id
                );

                /// send email
                try {
                    /// email body
                    const emailMessage = `I hope this message finds you well. We wanted to inform you about an issue encountered during the upload of your stock data to our platform.<br>
Some of your stock items could not be uploaded because their prices are set to zero (0) or certificate number is not entered or lab is not entered. Our system requires all items to have a valid price or valid certificate number or valid lab to ensure accurate listings and compliance with platform policies.<br>
Affected Stock Details With Zero Price: ${vendorStocksWithZero.map((item: any) => item?.stock_id).join(', ')}<br><br>
Affected Stock Details With Invalid Certificate Number: ${vendorStocksWithNoCertificate
                        .map((item: any) => item?.stock_id)
                        .join(', ')}<br><br>
Affected Stock Details With No Lab: ${vendorStocksWithNoLab.map((item: any) => item?.stock_id).join(', ')}<br><br>
Affected Stock Details With Natural Diamond: ${vendorStocksWithNaturalDiamond.map((item: any) => item?.stock_id).join(', ')}<br>`;

                    logger.info(`!!!!!EmailMessage!!!!!!!! ${emailMessage}}`);

                    if (vendor?.email) {
                        /// send mail
                        // try {
                        //     mailServices.send({
                        //         to: vendor?.email,
                        //         subject: 'Stock Upload Alert: Zero Price Detected for Some Items',
                        //         data: { name: `${vendor?.first_name} ${vendor?.last_name}`, message: emailMessage }
                        //     });
                        //     logger.info(`!!!!!!!!!!Mail Sent SuccessFully!!!!!!!! ${vendor?.email}`);
                        // } catch (error: any) {
                        //     logger.error(`!!!!!!!Error Sending Mail!!!!!!!!!!! ${error}`);
                        // }

                        // try {
                        //     mailServices.send({
                        //         to: '<EMAIL>',
                        //         subject: 'Stock Upload Alert: Zero Price Detected for Some Items',
                        //         data: { name: `${vendor?.first_name} ${vendor?.last_name}`, message: emailMessage }
                        //     });
                        //     logger.info(`!!!!!!!!!!Mail Sent SuccessFully to admin!!!!!!!!`);
                        // } catch (error: any) {
                        //     logger.error(`!!!!!!!Error Sending Mail To Admin!!!!!!!!!!! ${error}`);
                        // }
                    }

                    ///
                } catch (e) {
                    logger.error(e);
                }
            }
        } catch (e) {
            logger.error(e);
        }
    }
}

export default new CustomEmails();
