import Joi from 'joi';

export const createUnifiedOrderSchema = Joi.object({
    // id: Joi.string().uuid().required(),
    // user_id: Joi.string().uuid().required(),
    diamonds: Joi.object({
        stock_ids: Joi.array()
            .items(
                Joi.object({
                    stock_id: Joi.string().uuid().required(),
                    is_available: Joi.boolean().default(true).optional(),
                    is_action_taken: Joi.boolean().default(false).optional(),
                    vendor_id: Joi.string().uuid().optional(),
                    admin_id: Joi.string().uuid().optional()
                }).or('vendor_id', 'admin_id')
            )
            .min(1)
            .required(),
        total_amount: Joi.number().required()
    }).optional(),
    melee: Joi.object({
        melee_array: Joi.array()
            .items(
                Joi.object({
                    data: Joi.object().required(),
                    price_per_carat: Joi.number().required(),
                    quantity: Joi.number().required(),
                    total_amount: Joi.number().required()
                })
            )
            .min(1)
            .required(),
        total_amount: Joi.number().required()
    }).optional(),
    jewellery: Joi.object({
        jewellery_array: Joi.array()
            .items(
                Joi.object({
                    quantity: Joi.number().required(),
                    data: Joi.object().required(),
                    variant_id: Joi.string().required(),
                    product_id: Joi.string().required(),
                    total_amount: Joi.number().required()
                })
            )
            .min(1)
            .required(),
        total_amount: Joi.number().required()
    }).optional(),
    // vendor_ids: Joi.array().items(Joi.string().uuid()),
    // status: Joi.string().valid('PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED').required(),
    // updated_by_id: Joi.string().uuid().required(),
    payment_mode: Joi.string().valid('CREDIT_LIMIT', 'CREDIT_CARD', 'APPLE_PAY').required(),
    shipping_address: Joi.object().required(),
    billing_address: Joi.object().required(),
    total_amount: Joi.number().required(),
    shipment_id: Joi.string().uuid().required(),
    shipment_price: Joi.number().required(),
    card_holder_name: Joi.string()
        .regex(/^[A-Z\s]+$/)
        .uppercase()
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.empty': 'Card holder name cannot be empty',
            'string.pattern.base': 'Card holder name must only contain uppercase letters and spaces'
        }),
    card_number: Joi.string().creditCard().when('payment_mode', {
        is: 'CREDIT_CARD',
        then: Joi.required()
    }),
    exp_date: Joi.string()
        .pattern(/^(20[2-9][0-9]|2100)-(0[1-9]|1[0-2])$/)
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.pattern.base': 'Expiration Date must be in YYYY-MM format'
        }),
    cvv: Joi.string()
        .pattern(/^[0-9]{3,4}$/)
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.pattern.base': 'CVV must be 3 or 4 digits'
        }),
    apple_pay_response: Joi.object().when('payment_mode', {
        is: 'APPLE_PAY',
        then: Joi.required()
    })
    // ref_trans_id: Joi.string().optional(),
    // trans_id: Joi.string().optional(),
    // transaction_payload: Joi.string().optional()
    // shipping_address: Joi.object({
    //     street: Joi.string().required(),
    //     city: Joi.string().required(),
    //     state: Joi.string().required(),
    //     postalCode: Joi.string().required(),
    //     country: Joi.string().required()
    // }).required(),
    // billing_address: Joi.object({
    //     street: Joi.string().required(),
    //     city: Joi.string().required(),
    //     state: Joi.string().required(),
    //     postalCode: Joi.string().required(),
    //     country: Joi.string().required()
    // }).required()
    // reject_reason: Joi.string().required()
});
