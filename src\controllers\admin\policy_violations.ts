import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import {
    httpStatusCodes,
    adminRole,
    ViolationType,
    vendorOrderStatus,
    buyRequestType,
    ReturnOrderStatus
} from '../../utils/constants';
import models from '../../models';
import { Op, Sequelize } from 'sequelize';
import { PolicyViolationsAttributes } from '../../models/policy_violations';
import moment from 'moment';
import mailServices from '../user/user_notifications/policy_violation_email_service';
import vendorController from './vendor';

class PolicyViolations {
    /**
     * @api {get} /v1/auth/admin/policy-violations
     * @apiName listPolicyViolations
     * @apiGroup PolicyViolations
     *
     * @apiQuery {Number} [page=1] Page number
     * @apiQuery {Number} [limit=10] Items per page
     * @apiQuery {String} [violation_type] Filter by violation type
     * @apiQuery {String} [vendor_id] Filter by vendor ID
     *
     * @apiSuccess {Object} PolicyViolations list with pagination
     */
    async listPolicyViolations(req: Request, res: Response, next: NextFunction) {
        try {
            logger.info('!!!Calling list policy violations');
            const role = req[`role`];
            const id = req[`id`];

            const violationType = req.query.violation_type;
            const vendorId = req.query.vendor_id;
            const offset = parseInt(String(req.query.skip ?? 0), 10);
            const limit = parseInt(String(req.query.limit ?? 10), 10);

            const conditions: any = [];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error('Unauthorized access');
            }

            if (violationType) {
                conditions.push({ violation_type: violationType });
            }

            if (vendorId) {
                conditions.push({ vendor_id: vendorId });
            }

            /// fetch all vendors
            const { rows, count } = await models.policy_violations.findAndCountAll({
                attributes: [[Sequelize.fn('DISTINCT', Sequelize.col('vendor_id')), 'vendor_id']],
                where: { is_active: true },
                raw: true
            });

            /// vendor ids
            const vendorIds = rows?.map((item: any) => item?.vendor_id);

            /// fetch vendors
            const vendors = await models.vendors.findAll({
                where: { id: { [Op.in]: vendorIds } },
                raw: true,
                attributes: ['id', 'first_name', 'last_name', 'email', 'company_name', 'is_blacklisted']
            });

            /// add vendors and stats in result
            for (const row of rows) {
                /// fetch vendor
                const vendor = vendors?.find((item: any) => item?.id === row?.vendor_id);

                /// fetch stats
                const stats = await new PolicyViolations().getVendorPolicyViolationStatistics(row?.vendor_id);

                row.vendor = vendor;
                row.stats = stats;
            }

            /// Sort by total violations
            rows.sort((a, b) => b?.stats?.total_violations - a?.stats?.total_violations);

            // Paginate manually
            const paginatedResults = rows.slice(offset, offset + limit);

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Policy violations retrieved successfully',
                data: paginatedResults,
                count: paginatedResults?.length ?? 0
            });

            ///
        } catch (error: any) {
            logger.error(`Error listing policy violations: ${error.message}`);
            next(error);
        }
    }

    async getVendorPolicyViolationStatistics(vendorId: any) {
        try {
            if (!vendorId) {
                return;
            }

            /// fetch duplicate diamond count
            const duplicateDiamondCount =
                (await models.policy_violations.sum('duplicate_violation_count', {
                    where: {
                        vendor_id: vendorId,
                        violation_type: ViolationType.DUPLICATE_DIAMOND,
                        is_active: true
                    }
                })) ?? 0;

            /// fetch unfulfilled order count
            const unfulfilledOrderCount =
                (await models.policy_violations.count({
                    where: { vendor_id: vendorId, violation_type: ViolationType.UNFULFILLED_ORDER, is_active: true }
                })) ?? 0;

            /// fetch late shipment count
            const lateShipmentCount =
                (await models.policy_violations.count({
                    where: { vendor_id: vendorId, violation_type: ViolationType.LATE_SHIPMENT, is_active: true }
                })) ?? 0;

            /// fetch vendor orders
            const vendorOrders = await models.vendor_orders.findAll({
                where: { vendor_id: vendorId, status: vendorOrderStatus.paid },
                attributes: ['id', 'order_id', 'vendor_id', 'status'],
                include: [
                    { model: models.orders, attributes: ['id', 'createdAt', 'type'] },
                    { model: models.stocks, attributes: ['id', 'final_price'] }
                ]
            });

            /// update vendor order prices
            for (const vendorOder of vendorOrders) {
                /// update final price for offer type
                if (vendorOder?.order?.type === buyRequestType.offer) {
                    /// fetch offer
                    const offer: any = await models.stock_offers.findOne({
                        where: { stock_id: vendorOder?.stock?.id }
                    });
                    if (offer) {
                        vendorOder.stock.final_price = offer.offer_price;
                    }
                }
            }

            /// calculate sold diamonds
            const soldDiamondCount = vendorOrders?.length ?? 0;

            /// calculate sold value
            const soldValue = vendorOrders.reduce((sum, order) => sum + parseFloat(order?.stock?.final_price ?? 0), 0);

            /// fetch return diamond count
            const returnDiamondCount =
                (await models.return_orders.count({
                    where: {
                        [Op.and]: [{ vendor_id: vendorId }, { is_accepted: true }]
                    }
                })) ?? 0;

            // calculate unfulfillment ratio
            const unfulfillmentRatio = soldDiamondCount > 0 ? (unfulfilledOrderCount / soldDiamondCount) * 100 : 0;

            // calculate fulfillment ratio
            const fulfillmentRatio =
                soldDiamondCount > 0 ? ((soldDiamondCount - unfulfilledOrderCount) / soldDiamondCount) * 100 : 0;

            // calculate return ratio
            const returnRatio = soldDiamondCount > 0 ? (returnDiamondCount / soldDiamondCount) * 100 : 0;

            return {
                unfulfillment_ratio: Math.round(unfulfillmentRatio * 100) / 100,
                fulfillment_ratio: Math.round(fulfillmentRatio * 100) / 100,
                duplicate_diamond_count: duplicateDiamondCount,
                unfulfilled_order_count: unfulfilledOrderCount,
                return_diamond_count: returnDiamondCount,
                return_ratio: Math.round(returnRatio * 100) / 100,
                late_shipment_count: lateShipmentCount,
                sold_diamond_count: soldDiamondCount,
                sold_value: Math.round(soldValue * 100) / 100,
                total_violations: duplicateDiamondCount + unfulfilledOrderCount + lateShipmentCount
            };

            ///
        } catch (error: any) {
            logger.error(`Error getting policy violation statistics: ${error.message}`);
            return;
        }
    }

    /**
     * @api {get} /v1/auth/admin/policy-violations/dashboard
     * @apiName getPolicyViolationDashboard
     * @apiGroup PolicyViolations
     *
     * @apiSuccess {Object} Dashboard data with violation counts and trends
     */
    async getPolicyViolationDashboard(req: Request, res: Response, next: NextFunction) {
        try {
            logger.info('!!!Calling get policy violation dashboard');
            const role = req[`role`];
            const id = req[`id`];

            if (![adminRole.superAdmin, adminRole.subAdmin, adminRole.vendor].includes(role)) {
                throw new Error('Unauthorized access');
            }

            /// get total duplicate diamond count
            const duplicateDiamondCount = await models.policy_violations.sum('duplicate_violation_count', {
                where: {
                    [Op.and]: [
                        { violation_type: ViolationType.DUPLICATE_DIAMOND },
                        { is_active: true },
                        role === adminRole.vendor ? { vendor_id: id } : {}
                    ]
                }
            });

            /// get total unfulfilled order count
            const unfulfilledOrderCount = await models.policy_violations.count({
                where: {
                    [Op.and]: [
                        { violation_type: ViolationType.UNFULFILLED_ORDER },
                        { is_active: true },
                        role === adminRole.vendor ? { vendor_id: id } : {}
                    ]
                }
            });

            /// get total late shipment count
            const lateShipmentCount = await models.policy_violations.count({
                where: {
                    [Op.and]: [
                        { violation_type: ViolationType.LATE_SHIPMENT },
                        { is_active: true },
                        role === adminRole.vendor ? { vendor_id: id } : {}
                    ]
                }
            });

            // get top violating vendors
            const totalViolatingVendors =
                role === adminRole.vendor
                    ? undefined
                    : await models.policy_violations.count({
                          where: { is_active: true },
                          distinct: true,
                          col: 'vendor_id'
                      });

            const dashboardData = {
                total_violations: duplicateDiamondCount ?? 0 + unfulfilledOrderCount ?? 0 + lateShipmentCount ?? 0,
                total_duplicate_violations: duplicateDiamondCount ?? 0,
                total_unfulfilled_violations: unfulfilledOrderCount ?? 0,
                total_late_shipment_violations: lateShipmentCount ?? 0,
                total_violating_vendors: totalViolatingVendors
                // active_violations: recentViolations.total
            };

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Policy violation dashboard data retrieved successfully',
                data: dashboardData
            });

            ///
        } catch (error: any) {
            logger.error(`Error getting policy violation dashboard: ${error.message}`);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/policy-violations/details
     * @apiName getPolicyViolationDetails
     * @apiGroup PolicyViolations
     *
     * @apiSuccess {Object} Dashboard data with violation counts and trends
     */
    async getPolicyViolationDetails(req: Request, res: Response, next: NextFunction) {
        try {
            logger.info('!!!Calling get policy violation details');
            const role = req[`role`];
            const id = req[`id`];

            if (![adminRole.superAdmin, adminRole.subAdmin, adminRole.vendor].includes(role)) {
                throw new Error('Unauthorized access');
            }

            /// vendor id
            const vendorId = role === adminRole.vendor ? id : req.query.vendor_id;

            if (!vendorId) {
                throw new Error('Vendor id required!!!');
            }

            /// list all violations for vendor
            const violations = await models.policy_violations.findAll({
                where: { vendor_id: vendorId },
                order: [['createdAt', 'DESC']],
                raw: true
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Policy violation dashboard data retrieved successfully',
                data: violations
            });

            ///
        } catch (error: any) {
            logger.error(`Error getting policy violation dashboard: ${error.message}`);
            next(error);
        }
    }

    /**
     * Create a new policy violation
     */
    async createViolation(violationList: PolicyViolationsAttributes[]): Promise<any> {
        try {
            logger.info(`Creating policy violation: ${JSON.stringify(violationList)}`);

            if (!violationList?.length) {
                return;
            }

            /// bulk create violations object
            const bulkCreateObject = violationList.map((item: any) => ({
                vendor_order_id: item?.vendor_order_id,
                vendor_id: item?.vendor_id,
                violation_type: item?.violation_type,
                stock_id: item?.stock_id,
                order_id: item?.order_id,
                details: item?.details || {}
            }));

            await models.policy_violations.bulkCreate(bulkCreateObject, { ignoreDuplicates: true });

            for (const violation of violationList) {
                /// update last_violation_at and violation_count
                await models.vendors.update(
                    {
                        last_violation_at: moment.utc(),
                        violation_count: Sequelize.literal(`violation_count + 1`)
                    },
                    {
                        where: {
                            [Op.and]: [
                                { id: violation?.vendor_id },

                                /// update once a day for duplicate diamond
                                ...(violation?.violation_type === ViolationType.DUPLICATE_DIAMOND
                                    ? [
                                          {
                                              [Op.or]: [
                                                  {
                                                      last_violation_at: {
                                                          [Op.lt]: moment.utc().startOf('day').toDate()
                                                      }
                                                  },
                                                  {
                                                      last_violation_at: {
                                                          [Op.is]: null
                                                      }
                                                  }
                                              ]
                                          }
                                      ]
                                    : [])
                            ]
                        }
                    }
                );
            }

            logger.info(`Policy violation created`);
        } catch (error) {
            logger.error(`Error creating policy violation: ${error}`);
            throw error;
        }
    }

    /**
     * Check for duplicate diamonds and create violation if found
     */
    async checkDuplicateDiamond(createdStockObjects: any[], vendorId?: any, adminId?: any): Promise<void> {
        try {
            logger.info(`Checking duplicate diamond: ${JSON.stringify(createdStockObjects)}`);
            if (!createdStockObjects?.length) {
                return;
            }

            /// list certificate number is already exists with different vendor or admin
            const checkExistingCertificates = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        {
                            certificate_number: {
                                [Op.in]: createdStockObjects?.map((item: any) => item?.certificate_number)
                            }
                        },
                        {
                            [Op.or]: [
                                vendorId ? { vendor_id: { [Op.ne]: vendorId } } : {},
                                adminId ? { admin_id: { [Op.ne]: adminId } } : {}
                            ]
                        }
                    ]
                },
                attributes: ['certificate_number', 'vendor_id', 'stock_id'],
                raw: true
            });

            /// extract duplicate certificates using stocklist and checkExistingCertificates
            const duplicateCertificates = createdStockObjects?.filter((item: any) =>
                checkExistingCertificates?.map((s: any) => s.certificate_number).includes(item.certificate_number)
            );

            if (!duplicateCertificates.length) {
                throw new Error('Duplicate certificates not found');
            }

            // Check for duplicate diamonds based on certificate number
            if (duplicateCertificates.length) {
                /// create violation
                const violationList: PolicyViolationsAttributes[] = duplicateCertificates
                    .map((item: any) => {
                        /// find stock
                        const foundStock = createdStockObjects.find(
                            (stock: any) => stock.certificate_number === item.certificate_number
                        );

                        return {
                            vendor_id: vendorId,
                            violation_type: ViolationType.DUPLICATE_DIAMOND,
                            stock_id: item.stock_id,
                            details: {
                                // certificate_number: item.certificate_number,
                                // stock_id: item.stock_id,
                                // vendor_id: item.vendor_id,
                                stock: JSON.parse(JSON.stringify(foundStock)) ?? {}
                            }
                        };
                    })
                    .filter((data) => !!data) as PolicyViolationsAttributes[];

                /// update duplicate_violation_count
                /// update once a day
                await models.policy_violations.update(
                    { duplicate_violation_count: Sequelize.literal('duplicate_violation_count + 1') },
                    {
                        where: {
                            [Op.and]: [
                                { violation_type: ViolationType.DUPLICATE_DIAMOND },
                                { vendor_id: vendorId },
                                {
                                    stock_id: {
                                        [Op.in]: duplicateCertificates
                                            ?.map((item: any) => item?.stock_id)
                                            ?.filter((data: any) => !!data)
                                    }
                                },
                                {
                                    updatedAt: {
                                        [Op.lt]: moment.utc().startOf('day').toDate()
                                    }
                                },
                                { is_active: true }
                            ]
                        }
                    }
                );

                /// create violation
                await this.createViolation(violationList);

                /// send email
                try {
                    /// send violation email
                    await mailServices.sendViolationNotification(violationList, vendorId);

                    ///
                } catch (error: any) {
                    logger.error(`Error sending duplicate diamond email: ${error}`);
                    // Don't throw error to avoid breaking the stock creation process
                }
            }
        } catch (error) {
            logger.error(`Error checking duplicate diamond: ${error}`);
            throw error;
        }
    }

    /**
     * Create unfulfilled order violation
     */
    async createUnfulfilledOrderViolation(order_id: string, return_stock_ids: any[]): Promise<void> {
        logger.info(`Creating unfulfilled order violation: ${return_stock_ids?.length}`);
        try {
            if (!return_stock_ids.length) {
                return;
            }

            /// fetch stock_ids
            const stockIds = return_stock_ids?.map((item: any) => item?.stock_id).filter((data: any) => !!data);

            /// fetch stocks
            const stocks = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'discounts_ori',
                    'price_per_caret',
                    'price_per_caret_ori',
                    'final_price',
                    'final_price_ori',
                    'admin_id',
                    'vendor_id',
                    'sku_number'
                ]
            });

            const violationList: PolicyViolationsAttributes[] = return_stock_ids?.map((item: any) => {
                /// find stock
                const foundStock = stocks.find((stock: any) => stock.id === item?.stock_id);

                /// create violation
                return {
                    vendor_id: item?.vendor_id,
                    violation_type: ViolationType.UNFULFILLED_ORDER,
                    order_id,
                    stock_id: foundStock?.stock_id,
                    details: {
                        reason: item?.reason,
                        stock: JSON.parse(JSON.stringify(foundStock)) ?? {}
                    }
                };
            });

            /// create violation
            await this.createViolation(violationList);
        } catch (error) {
            logger.error(`Error creating unfulfilled order violation: ${error}`);
            throw error;
        }
    }

    /**
     * Check for late shipment and create violation if applicable
     */
    async checkLateShipment(vendor_order_ids: any[]): Promise<void> {
        logger.info(`Checking late shipment for vendor order ids: ${JSON.stringify(vendor_order_ids)}`);
        try {
            /// fetch vendor order
            const vendorOrders = await models.vendor_orders.findAll({
                where: { id: vendor_order_ids },
                attributes: ['id', 'order_id', 'vendor_id', 'stock_id', 'status', 'createdAt'],
                include: [
                    { model: models.orders, attributes: ['id', 'createdAt'] },
                    {
                        model: models.vendors,
                        attributes: ['id', 'first_name', 'last_name', 'email', 'company_name']
                    }
                ]
            });

            if (!vendorOrders.length) {
                return;
            }

            /// fetch stocks
            const stockIds = vendorOrders.map((item: any) => item?.stock_id).filter((data: any) => !!data);

            const stocks = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'discounts_ori',
                    'price_per_caret',
                    'price_per_caret_ori',
                    'final_price',
                    'final_price_ori',
                    'admin_id',
                    'vendor_id',
                    'sku_number'
                ]
            });

            /// fetch order details id, order_code, user_id,
            const orderIds = [
                ...new Set(vendorOrders.map((item: any) => item?.order_id).filter((data: any) => !!data))
            ];

            const orders = await models.orders.findAll({
                where: { id: { [Op.in]: orderIds } },
                attributes: ['id', 'order_code', 'user_id']
            });

            const lateVendorOrders: any[] = vendorOrders
                .map((item: any) => {
                    /// calculate hours difference
                    const orderCreatedAt = moment.utc(item?.createdAt);
                    const now = moment.utc();

                    const hoursDiff = now.diff(orderCreatedAt, 'hours', true); // true = get fractional hours

                    if (hoursDiff > 24 && item.status === vendorOrderStatus.shipped) {
                        /// found stock
                        const foundStock = stocks.find((stock: any) => stock.id === item?.stock_id);

                        if (!foundStock) {
                            return;
                        }

                        /// found order
                        const foundOrder = orders.find((order: any) => order.id === item?.order_id);

                        if (!foundOrder) {
                            return;
                        }

                        return {
                            vendor_order_id: item.id,
                            vendor_id: item.vendor_id,
                            violation_type: ViolationType.LATE_SHIPMENT,
                            order_id: item.order_id,
                            stock_id: foundStock?.stock_id,
                            details: {
                                hours_delayed: Math.round(hoursDiff),
                                order_created_at: orderCreatedAt,
                                shipped_at: now,
                                order: JSON.parse(JSON.stringify(foundOrder)) ?? {},
                                stock: JSON.parse(JSON.stringify(foundStock)) ?? {}
                            }
                        };
                    }
                })
                .filter((data: any) => !!data);

            if (!lateVendorOrders.length) {
                return;
            }

            /// create violation
            await this.createViolation(lateVendorOrders);

            ///
        } catch (error) {
            logger.error(`Error checking late shipment: ${error}`);
            throw error;
        }
    }

    /**
     * check violation count exceeds threshold from vendor table and auto black list vendor
     */
    async checkViolationThreshold() {
        logger.info('Checking policy violation threshold');
        const transaction = await models.sequelize.transaction();
        try {
            /// check last_violation_at and reset violation count to 0 after 6 month
            await models.vendors.update(
                { violation_count: 0 },
                {
                    where: {
                        [Op.and]: [
                            { violation_count: { [Op.gt]: 0 } },
                            {
                                last_violation_at: {
                                    [Op.lt]: moment.utc().subtract(6, 'months').toDate()
                                }
                            }
                        ]
                    }
                }
            );

            /// fetch vendors
            const vendors = await models.vendors.findAll({
                where: {
                    [Op.and]: [
                        { violation_count: { [Op.gte]: process.env.VIOLATION_THRESHOLD ?? 10000 } },
                        { is_blacklisted: false },
                        { is_active: true }
                    ]
                },
                attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'],
                raw: true
            });

            if (!vendors?.length) {
                throw new Error('No vendors found');
            }

            /// blacklist vendors
            await models.vendors.update(
                { violation_count: 0, is_blacklisted: true },
                { where: { id: { [Op.in]: vendors?.map((item: any) => item?.id) } }, transaction }
            );

            /// call blacklistVendor from vendor controller
            for (const vendor of vendors ?? []) {
                /// vendor blacklist
                await vendorController.deleteBlacklistedStocks(vendor.id, transaction);
            }

            /// commit
            await transaction.commit();

            ///
        } catch (error) {
            /// rollback
            await transaction.rollback();

            logger.error(`Error checking violation threshold: ${error}`);
            throw error;
        }
    }
}

export default new PolicyViolations();
