import { IRouter, Router } from 'express';
import vendor_notification from '../../../controllers/admin/vendor_notifications';

const routes: IRouter = Router();

routes.get('/notification', vendor_notification.getVendorNotification);

routes.get('/notification-unread-count', vendor_notification.getVendorNotificationUnreadCount);

routes.put('/notification-read', vendor_notification.markAsReadVendorNotification);

export default routes;
