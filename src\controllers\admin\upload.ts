import { logger } from '../../utils/logger';
import path from 'path';
import dotenv from 'dotenv';
import multer from 'multer';
import multerS3 from 'multer-s3';
import express from 'express';
import AWS from 'aws-sdk';
import { UploadConstant } from '../../utils/upload_constant';
dotenv.config();

const USESERVER = process.env.USESERVER;
const MODE = process.env.MODE;
const ID = process.env.AWS_ID;
const SECRET = process.env.AWS_SECRET;
const BUCKET_NAME = process.env.BUCKETNAME;
const spacesEndpoint = new AWS.Endpoint('sgp1.digitaloceanspaces.com');
const s3 = new AWS.S3({
    endpoint: spacesEndpoint,
    accessKeyId: ID!,
    secretAccessKey: SECRET!
});

class Uploader {
    storage: any;
    upload_dir: any;

    constructor() {
        this.upload_dir = UploadConstant.UPLOAD_DIR_DEFAULT;
        this.storage = undefined;
    }

    /**
     * Uploader Function
     * @param field (Field name in which file has been passed)
     * @param res (Express Response Object)
     * @param folder (Folder for image to placed)
     * @returns
     */
    uploader(field, res, folder = null) {
        logger.info('innn', field);
        return multer({
            storage: this.storage,
            fileFilter: (req, file, cb) => {
                logger.info('i file filter', file);
                this.checkFileType(file, cb, res);
            }
        }).single(field);
    }

    /**
     * Function to check mime type for file
     * @param file (File Object)
     * @param cb
     * @param res (Express Response Object)
     * @returns
     */
    checkFileType(file, cb, res) {
        logger.info(`!!!!!!!checkFileType Initial!!!!!!!!!! ${file.originalname} and ${file.mimetype}`);

        // Allowed ext
        const filetypes =
            /image\/jpg|image\/jpeg|jpeg|jpg|png|xlsx|xls|application\/octet-stream|application\/vnd\.ms-excel|application\/vnd.openxmlformats-officedocument.spreadsheetml.sheet|csv|jfif|aac|mp4|flv|avi|wav|m4a|wmv|mkv|ogg|mov|webm|pdf|3gp|audio\/mpeg|audio\/x-mpeg-3|audio\/wav|audio\/x-wav|audio\/ogg|audio\/x-ogg|audio\/m4a|audio\/aac|acc|audio\/acc|mp3/;

        // const filetypes = /image\/jpg|image\/jpeg|jpeg|jpg|png/;
        // Check ext
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        // Check mime
        const mimetype = filetypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb('File type not allowed');
        }
    }

    /**
     * Handler function to upload file
     * @returns Filename of the file which is stored in server
     */
    handler() {
        const app = express.Router();

        app.post('/:folder/:field', (req: any, res: any) => {
            logger.info(`!!!!!!!!!!!!!!upload dir!!!!!!!!!!!!!!!!!!!!`);
            logger.info(`Enter Upload ${req.params.folder} and ${req.params.field} and ${req.file} and ${req.uri}`);

            let folderName = 'common';
            if (req.params.folder === 'stock' && req.params.field === 'file') {
                folderName = MODE === 'development' ? 'stock_dev' : 'stock';
                this.upload_dir = UploadConstant.UPLOAD_DIR_STOCK;
            } else if (req.params.folder === 'user' && req.params.field === 'document') {
                folderName = MODE === 'development' ? 'user_dev' : 'user';
                this.upload_dir = UploadConstant.UPLOAD_DIR_DOCUMENT;
            } else if (req.params.folder === 'vendor' && req.params.field === 'document') {
                folderName = MODE === 'development' ? 'vendor_dev' : 'vendor';
                this.upload_dir = UploadConstant.UPLOAD_DIR_VENDOR_DOCUMENT;
            } else if (req.params.folder === 'user' && req.params.field === 'signature') {
                folderName = MODE === 'development' ? 'user_dev' : 'user';
                this.upload_dir = UploadConstant.UPLOAD_DIR_SIGNATURE;
            } else if (req.params.folder === 'banner' && req.params.field === 'image') {
                folderName = MODE === 'development' ? 'banner_dev' : 'banner';
                this.upload_dir = UploadConstant.UPLOAD_DIR_BANNER;
            } else if (req.params.folder === 'invoice' && req.params.field === 'file') {
                folderName = MODE === 'development' ? 'invoice_dev' : 'invoice';
                this.upload_dir = UploadConstant.UPLOAD_DIR_INVOICE;
            } else if (req.params.folder === 'vendor-order-invoice' && req.params.field === 'file') {
                folderName = MODE === 'development' ? 'invoice_dev' : 'invoice';
                this.upload_dir = UploadConstant.UPLOAD_DIR_VENDOR_ORDER_INVOICE;
            } else if (req.params.folder === 'jewellery' && req.params.field === 'video') {
                folderName = MODE === 'development' ? 'jewellery_video_dev' : 'jewellery_video';
                this.upload_dir = UploadConstant.UPLOAD_DIR_JEWELLERY_VIDEO;
            } else if (req.params.folder === 'jewellery-video-thumbnail' && req.params.field === 'image') {
                folderName = MODE === 'development' ? 'jewellery_video_dev' : 'jewellery_video';
                this.upload_dir = UploadConstant.UPLOAD_DIR_JEWELLERY_VIDEO_THUMBNAIL;
            }

            logger.info(
                `!!!!!!!!!!!!!!upload dir!!!!!!!!!!!!!!!!!!!! ${USESERVER === 'false'} ${
                    this.upload_dir
                } and ${folderName}`
            );

            /**
             * Assign storage
             */
            this.storage = multer.diskStorage({
                destination: this.upload_dir,
                filename(request, file, cb) {
                    logger.info(`<<<<<<<<${this.upload_dir}>>>>>>>>>>>>>>>>`);
                    cb(null, file.fieldname + '_' + Date.now() + path.extname(file.originalname));
                }
            });

            /**
             * Assigning upload function
             */
            const upload = this.uploader(req.params.field || 'file', res, req.params.folder);

            upload(req, res, (err) => {
                logger.info(`in upload function ${req.file}`);
                if (err) {
                    logger.info(`!!!!!!!!!!!First Err!!!!!!!!! ${err}`);
                    res.status(500).send({
                        code: 'ERROR_IN_UPLOAD',
                        message: err,
                        status: 500
                    });
                } else {
                    if (req.file === undefined) {
                        logger.info('Error: No File Selected!');
                        res.status(500).send({
                            code: 'NO_FILE',
                            message: 'No file selected',
                            status: 500
                        });
                    } else {
                        logger.info('File Uploaded!');
                        res.status(200).send({
                            message: 'File uploaded successfully',
                            status: 200,
                            code: 'FILE_UPLOADED',
                            file: USESERVER === 'false' ? req.file.key.split('/')[1] : `${req.file.filename}`
                        });
                    }
                }
            });
        });

        return app;
    }
}

export default new Uploader().handler();
