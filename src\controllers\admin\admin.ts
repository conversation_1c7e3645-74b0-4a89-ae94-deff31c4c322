import bcrypt from 'bcrypt';
import { AdminAttributes } from '../../models/admins';
import models from '../../models';
import { NextFunction, Request, Response } from 'express';
import { adminRole, cutAlias, cutDisplayNames, fancyColor, fluorescenceColorAlias, fluorescenceColorDisplayNames, httpStatusCodes, intensityAlias, intensityDisplayNames, roundAlias, shapeDisplayNames, TOKEN, whiteColor } from '../../utils/constants';
import jwt from 'jsonwebtoken';
import { VendorAttributes } from '../../models/vendors';
import { logger } from '../../utils/logger';
import { decryptBody, encryptBody } from '../../utils/encrypt';
import axios from 'axios';
import { exec } from 'child_process';
import path from 'path';

class Admin {
    // constructor() { }

    async restartServer(req: Request, res: Response, next: NextFunction) {
        try {
            const restartScriptPath = path.join(__dirname, '../../../', 'restart_services.sh');

            // Execute the shell script to restart the server
            const response = await new Promise((resolve, reject) => {
                exec(restartScriptPath, (error, stdout, stderr) => {
                    if (error) {
                        logger.error(`Error restarting server: ${error.message}`);
                        reject('Server Restart Failed')
                    }
                    
                    if (stderr) {
                        logger.error(`Error output: ${stderr}`);
                        reject('Server Restart Failed')
                    }
    
                    logger.info(`Server restarted successfully: <pre>${stdout}</pre>`);
    
                    resolve(`<pre>${stdout}</pre>`)
                });
            })

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Server Restarted Successfully',
                data: response
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    async getAliasConfig(req: Request, res: Response, next: NextFunction) {
        try {
            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Alias Configurations',
                data: {
                    shapeAlias: roundAlias,
                    shapeDisplayNames,
                    cutAlias,
                    cutDisplayNames,
                    intensityAlias,
                    intensityDisplayNames,
                    fluorescenceColorAlias,
                    fluorescenceColorDisplayNames,
                    whiteColor,
                    fancyColor
                }
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /*
        --------------------------------------------------------------------------------
        Admin functions
    */
    /**
     *  @api {post} /v1/adminLogin
     *  @apiName admin Login
     *  @apiGroup Admin
     *
     *  @apiSuccess {Object} Admin
     */

    async adminLogin(req: Request, res: Response, next: NextFunction) {
        try {
            const { password } = req.body as AdminAttributes | VendorAttributes;
            let user: AdminAttributes | VendorAttributes | null;
            let role: adminRole;

            let email = req.body.email;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            // Check if the user exists in the admins table
            const admin = await models.admins.findOne({ where: { email, isActive: true } });

            if (!admin) {
                // If user is not found in admins table, assume the role as "vendor"
                user = await models.vendors.findOne({ where: { email, is_active: true, is_blacklisted: false } });
                role = adminRole.vendor;

                if (user) {
                    /// update fcm token into vendor table
                    await models.vendors.update({ fcm_token: req.body?.fcm_token }, { where: { id: user.id } });
                }
            } else {
                /// update fcm token into admin table
                await models.admins.update({ fcm_token: req.body?.fcm_token }, { where: { id: admin.id } });

                user = admin;
                role = admin.role;
            }

            if (!user) {
                throw new Error('Invalid Email');
            }

            const isMatch = await bcrypt.compare(password, user.password);
            if (!isMatch) {
                throw new Error('Invalid Password');
            }

            const token = jwt.sign(
                {
                    id: user.id,
                    role
                },
                TOKEN
            );

            const userData = JSON.parse(JSON.stringify(user));

            /// create FTP if not created!!!
            if (role === adminRole.vendor && userData?.vendor_type === 'FTP') {
                /// check if ftp created or not
                if (!userData?.ftp_username && !userData?.ftp_password) {
                    logger.info('Creating FTP for vendor on LOGIN');
                    const encryptedPassword = encryptBody(password);

                    const result = await new Promise(async (resolve, reject) => {
                        try {
                            /// post request
                            await axios.post(
                                process.env.CREATE_FTP_URL ?? '',
                                {
                                    ftp_username: email,
                                    ftp_password: password,
                                    vendor_id: userData?.id
                                },
                                {
                                    headers: {
                                        'Content-Type': 'application/json',
                                        authorization: process.env.ADD_STOCK_KEY
                                    }
                                }
                            );
                            resolve(true);
                            ///
                        } catch (error) {
                            logger.error(error);
                            // reject(error);
                            resolve(false);
                        }
                    });

                    if (result) {
                        await models.vendors.update(
                            { ftp_username: email, ftp_password: encryptedPassword },
                            { where: { id: userData?.id } }
                        );
                        userData.ftp_username = email;
                        userData.ftp_password = encryptedPassword;
                    }
                }
            }

            if (userData?.ftp_password) {
                const decryptedPassword = await decryptBody(userData?.ftp_password);
                userData.ftp_password = decryptedPassword;
            }

            delete userData.otp;
            delete userData.password;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Successfully Login',
                data: { ...userData, role, token }
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/details
     * @apiName vendorDetails
     * @apiGroup Vendor
     *
     *
     * @apiSuccess {Object} Vendors.
     */
    async vendorDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!details function start!!!!!');
        try {
            // const id: any = req[`id`];
            const role = req[`role`];

            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                res.json({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: 'Admin details successfully listed',
                    data: req[`admin`]
                });
            } else if (role === adminRole.vendor) {
                res.json({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: 'Vendor details successfully listed',
                    data: req[`vendor`]
                });
            }

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Admin();
