import Joi from 'joi';

export const addBannerSchema = Joi.object({
    order: Joi.number().integer().min(0).optional(),
    name: Joi.string().allow(null).optional(),
    description: Joi.string().allow(null).optional(),
    placement: Joi.string().required(),
    banner_image: Joi.string().allow(null).required(),
    position: Joi.string().valid('top', 'bottom', 'drawer', 'dashboard').allow(null).optional(),
    deep_link: Joi.string().allow(null).optional(),
    type: Joi.string().optional(),
    payload: Joi.string().allow(null).optional(),
    expiresAt: Joi.date().allow(null)
});

export const updateBannerSchema = Joi.object({
    id: Joi.string().uuid().required(),
    order: Joi.number().integer().min(0).optional(),
    name: Joi.string().allow(null).optional(),
    description: Joi.string().allow(null).optional(),
    placement: Joi.string().optional(),
    banner_image: Joi.string().allow(null).optional(),
    position: Joi.string().valid('top', 'bottom', 'drawer', 'dashboard').allow(null).optional(),
    deep_link: Joi.string().allow(null).optional(),
    type: Joi.string().optional(),
    payload: Joi.string().allow(null).optional(),
    expiresAt: Joi.date().allow(null)
});

export const changeBannerStatusSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_active: Joi.boolean().required()
});
