import { I<PERSON><PERSON><PERSON>, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import { createNotificationSchema } from '../../../utils/schema/user_notification_schema';
import user_notification from '../../../controllers/user/user_notifications/user_notification';

const routes: IRouter = Router();

routes.post(
    '/inquiry-notification',
    (req, res, next) => fieldsValidator(req, res, next, createNotificationSchema),
    user_notification.sendUserInquiryNotification
);

export default routes;
