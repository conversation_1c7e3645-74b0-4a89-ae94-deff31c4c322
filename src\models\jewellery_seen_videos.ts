import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface JewellerySeenVideoAttributes {
    id?: string;
    jewellery_showcase_id?: string;
    user_id?: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface JewellerySeenVideoCreationAttributes extends Optional<JewellerySeenVideoAttributes, 'id'> { }

interface JewellerySeenVideoInstance
    extends Model<JewellerySeenVideoAttributes, JewellerySeenVideoCreationAttributes>,
    JewellerySeenVideoAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type JewellerySeenVideoStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => JewellerySeenVideoInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const jewellery_seen_videos = sequelize.define<JewellerySeenVideoInstance>(
        'jewellery_seen_videos',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            jewellery_showcase_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as JewellerySeenVideoStatic;

    // TODO: make common function to sync
    // await jewellery_seen_videos.sync({ alter: true });

    return jewellery_seen_videos;
};
