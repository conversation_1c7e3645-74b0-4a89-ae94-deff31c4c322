import xlsxToJson from 'xlsx-to-json-lc';
import xlsToJson from 'xls-to-json-lc';
import csv from 'csvtojson';
import * as fs from 'fs';
import { logger } from '../../../utils/logger';

export async function convertCSVToJson(filePath: string) {
    return new Promise(async (resolve, reject) => {
        try {
            const csvResult = await csv().fromFile(filePath);
            resolve(csvResult);
        } catch (error: any) {
            reject(error);
        }
    });
}

export function convertExcelToJson(filePath: string) {
    ///
    return new Promise((resolve, reject) => {
        let excelToJson: any;

        const isExist = fs.existsSync(filePath);
        logger.info(`!!!!ISExist!!!!!!!! ${isExist} and ${filePath}`);

        if (filePath.split('.')[filePath.split('.').length - 1] === 'xlsx') {
            excelToJson = xlsxToJson;
        } else if (filePath.split('.')[filePath.split('.').length - 1] === 'xls') {
            excelToJson = xlsToJson;
        }

        return excelToJson(
            {
                input: filePath,
                output: null,
                lowerCaseHeaders: true
            },
            async (error: any, result: any) => {
                if (error) {
                    reject(error);
                }

                resolve(result);
            }
        );
    });
}
