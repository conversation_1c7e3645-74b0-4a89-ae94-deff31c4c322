import { IRouter, NextFunction, Router, Request, Response } from 'express';
import jewelleryShowcase from '../../../controllers/admin/jewellery_showcase';
import { fieldsValidator } from '../../../middlewares/validator';
import { addJewelleryShowcase, updateJewelleryShowcase } from '../../../utils/schema/jewellery_showcase_schema';

const routes: IRouter = Router();

// jewellery showcase video
routes.post(
    '/jewellery-showcase',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addJewelleryShowcase),
    jewelleryShowcase.addJewelleryShowcase
);

// jewellery showcase video
routes.get('/jewellery-showcase', jewelleryShowcase.listJewelleryShowcaseVideos);

// update jewellery showcase video
routes.put(
    '/jewellery-showcase',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateJewelleryShowcase),
    jewelleryShowcase.editJewelleryShowcase
);

// jewellery showcase video
routes.delete('/jewellery-showcase', jewelleryShowcase.deleteJewelleryShowcaseVideo);

export default routes;
