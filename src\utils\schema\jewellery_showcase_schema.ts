import Joi from 'joi';

export const addJewelleryShowcase = Joi.object({
    file_name: Joi.string().required(),
    description: Joi.string().required(),
    thumbnail_name: Joi.string().required(),
    tags: Joi.array().items(Joi.string()).required(),
});

export const updateJewelleryShowcase = Joi.object({
    id: Joi.string().required(),
    file_name: Joi.string().optional(),
    description: Joi.string().optional(),
    thumbnail_name: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
});

/// add seen video
export const addJewellerySeenVideoSchema = Joi.object({
    jewellery_showcase_ids: Joi.array().items(Joi.string().uuid().required()).required(),
});
