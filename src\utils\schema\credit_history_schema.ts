import Joi from 'joi';

export const addCreditHistorySchema = Joi.object({
    user_id: Joi.string().uuid().required(),
    credit: Joi.number().required(),
    transaction_type: Joi.string().valid('CREDIT', 'DEBIT').required(),
    type: Joi.string()
        .valid('VERIFY', 'UPDATE', 'CREATE-BUY-REQUEST', 'UPDATE-BUY-REQUEST', 'ORDER-DELIVERED')
        .required(),
    buy_request_id: Joi.string().uuid().allow(null)
});
