import { NextFunction, Request, Response } from 'express';
import models from '../../models';
import { Op } from 'sequelize';
import { adminRole, httpStatusCodes, vendorRejectedHoldRequestStatus } from '../../utils/constants';
import { logger } from '../../utils/logger';

class VendorRejectedHoldRequests {
    /*
        --------------------------------------------------------------------------------
        Vendor Rejected Hold Requests functions
    */

    /**
     * @api {get} /v1/auth/admin/vendor-rejected-requests
     * @apiName listVendorRejectedHoldRequests
     * @apiGroup VendorRejectedHoldRequests
     *
     *
     * @apiSuccess {Object} VendorOrders.
     */
    async listVendorRejectedRequests(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list vendor rejected requests');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const conditions: any = [];
            const role = req[`role`];
            const reqId = req[`id`];

            if (role === adminRole.vendor) {
                conditions.push({ vendor_id: reqId });
            }

            conditions.push({ status: vendorRejectedHoldRequestStatus.declined });

            const { rows, count } = await models.vendor_rejected_hold_requests.findAndCountAll({
                where: { [Op.and]: conditions },
                include: [
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'price_per_caret',
                            'final_price',
                            'final_price_ori',
                            'price_per_caret_ori',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'stock_margin'
                        ]
                    },
                    { model: models.vendors, attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'] },
                    { model: models.buy_requests, attributes: ['id', 'order_code', 'type'] }
                ],
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            /// update approved margin in stock ids
            const updatedData = JSON.parse(JSON.stringify(rows)).map((row: any) => ({ ...row, stock: { ...row?.stock ?? {}, ...row?.approved_margin?.stock ?? {} } }));

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Rejected Vendor Requests listed successfully`,
                data: updatedData,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new VendorRejectedHoldRequests();
