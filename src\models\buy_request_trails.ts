import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface BuyRequestTrailsAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    buy_request_id: string;
    parent_buyrequest_id: string;
    status: string;
    updated_by_id: string;
    notes: string;
    payload: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface BuyRequestTrailsCreationAttributes extends Optional<BuyRequestTrailsAttributes, 'id'> { }

interface BuyRequestTrailsInstance
    extends Model<BuyRequestTrailsAttributes, BuyRequestTrailsCreationAttributes>,
    BuyRequestTrailsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type BuyRequestTrailsStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => BuyRequestTrailsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const buy_request_trails = sequelize.define<BuyRequestTrailsInstance>(
        'buy_request_trails',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            parent_buyrequest_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            notes: {
                type: DataTypes.STRING,
                allowNull: true
            },
            status: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: ['PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED']
            },
            updated_by_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            payload: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as BuyRequestTrailsStatic;
    //
    // await buy_request_trails.sync({ alter: true })

    return buy_request_trails;
};
