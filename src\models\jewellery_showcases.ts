import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface JewelleryShowcaseAttributes {
    id?: string;
    video_url: string;
    file_name: string;
    tags?: string[]; /// comma separated tags
    description?: string;
    thumbnail_url?: string;
    thumbnail_name?: string;
    is_liked?: boolean;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface JewelleryShowcaseCreationAttributes extends Optional<JewelleryShowcaseAttributes, 'id'> { }

interface JewelleryShowcaseInstance
    extends Model<JewelleryShowcaseAttributes, JewelleryShowcaseCreationAttributes>,
    JewelleryShowcaseAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type JewelleryShowcaseStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => JewelleryShowcaseInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const jewellery_showcases = sequelize.define<JewelleryShowcaseInstance>(
        'jewellery_showcases',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            file_name: {
                type: DataTypes.STRING,
                allowNull: true
            },
            tags: {
                type: DataTypes.ARRAY(DataTypes.STRING),
                allowNull: true
            },
            video_url: {
                type: DataTypes.STRING,
                allowNull: false
            },
            thumbnail_name: {
                type: DataTypes.STRING,
                allowNull: true
            },
            thumbnail_url: {
                type: DataTypes.STRING,
                allowNull: true
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            is_liked: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as JewelleryShowcaseStatic;

    // TODO: make common function to sync
    // await jewellery_showcases.sync({ alter: true });

    return jewellery_showcases;
};
