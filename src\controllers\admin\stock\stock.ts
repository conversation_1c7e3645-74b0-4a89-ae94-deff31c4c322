import { NextFunction, Request, Response } from 'express';
import models, { sequelize } from '../../../models';
import {
    NotificationType,
    OfferStatus,
    adminRole,
    httpStatusCodes,
    naturalAlias,
    stockStatus
} from '../../../utils/constants';
import { logger } from '../../../utils/logger';
import { Op, Sequelize } from 'sequelize';
import { convertCSVToJson, convertExcelToJson } from './readExcel';
import filters from './getFilterStockFields';
import { UploadConstant } from '../../../utils/upload_constant';
import userNotification from '../../user/user_notifications/user_notification';
import customEmails from '../../user/user_notifications/custom_emails';
import { generateStockObject } from './generate_stock_object';
import policyViolationsService from '../policy_violations';
import * as path from 'path';
import * as fs from 'fs';
import { saveCsvFile } from '../../../controllers/common/save_csv';
import moment from 'moment';
import { parse } from 'json2csv';

class Stock {
    /*
        --------------------------------------------------------------------------------
        Stock functions
    */

    /**
     * @api {get} /v1/auth/admin/stock-list
     * @apiName listStocks
     * @apiGroup AdminStocks
     *
     *
     * @apiSuccess {Object} Stocks.
     */
    async listStocks(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!listStocks function start!!!!!');
        try {
            const role = req[`role`];
            if (![adminRole.vendor, adminRole.superAdmin, adminRole.subAdmin].includes(role as adminRole)) {
                throw new Error('Unauthorized');
            }

            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            // let isAdmin: any = (req.query.is_admin || false).toString().toLowerCase() === 'true';
            let isAdmin: any = req.query.is_admin;
            const filterObject = req.body.filterObject;
            let vendorId: any;
            let adminId: any;
            const is_download: boolean = String(req.query.is_download).toLowerCase() === 'true';

            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role as adminRole)) {
                adminId = req[`id`];
            } else if (role === adminRole.vendor) {
                vendorId = req[`id`];
                isAdmin = false;
            }

            /// list stocks based on vendor id and is admin false
            if (req.query?.vendor_id) {
                vendorId = req.query.vendor_id;
                isAdmin = false;
            }

            const conditions: any = [
                {
                    is_active: true
                },
                {
                    _deleted: false
                }
            ];

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (filterObject) {
                const filterObjectArray = await filters.getFilters(filterObject);
                conditions.push({
                    [Op.and]: filterObjectArray
                });
            }

            /// this is to list only admin's stock or vendor's stock
            /// if isAdmin is not undefined
            /// list stocks where admin_id is not null
            if (isAdmin?.toString().toLowerCase() === 'true') {
                conditions.push({
                    admin_id: { [Op.ne]: null }
                });
            }
            /// list stocks where vendor_id is not null
            /// or list stocks for that vendor_id only
            else if (isAdmin?.toString().toLowerCase() === 'false') {
                if (vendorId) {
                    conditions.push({
                        vendor_id: vendorId
                    });
                } else {
                    conditions.push({
                        vendor_id: { [Op.ne]: null }
                    });
                }
            }

            /// exclude RETURNED diamonds
            /// conditions.push({ status: { [Op.ne]: 'RETURNED' } });

            /// excludes stock with 0 price
            conditions.push({
                [Op.or]: [{ final_price: { [Op.gt]: 0 } }, { final_price_ori: { [Op.gt]: 0 } }]
            });

            /// list stocks
            const { rows, count } = await models.stocks.findAndCountAll({
                where: {
                    [Op.and]: conditions
                },
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            const vendorsIds = rows.map((stock: any) => stock.vendor_id);

            const vendors = await models.vendors.findAll({
                where: { id: { [Op.in]: vendorsIds } },
                attributes: ['id', 'first_name', 'last_name', 'email']
            });

            const stocksList = rows.map((stock: any) => ({
                ...JSON.parse(JSON.stringify(stock)),
                vendor_details: vendors.find((vendor: any) => vendor.id === stock.vendor_id)
            }));

            let download_url: any;

            if (is_download) {
                // saved file path
                const localFilePath: any = await saveCsvFile(stocksList, `${req[`id`]}_${Date.now()}.csv`);

                // Extract the file name
                const fileName = path.basename(localFilePath);

                /// download url
                download_url = `${process.env.BASE_URL}/download-stock/${fileName}`;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stocks successfully listed',
                data: is_download ? download_url : stocksList,
                count: is_download ? limit : count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/stock
     *  @apiName addStock
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async addStock(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!AddStock function start!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const role = req[`role`];
            if (![adminRole.vendor, adminRole.superAdmin].includes(role as adminRole)) {
                throw new Error('Unauthorized');
            }

            const fileName = req.body.file_name;
            const replaceAll = req.body.replace_all;
            const offset = req.body.offset || 0;
            // const filePath = `./public/stock/file_1711611076714.xlsx`;
            // const filePath = `${process.env.BASE_URL}/stock/${fileName}`;
            const filePath = `${UploadConstant.UPLOAD_DIR_STOCK}/${fileName}`;

            if (!fileName) {
                throw new Error('Filename required');
            }

            const adminId = req[`admin`]?.id;
            const vendorId = req[`vendor`]?.id;
            let vendorMargin = 0;
            let stocksWithZeroPrice: any = [];
            let stocksWithoutCertificate: any = [];
            let stocksWithoutLab: any = [];
            let stocksWithNaturalDiamond: any = [];
            let filteredBulkCreateObject: any = [];

            if (vendorId) {
                const isVendorBlackListed = await models.vendors.findOne({
                    where: { id: vendorId }
                });

                if (isVendorBlackListed && isVendorBlackListed.is_blacklisted) {
                    throw new Error(`Couldn't create stock, Vendor blacklisted`);
                }

                vendorMargin = isVendorBlackListed.margin_percentage;
            }

            const stockData = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [adminId ? { admin_id: adminId } : {}, vendorId ? { vendor_id: vendorId } : {}]
                        },
                        {
                            _deleted: false
                        }
                    ]
                },
                include: [
                    {
                        model: models.stock_offers,
                        as: 'stock_offers',
                        where: {
                            status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                        },
                        required: false, // Allows stocks without offers to be returned
                        attributes: []
                    }
                ],
                attributes: {
                    include: [
                        'stock_id',
                        'status',
                        'admin_id',
                        'vendor_id',
                        'certificate_number',
                        'id',
                        [
                            Sequelize.literal('CASE WHEN "stock_offers"."id" IS NOT NULL THEN true ELSE false END'),
                            'is_offer_created'
                        ]
                    ]
                }
            });

            const stockIds = stockData?.map((stockItem: any) => stockItem.stock_id);

            logger.info(`Existing Stock Ids Length ${stockIds.length} and Data:::: ${stockIds}`);

            /// convert array object from excel
            const isExcel = filePath.split('.')[filePath.split('.').length - 1] !== 'csv';
            logger.info(`!!!!!!!!!!isExcel!!!!!!!!!!!! ${isExcel}`);
            logger.info(`!!!!!!!!!!Filepath!!!!!!!!!!!!! ${filePath}`);

            const convertedResultArray: any = isExcel
                ? await convertExcelToJson(filePath)
                : await convertCSVToJson(filePath);

            logger.info(`!!!!!!!!!Result Length!!!!!!!!! ${convertedResultArray?.length}`);

            /// stock margin
            const stockMargins: any = await models.stock_margins.findAll();

            /// generate stock model object
            const generatedStocksObject: any = await generateStockObject(
                convertedResultArray,
                stockData,
                stockIds,
                offset,
                adminId,
                vendorId,
                stockMargins,
                vendorMargin,
                req[`ip`] || ''
            );
            logger.info(`!!!!!!!!!Stock Object Length!!!!!!!!! ${generatedStocksObject.stock_object_list?.length}`);

            const totalEngagedStockIds: any = generatedStocksObject.updated_stock_list;
            if (generatedStocksObject.stock_object_list.length) {
                /// add admin id and vendor id
                const bulkCreateObject = generatedStocksObject.stock_object_list.map((item: any) =>
                    adminId ? { ...item, admin_id: adminId } : { ...item, vendor_id: vendorId }
                );

                /// filtered bulk create object
                filteredBulkCreateObject = bulkCreateObject
                    .map((createObject: any) => ({
                        ...createObject,
                        createdAt: moment.utc(),
                        updatedAt: moment.utc()
                    }))
                    .filter(
                        (stock: any) =>
                            stock?.final_price &&
                            stock?.final_price_ori &&
                            stock?.price_per_caret &&
                            stock?.certificate_number &&
                            ![stockStatus.onHold, stockStatus.onMemo].includes(stock?.status) &&
                            stock?.lab &&
                            !['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim()) &&
                            !naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim())
                    );

                /// bulk create socks
                await models.stocks.bulkCreate(filteredBulkCreateObject, { ignoreDuplicates: true, transaction });

                filteredBulkCreateObject.forEach((element) => {
                    totalEngagedStockIds.push(element.stock_id);
                });
                logger.info(`!!!!!!!!!!!!!!totalEngagedStockIds Length!!!!!!!!!!! ${totalEngagedStockIds.length}`);

                /// obtain list for zero price stocks not uploaded
                stocksWithZeroPrice = bulkCreateObject.filter(
                    (stock: any) => !stock?.final_price || !stock?.final_price_ori || !stock?.price_per_caret
                );

                stocksWithoutCertificate = bulkCreateObject.filter((stock: any) => !stock?.certificate_number);

                stocksWithoutLab = bulkCreateObject.filter(
                    (stock: any) =>
                        !stock?.lab ||
                        ['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim())
                );

                stocksWithNaturalDiamond = bulkCreateObject.filter((stock: any) => {
                    return naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim());
                });

                if (generatedStocksObject.stocksWithZeroPrice && generatedStocksObject.stocksWithZeroPrice.length > 0) {
                    generatedStocksObject.stocksWithZeroPrice.forEach((stockI: any) => {
                        stocksWithZeroPrice.push(stockI);
                    });
                }

                if (
                    generatedStocksObject.stocksWithoutCertificate &&
                    generatedStocksObject.stocksWithoutCertificate.length > 0
                ) {
                    generatedStocksObject.stocksWithoutCertificate.forEach((stockIWc: any) => {
                        stocksWithZeroPrice.push(stockIWc);
                    });
                }

                if (generatedStocksObject.stocksWithoutLab && generatedStocksObject.stocksWithoutLab.length > 0) {
                    generatedStocksObject.stocksWithoutLab.forEach((stockIWl: any) => {
                        stocksWithZeroPrice.push(stockIWl);
                    });
                }

                if (
                    generatedStocksObject.stocksWithNaturalDiamond &&
                    generatedStocksObject.stocksWithNaturalDiamond.length > 0
                ) {
                    generatedStocksObject.stocksWithNaturalDiamond.forEach((stockIWl: any) => {
                        stocksWithNaturalDiamond.push(stockIWl);
                    });
                }
            }

            if (replaceAll) {
                /// filter to be deleted stocks
                const toBeDeletedStocksIds: any = stockData
                    ?.filter((stock: any) => !stock?.is_offer_created)
                    ?.filter((stock: any) =>
                        [stockStatus.available, stockStatus.onHold, stockStatus.onMemo].includes(stock?.status)
                    )
                    ?.filter((stock: any) => !totalEngagedStockIds.includes(stock.stock_id))
                    ?.map((item: any) => item?.id);
                logger.info(`!!!!!!toBeDeletedStocksIds Length!!!!!!!!!! ${toBeDeletedStocksIds?.length}`);

                const stocksToDelete = await models.stocks.findAll({
                    where: {
                        id: {
                            [Op.in]: toBeDeletedStocksIds
                        },
                        status: {
                            [Op.in]: [stockStatus.available, stockStatus.onHold, stockStatus.onMemo]
                        }
                    }
                });

                await models.stocks.destroy({
                    where: {
                        id: {
                            [Op.in]: toBeDeletedStocksIds
                        },
                        status: {
                            [Op.in]: [stockStatus.available, stockStatus.onHold, stockStatus.onMemo]
                        }
                    },
                    transaction
                });
                logger.info(`!!!!!!!!!Stocks Deleted!!!!!!!!!!!`);

                /// create bulk create object for deleted stocks
                const deletedStocksBulkCreate = stocksToDelete?.map((stock: any) => {
                    const { id, updatedAt, createdAt, ...rest } = JSON.parse(JSON.stringify(stock)); // Destructure and exclude 'id'
                    return {
                        ...rest, // Spread the rest of the properties
                        stock_uuid: id, // Use the 'id' value as 'stock_uuid'
                        deleted_by_id: adminId || vendorId, // Set the deleted_by_id
                        deleted_by_ip: req[`ip`],
                        delete_type: 'Add Stock From File PATH',
                        createdAt: moment.utc(),
                        updatedAt: moment.utc()
                    };
                });
                logger.info(`!!!!!!!!Deleted Stocks Created In New Table!!!!!!!!!!!!!`);

                /// bulk create deleted stocks
                await models.deleted_stocks.bulkCreate(deletedStocksBulkCreate, { transaction });
            }

            /// create file name entry into table
            try {
                /// update last inventory update time
                await models.vendors.update({ last_inventory_update: moment.utc() }, { where: { id: vendorId } });

                /// create file name entry into table
                await models.vendor_admin_file_histories.create({
                    admin_id: adminId,
                    vendor_id: vendorId,
                    file_name: fileName,
                    vendor_type: 'UPLOAD',
                    payload: JSON.stringify(convertedResultArray)
                });
            } catch (error: any) {
                logger.error(`file name entry error ${error}`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock created successfully!'
                // data: stockResult
            });

            /// commit transaction
            await transaction.commit();

            try {
                // Check for duplicate diamonds after stock creation
                if (vendorId && filteredBulkCreateObject.length) {
                    try {
                        policyViolationsService.checkDuplicateDiamond(filteredBulkCreateObject, vendorId, adminId);
                    } catch (error: any) {
                        logger.error(`Error checking duplicate diamonds: ${error}`);
                        // Don't throw error to avoid breaking the stock creation process
                    }
                }
            } catch (error: any) {
                logger.error(`Error checking duplicate diamonds: ${error}`);
            }

            //// notifications
            try {
                /// send notifications to users who have updated stocks in whish list
                let notifyStocks: any[] = [];

                if (replaceAll && generatedStocksObject.stock_object_list.length) {
                    notifyStocks = notifyStocks.concat(
                        generatedStocksObject.stock_object_list
                            .filter((filterItem: any) => filterItem?.stock_id)
                            .map((item: any) => item?.stock_id)
                    );
                }
                if (generatedStocksObject.updated_stock_list.length) {
                    notifyStocks = notifyStocks.concat(generatedStocksObject.updated_stock_list);
                }

                if (notifyStocks.length) {
                    /// stock_id not UUID
                    const notifyStockObjects: any[] = notifyStocks.map((stockId: any) => {
                        if (vendorId) {
                            return { stock_id: stockId, vendor_id: vendorId };
                        } else if (adminId) {
                            return { stock_id: stockId, admin_id: adminId };
                        }
                    });
                    userNotification.sendNotification(NotificationType.stockUpdate, notifyStockObjects);
                }
            } catch (error: any) {
                logger.error(`stock update notification error ${JSON.stringify(error, null, 2)}`);
            }

            /// send email for zero price stocks
            try {
                if (
                    stocksWithZeroPrice?.length ||
                    stocksWithoutCertificate?.length ||
                    stocksWithoutLab?.length ||
                    stocksWithNaturalDiamond?.length
                ) {
                    /// send email to vendor for zero price stocks
                    customEmails.sendEmailToVendorForZeroPriceStocks(
                        stocksWithZeroPrice,
                        stocksWithoutCertificate,
                        stocksWithoutLab,
                        stocksWithNaturalDiamond
                    );
                }
            } catch (error: any) {
                logger.error(`zero price stock not uploaded error!!! ${error}`);
            }

            ////
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    async addStockFromFilePathOld(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelize.transaction();
        try {
            logger.info(`!!!!!!!addStockFromFilePath started!!!!!!`);

            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const replaceAll = req.body.replace_all;
            const offset = req.body.offset || 0;
            const filePath = req.body.filePath;
            const adminId = req.body.admin_id;
            const vendorId = req.body.vendor_id;
            logger.info(`!!!vendorId!!!! ${vendorId}`);
            logger.info(`!!!adminId!!!! ${adminId}`);
            let vendorMargin = 0;
            logger.info(`!!!!!!!!!!FilePath!!!!!!!!!!! ${filePath}`);
            let stocksWithZeroPrice: any = [];
            let stocksWithoutCertificate: any = [];
            let stocksWithoutLab: any = [];
            let stocksWithNaturalDiamond: any = [];

            const isExcel = filePath.split('.')[filePath.split('.').length - 1] !== 'csv';
            logger.info(`!!!!!!!!!!isExcel!!!!!!!!!!!! ${isExcel}`);
            const stock_array_object: any = isExcel
                ? await convertExcelToJson(filePath)
                : await convertCSVToJson(filePath);
            // stock_array_object = stock_array_object.splice(0, 2)

            logger.info(`!!!!!!!!!!!Length!!!!!!!!!! ${stock_array_object?.length}`);

            fs.unlinkSync(filePath);

            if (!stock_array_object?.length) {
                throw new Error('Stock array cannot be empty!!!');
            }

            if (vendorId) {
                const isVendorBlackListed = await models.vendors.findOne({
                    where: { id: vendorId }
                });

                if (isVendorBlackListed && isVendorBlackListed.is_blacklisted) {
                    throw new Error(`Couldn't create stock, Vendor blacklisted`);
                }

                vendorMargin = isVendorBlackListed.margin_percentage;
            }

            const stockData = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [adminId ? { admin_id: adminId } : {}, vendorId ? { vendor_id: vendorId } : {}]
                        },
                        {
                            _deleted: false
                        }
                    ]
                },
                include: [
                    {
                        model: models.stock_offers,
                        as: 'stock_offers',
                        where: {
                            status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                        },
                        required: false, // Allows stocks without offers to be returned
                        attributes: []
                    }
                ],
                attributes: {
                    include: [
                        'stock_id',
                        'status',
                        'admin_id',
                        'vendor_id',
                        'certificate_number',
                        'id',
                        [
                            Sequelize.literal('CASE WHEN "stock_offers"."id" IS NOT NULL THEN true ELSE false END'),
                            'is_offer_created'
                        ]
                    ]
                }
            });

            const stockIds = stockData?.map((stockItem: any) => stockItem.stock_id);

            logger.info(`Existing Stock Ids Length ${stockIds.length} and Data:::: ${stockIds}`);
            logger.info(`!!!!New Array!!!!! ${stock_array_object.length}`);

            /// stock margin
            const stockMargins: any = await models.stock_margins.findAll();

            /// generate stock model object
            const generatedStocksObject: any = await generateStockObject(
                stock_array_object,
                stockData,
                stockIds,
                offset,
                adminId,
                vendorId,
                stockMargins,
                vendorMargin,
                req[`ip`] || ''
            );
            logger.info(`!generatedStocksObject! ${generatedStocksObject.stock_object_list?.length}`);

            const totalEngagedStockIds: any = generatedStocksObject.updated_stock_list;

            if (generatedStocksObject.stock_object_list.length) {
                /// add admin id and vendor id
                const bulkCreateObject = generatedStocksObject.stock_object_list.map((item: any) =>
                    adminId ? { ...item, admin_id: adminId } : { ...item, vendor_id: vendorId }
                );

                /// filtered bulk create object
                const filteredBulkCreateObject = bulkCreateObject
                    .map((createObject: any) => ({
                        ...createObject,
                        createdAt: moment.utc(),
                        updatedAt: moment.utc()
                    }))
                    .filter(
                        (stock: any) =>
                            stock?.final_price &&
                            stock?.final_price_ori &&
                            stock?.price_per_caret &&
                            stock?.certificate_number &&
                            ![stockStatus.onHold, stockStatus.onMemo].includes(stock?.status) &&
                            stock?.lab &&
                            !['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim()) &&
                            !naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim())
                    );

                // logger.info(`!!!!!!!!bulkCreateObject!!!!!!!!! ${JSON.stringify(bulkCreateObject, null, 2)}`)
                filteredBulkCreateObject.forEach((element) => {
                    totalEngagedStockIds.push(element.stock_id);
                });
                logger.info(`!!!!!!!!!!!!!!totalEngagedStockIds Length!!!!!!!!!!! ${totalEngagedStockIds.length}`);

                /// create stock
                await models.stocks.bulkCreate(filteredBulkCreateObject, { transaction });

                /// obtain list for zero price stocks not uploaded
                stocksWithZeroPrice = bulkCreateObject.filter(
                    (stock: any) => !stock?.final_price || !stock?.final_price_ori || !stock?.price_per_caret
                );

                stocksWithoutCertificate = bulkCreateObject.filter((stock: any) => !stock?.certificate_number);

                stocksWithoutLab = bulkCreateObject.filter(
                    (stock: any) =>
                        !stock?.lab ||
                        ['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim())
                );

                stocksWithNaturalDiamond = bulkCreateObject.filter((stock: any) => {
                    return naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim());
                });

                if (generatedStocksObject.stocksWithZeroPrice && generatedStocksObject.stocksWithZeroPrice.length > 0) {
                    generatedStocksObject.stocksWithZeroPrice.forEach((stockI: any) => {
                        stocksWithZeroPrice.push(stockI);
                    });
                }

                if (
                    generatedStocksObject.stocksWithoutCertificate &&
                    generatedStocksObject.stocksWithoutCertificate.length > 0
                ) {
                    generatedStocksObject.stocksWithoutCertificate.forEach((stockIWc: any) => {
                        stocksWithZeroPrice.push(stockIWc);
                    });
                }

                if (generatedStocksObject.stocksWithoutLab && generatedStocksObject.stocksWithoutLab.length > 0) {
                    generatedStocksObject.stocksWithoutLab.forEach((stockIWl: any) => {
                        stocksWithZeroPrice.push(stockIWl);
                    });
                }

                if (
                    generatedStocksObject.stocksWithNaturalDiamond &&
                    generatedStocksObject.stocksWithNaturalDiamond.length > 0
                ) {
                    generatedStocksObject.stocksWithNaturalDiamond.forEach((stockIWl: any) => {
                        stocksWithNaturalDiamond.push(stockIWl);
                    });
                }
            }

            /// filter to be deleted stocks
            const toBeDeletedStocksIds: any = stockData
                ?.filter((stock: any) => !stock?.is_offer_created)
                ?.filter((stock: any) => [stockStatus.available, stockStatus.onHold].includes(stock?.status))
                ?.filter((stock: any) => !totalEngagedStockIds.includes(stock.stock_id))
                ?.map((item: any) => item?.id);
            logger.info(`!!!!!!toBeDeletedStocksIds Length!!!!!!!!!! ${toBeDeletedStocksIds?.length}`);

            const stocksToDelete = await models.stocks.findAll({
                where: {
                    id: {
                        [Op.in]: toBeDeletedStocksIds
                    },
                    status: {
                        [Op.in]: [stockStatus.available, stockStatus.onHold]
                    }
                }
            });

            await models.stocks.destroy({
                where: {
                    id: {
                        [Op.in]: toBeDeletedStocksIds
                    },
                    status: {
                        [Op.in]: [stockStatus.available, stockStatus.onHold]
                    }
                },
                transaction
            });
            logger.info(`!!!!!!!!!Stocks Deleted!!!!!!!!!!!`);

            /// create bulk create object for deleted stocks
            const deletedStocksBulkCreate = stocksToDelete?.map((stock: any) => {
                const { id, updatedAt, createdAt, ...rest } = JSON.parse(JSON.stringify(stock)); // Destructure and exclude 'id'
                return {
                    ...rest, // Spread the rest of the properties
                    stock_uuid: id, // Use the 'id' value as 'stock_uuid'
                    deleted_by_id: adminId || vendorId, // Set the deleted_by_id
                    deleted_by_ip: req[`ip`],
                    delete_type: 'Add Stock From File PATH',
                    createdAt: moment.utc(),
                    updatedAt: moment.utc()
                };
            });
            logger.info(`!!!!!!!!Deleted Stocks Created In New Table!!!!!!!!!!!!!`);

            /// bulk create deleted stocks
            await models.deleted_stocks.bulkCreate(deletedStocksBulkCreate, { transaction });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock created successfully!'
                // data: stockResult
            });

            /// commit transaction
            await transaction.commit();

            //// notifications
            try {
                /// send notifications to users who have updated stocks in whish list
                let notifyStocks: any[] = [];

                if (replaceAll && generatedStocksObject.stock_object_list.length) {
                    notifyStocks = notifyStocks.concat(
                        generatedStocksObject.stock_object_list
                            .filter((filterItem: any) => filterItem?.stock_id)
                            .map((item: any) => item?.stock_id)
                    );
                }
                if (generatedStocksObject.updated_stock_list.length) {
                    notifyStocks = notifyStocks.concat(generatedStocksObject.updated_stock_list);
                }

                if (notifyStocks.length) {
                    /// stock_id not UUID
                    const notifyStockObjects: any[] = notifyStocks.map((stockId: any) => {
                        if (vendorId) {
                            return { stock_id: stockId, vendor_id: vendorId };
                        } else if (adminId) {
                            return { stock_id: stockId, admin_id: adminId };
                        }
                    });

                    userNotification.sendNotification(NotificationType.stockUpdate, notifyStockObjects);
                }
            } catch (error: any) {
                logger.error(`add stock from api notification error ${JSON.stringify(error, null, 2)}`);
            }

            /// send email for zero price stocks
            try {
                if (stocksWithZeroPrice?.length || stocksWithoutCertificate?.length || stocksWithoutLab?.length) {
                    /// send email to vendor for zero price stocks
                    customEmails.sendEmailToVendorForZeroPriceStocks(
                        stocksWithZeroPrice,
                        stocksWithoutCertificate,
                        stocksWithoutLab,
                        stocksWithNaturalDiamond
                    );
                }
            } catch (error: any) {
                logger.error(`zero price stock not uploaded error!!! ${error}`);
            }
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    async addStockFromFilePath(req: Request, res: Response, next: NextFunction) {
        logger.info(`!!!!!!!addStockFromFilePath started!!!!!!`);
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const replaceAll = req.body.replace_all;
            const offset = req.body.offset || 0;
            const filePath = req.body.filePath;
            const adminId = req.body.admin_id;
            const vendorId = req.body.vendor_id;
            logger.info(`!!!vendorId!!!! ${vendorId}`);
            logger.info(`!!!adminId!!!! ${adminId}`);
            let vendorMargin = 0;
            logger.info(`!!!!!!!!!!FilePath!!!!!!!!!!! ${filePath}`);
            let stocksWithZeroPrice: any = [];
            let stocksWithoutCertificate: any = [];
            let stocksWithoutLab: any = [];
            let stocksWithNaturalDiamond: any = [];
            let filteredBulkCreateObject: any = [];

            const isExcel = filePath.split('.')[filePath.split('.').length - 1] !== 'csv';
            logger.info(`!!!!!!!!!!isExcel!!!!!!!!!!!! ${isExcel}`);
            const stock_array_object: any = isExcel
                ? await convertExcelToJson(filePath)
                : await convertCSVToJson(filePath);
            // stock_array_object = stock_array_object.splice(0, 2)

            logger.info(`!!!!!!!!!!!Length!!!!!!!!!! ${stock_array_object?.length}`);

            fs.unlinkSync(filePath);

            if (!stock_array_object?.length) {
                throw new Error('Stock array cannot be empty!!!');
            }

            if (vendorId) {
                const isVendorBlackListed = await models.vendors.findOne({
                    where: { id: vendorId }
                });

                if (isVendorBlackListed && isVendorBlackListed.is_blacklisted) {
                    throw new Error(`Couldn't create stock, Vendor blacklisted`);
                }

                vendorMargin = isVendorBlackListed.margin_percentage;
            }

            const stockData = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [adminId ? { admin_id: adminId } : {}, vendorId ? { vendor_id: vendorId } : {}]
                        },
                        {
                            _deleted: false
                        }
                    ]
                },
                include: [
                    {
                        model: models.stock_offers,
                        as: 'stock_offers',
                        where: {
                            status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                        },
                        required: false, // Allows stocks without offers to be returned
                        attributes: []
                    }
                ],
                attributes: {
                    include: [
                        'stock_id',
                        'status',
                        'admin_id',
                        'vendor_id',
                        'certificate_number',
                        'id',
                        [
                            Sequelize.literal('CASE WHEN "stock_offers"."id" IS NOT NULL THEN true ELSE false END'),
                            'is_offer_created'
                        ]
                    ]
                }
            });

            const stockIds = stockData?.map((stockItem: any) => stockItem.stock_id);

            logger.info(`Existing Stock Ids Length ${stockIds.length} and Data:::: ${stockIds}`);
            logger.info(`!!!!New Array!!!!! ${stock_array_object.length}`);

            /// stock margin
            const stockMargins: any = await models.stock_margins.findAll();

            /// generate stock model object
            const generatedStocksObject: any = await generateStockObject(
                stock_array_object,
                stockData,
                stockIds,
                offset,
                adminId,
                vendorId,
                stockMargins,
                vendorMargin,
                req[`ip`] || ''
            );
            logger.info(`!generatedStocksObject! ${generatedStocksObject.stock_object_list?.length}`);

            const totalEngagedStockIds: any = generatedStocksObject.updated_stock_list;

            if (generatedStocksObject.stock_object_list.length) {
                /// add admin id and vendor id
                const bulkCreateObject = generatedStocksObject.stock_object_list.map((item: any) =>
                    adminId ? { ...item, admin_id: adminId } : { ...item, vendor_id: vendorId }
                );

                /// filtered bulk create object
                filteredBulkCreateObject = bulkCreateObject
                    .map((createObject: any) => ({
                        ...createObject,
                        createdAt: moment.utc(),
                        updatedAt: moment.utc()
                    }))
                    .filter(
                        (stock: any) =>
                            stock?.final_price &&
                            stock?.final_price_ori &&
                            stock?.price_per_caret &&
                            stock?.certificate_number &&
                            ![stockStatus.onHold, stockStatus.onMemo].includes(stock?.status) &&
                            stock?.lab &&
                            !['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim()) &&
                            !naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim())
                    );

                // logger.info(`!!!!!!!!bulkCreateObject!!!!!!!!! ${JSON.stringify(bulkCreateObject, null, 2)}`)
                filteredBulkCreateObject.forEach((element) => {
                    totalEngagedStockIds.push(element.stock_id);
                });
                logger.info(`!!!!!!!!!!!!!!totalEngagedStockIds Length!!!!!!!!!!! ${totalEngagedStockIds.length}`);

                /// create stock
                await models.stocks.bulkCreate(filteredBulkCreateObject, { ignoreDuplicates: true });

                /// obtain list for zero price stocks not uploaded
                stocksWithZeroPrice = bulkCreateObject.filter(
                    (stock: any) => !stock?.final_price || !stock?.final_price_ori || !stock?.price_per_caret
                );

                stocksWithoutCertificate = bulkCreateObject.filter((stock: any) => !stock?.certificate_number);

                stocksWithoutLab = bulkCreateObject.filter(
                    (stock: any) =>
                        !stock?.lab ||
                        ['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim())
                );

                stocksWithNaturalDiamond = bulkCreateObject.filter((stock: any) => {
                    return naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim());
                });

                if (generatedStocksObject.stocksWithZeroPrice && generatedStocksObject.stocksWithZeroPrice.length > 0) {
                    generatedStocksObject.stocksWithZeroPrice.forEach((stockI: any) => {
                        stocksWithZeroPrice.push(stockI);
                    });
                }

                if (
                    generatedStocksObject.stocksWithoutCertificate &&
                    generatedStocksObject.stocksWithoutCertificate.length > 0
                ) {
                    generatedStocksObject.stocksWithoutCertificate.forEach((stockIWc: any) => {
                        stocksWithZeroPrice.push(stockIWc);
                    });
                }

                if (generatedStocksObject.stocksWithoutLab && generatedStocksObject.stocksWithoutLab.length > 0) {
                    generatedStocksObject.stocksWithoutLab.forEach((stockIWl: any) => {
                        stocksWithZeroPrice.push(stockIWl);
                    });
                }

                if (
                    generatedStocksObject.stocksWithNaturalDiamond &&
                    generatedStocksObject.stocksWithNaturalDiamond.length > 0
                ) {
                    generatedStocksObject.stocksWithNaturalDiamond.forEach((stockIWl: any) => {
                        stocksWithNaturalDiamond.push(stockIWl);
                    });
                }
            }

            /// filter to be deleted stocks
            const toBeDeletedStocksIds: any = stockData
                ?.filter((stock: any) => !stock?.is_offer_created)
                ?.filter((stock: any) =>
                    [stockStatus.available, stockStatus.onHold, stockStatus.onMemo].includes(stock?.status)
                )
                ?.filter((stock: any) => !totalEngagedStockIds.includes(stock.stock_id))
                ?.map((item: any) => item?.id);
            logger.info(`!!!!!!toBeDeletedStocksIds Length!!!!!!!!!! ${toBeDeletedStocksIds?.length}`);

            const stocksToDelete = await models.stocks.findAll({
                where: {
                    id: {
                        [Op.in]: toBeDeletedStocksIds
                    },
                    status: {
                        [Op.in]: [stockStatus.available, stockStatus.onHold, stockStatus.onMemo]
                    }
                }
            });

            await models.stocks.destroy({
                where: {
                    id: {
                        [Op.in]: toBeDeletedStocksIds
                    },
                    status: {
                        [Op.in]: [stockStatus.available, stockStatus.onHold, stockStatus.onMemo]
                    }
                }
            });
            logger.info(`!!!!!!!!!Stocks Deleted!!!!!!!!!!!`);

            /// create bulk create object for deleted stocks
            const deletedStocksBulkCreate = stocksToDelete?.map((stock: any) => {
                const { id, updatedAt, createdAt, ...rest } = JSON.parse(JSON.stringify(stock)); // Destructure and exclude 'id'
                return {
                    ...rest, // Spread the rest of the properties
                    stock_uuid: id, // Use the 'id' value as 'stock_uuid'
                    deleted_by_id: adminId || vendorId, // Set the deleted_by_id
                    deleted_by_ip: req[`ip`],
                    delete_type: 'Add Stock From File PATH',
                    createdAt: moment.utc(),
                    updatedAt: moment.utc()
                };
            });
            logger.info(`!!!!!!!!Deleted Stocks Created In New Table!!!!!!!!!!!!!`);

            /// bulk create deleted stocks
            await models.deleted_stocks.bulkCreate(deletedStocksBulkCreate);

            try {
                /// create file name entry into table
                const vendor: any = await models.vendors.findOne({
                    where: { id: vendorId },
                    attributes: ['id', 'vendor_type']
                });

                /// update last inventory update time
                await models.vendors.update({ last_inventory_update: moment.utc() }, { where: { id: vendorId } });

                await models.vendor_admin_file_histories.create({
                    admin_id: adminId,
                    vendor_id: vendorId,
                    file_name: path.basename(filePath),
                    vendor_type: vendor?.vendor_type,
                    payload: JSON.stringify(stock_array_object)
                });
            } catch (error: any) {
                logger.error(`file name entry error ${error}`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock created successfully!'
                // data: stockResult
            });

            /// commit transaction


            try {
                // Check for duplicate diamonds after stock creation
                if (vendorId && filteredBulkCreateObject.length) {
                    try {
                        policyViolationsService.checkDuplicateDiamond(filteredBulkCreateObject, vendorId, adminId);
                    } catch (error: any) {
                        logger.error(`Error checking duplicate diamonds: ${error}`);
                        // Don't throw error to avoid breaking the stock creation process
                    }
                }
            } catch (error: any) {
                logger.error(`Error checking duplicate diamonds: ${error}`);
            }

            //// notifications
            try {
                /// send notifications to users who have updated stocks in whish list
                let notifyStocks: any[] = [];

                if (replaceAll && generatedStocksObject.stock_object_list.length) {
                    notifyStocks = notifyStocks.concat(
                        generatedStocksObject.stock_object_list
                            .filter((filterItem: any) => filterItem?.stock_id)
                            .map((item: any) => item?.stock_id)
                    );
                }
                if (generatedStocksObject.updated_stock_list.length) {
                    notifyStocks = notifyStocks.concat(generatedStocksObject.updated_stock_list);
                }

                if (notifyStocks.length) {
                    /// stock_id not UUID
                    const notifyStockObjects: any[] = notifyStocks.map((stockId: any) => {
                        if (vendorId) {
                            return { stock_id: stockId, vendor_id: vendorId };
                        } else if (adminId) {
                            return { stock_id: stockId, admin_id: adminId };
                        }
                    });

                    userNotification.sendNotification(NotificationType.stockUpdate, notifyStockObjects);
                }
            } catch (error: any) {
                logger.error(`add stock from api notification error ${JSON.stringify(error, null, 2)}`);
            }

            /// send email for zero price stocks
            try {
                if (stocksWithZeroPrice?.length || stocksWithoutCertificate?.length || stocksWithoutLab?.length) {
                    /// send email to vendor for zero price stocks
                    customEmails.sendEmailToVendorForZeroPriceStocks(
                        stocksWithZeroPrice,
                        stocksWithoutCertificate,
                        stocksWithoutLab,
                        stocksWithNaturalDiamond
                    );
                }
            } catch (error: any) {
                logger.error(`zero price stock not uploaded error!!! ${error}`);
            }
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/admin/stock-from-api
     *  @apiName addStockFromArray
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    // Deprecated Old Method
    async addStockFromApi(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!AddStock from array function start!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const replaceAll = req.body.replace_all;
            const offset = req.body.offset || 0;
            const stock_array_object = req.body.stock_array_object;
            const adminId = req.body.admin_id;
            const vendorId = req.body.vendor_id;
            logger.info(`!!!vendorId!!!! ${vendorId}`);
            logger.info(`!!!adminId!!!! ${adminId}`);
            let vendorMargin = 0;
            let stocksWithZeroPrice: any = [];
            let stocksWithoutCertificate: any = [];
            let stocksWithoutLab: any = [];
            let stocksWithNaturalDiamond: any = [];

            if (!stock_array_object?.length) {
                throw new Error('Stock array cannot be empty!!!');
            }

            if (vendorId) {
                const isVendorBlackListed = await models.vendors.findOne({
                    where: { id: vendorId }
                });

                if (isVendorBlackListed && isVendorBlackListed.is_blacklisted) {
                    throw new Error(`Couldn't create stock, Vendor blacklisted`);
                }

                vendorMargin = isVendorBlackListed.margin_percentage;
            }

            const stockData = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [adminId ? { admin_id: adminId } : {}, vendorId ? { vendor_id: vendorId } : {}]
                        },
                        {
                            _deleted: false
                        }
                    ]
                },
                include: [
                    {
                        model: models.stock_offers,
                        as: 'stock_offers',
                        where: {
                            status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                        },
                        required: false, // Allows stocks without offers to be returned
                        attributes: []
                    }
                ],
                attributes: {
                    include: [
                        'stock_id',
                        'status',
                        'admin_id',
                        'vendor_id',
                        'certificate_number',
                        'id',
                        [
                            Sequelize.literal('CASE WHEN "stock_offers"."id" IS NOT NULL THEN true ELSE false END'),
                            'is_offer_created'
                        ]
                    ]
                }
            });

            let stockIds = stockData?.map((stockItem: any) => stockItem.stock_id);

            // Old method for replacing all stock, delete all and create the stocks again
            // In new method we are deleting the stocks which needs to be deleted
            if (replaceAll) {
                /// filter to be deleted stocks
                const toBeDeletedStocksIds: any = stockData
                    ?.filter((stock: any) => !stock?.is_offer_created)
                    ?.map((item: any) => item?.id);

                const stocksToDelete = await models.stocks.findAll({
                    where: {
                        id: {
                            [Op.in]: toBeDeletedStocksIds
                        },
                        status: {
                            [Op.in]: [stockStatus.available, stockStatus.onHold]
                        }
                    }
                });

                await models.stocks.destroy({
                    where: {
                        id: {
                            [Op.in]: toBeDeletedStocksIds
                        },
                        status: {
                            [Op.in]: [stockStatus.available, stockStatus.onHold]
                        }
                    },
                    transaction
                });

                /// create bulk create object for deleted stocks
                const deletedStocksBulkCreate = stocksToDelete?.map((stock: any) => {
                    const { id, updatedAt, createdAt, ...rest } = JSON.parse(JSON.stringify(stock)); // Destructure and exclude 'id'
                    return {
                        ...rest, // Spread the rest of the properties
                        stock_uuid: id, // Use the 'id' value as 'stock_uuid'
                        deleted_by_id: adminId || vendorId, // Set the deleted_by_id
                        deleted_by_ip: req[`ip`],
                        delete_type: 'Add Stock From API',
                        createdAt: moment.utc(),
                        updatedAt: moment.utc()
                    };
                });

                /// bulk create deleted stocks
                await models.deleted_stocks.bulkCreate(deletedStocksBulkCreate, { transaction });

                stockIds = stockData
                    ?.filter((stock: any) => stock.status !== stockStatus.available)
                    .map((stock: any) => stock.stock_id);
            }

            logger.info(`Existing Stock Ids Length ${stockIds.length}`);
            logger.info(`!!!!New Array!!!!! ${stock_array_object.length}`);

            /// stock margin
            const stockMargins: any = await models.stock_margins.findAll();

            /// generate stock model object
            const generatedStocksObject: any = await generateStockObject(
                stock_array_object,
                stockData,
                stockIds,
                offset,
                adminId,
                vendorId,
                stockMargins,
                vendorMargin,
                req[`ip`] || ''
            );
            logger.info(`!generatedStocksObject! ${generatedStocksObject.stock_object_list?.length}`);

            if (generatedStocksObject.stock_object_list.length) {
                /// add admin id and vendor id
                const bulkCreateObject = generatedStocksObject.stock_object_list.map((item: any) =>
                    adminId ? { ...item, admin_id: adminId } : { ...item, vendor_id: vendorId }
                );

                /// filtered bulk create object
                const filteredBulkCreateObject = bulkCreateObject
                    .map((createObject: any) => ({
                        ...createObject,
                        createdAt: moment.utc(),
                        updatedAt: moment.utc()
                    }))
                    .filter(
                        (stock: any) =>
                            stock?.final_price &&
                            stock?.final_price_ori &&
                            stock?.certificate_number &&
                            ![stockStatus.onHold, stockStatus.onMemo].includes(stock?.status) &&
                            stock?.lab &&
                            !['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim()) &&
                            !naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim())
                    );

                /// create stock
                await models.stocks.bulkCreate(filteredBulkCreateObject, { transaction });

                /// obtain list for zero price stocks not uploaded
                stocksWithZeroPrice = bulkCreateObject.filter(
                    (stock: any) => !stock?.final_price || !stock?.final_price_ori
                );

                stocksWithoutCertificate = bulkCreateObject.filter((stock: any) => !stock?.certificate_number);

                stocksWithoutLab = bulkCreateObject.filter(
                    (stock: any) =>
                        !stock?.lab ||
                        ['none', 'nan', 'non', ''].includes(String(stock?.lab).toLocaleLowerCase().trim())
                );

                stocksWithNaturalDiamond = bulkCreateObject.filter((stock: any) => {
                    return naturalAlias.includes(String(stock?.growth_type).toLocaleLowerCase().trim());
                });
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock created successfully!'
                // data: stockResult
            });

            /// commit transaction
            await transaction.commit();

            //// notifications
            try {
                /// send notifications to users who have updated stocks in whish list
                let notifyStocks: any[] = [];

                if (replaceAll && generatedStocksObject.stock_object_list.length) {
                    notifyStocks = notifyStocks.concat(
                        generatedStocksObject.stock_object_list
                            .filter((filterItem: any) => filterItem?.stock_id)
                            .map((item: any) => item?.stock_id)
                    );
                }
                if (generatedStocksObject.updated_stock_list.length) {
                    notifyStocks = notifyStocks.concat(generatedStocksObject.updated_stock_list);
                }

                if (notifyStocks.length) {
                    /// stock_id not UUID
                    const notifyStockObjects: any[] = notifyStocks.map((stockId: any) => {
                        if (vendorId) {
                            return { stock_id: stockId, vendor_id: vendorId };
                        } else if (adminId) {
                            return { stock_id: stockId, admin_id: adminId };
                        }
                    });

                    userNotification.sendNotification(NotificationType.stockUpdate, notifyStockObjects);
                }
            } catch (error: any) {
                logger.error(`add stock from api notification error ${JSON.stringify(error, null, 2)}`);
            }

            /// send email for zero price stocks
            try {
                if (stocksWithZeroPrice?.length || stocksWithoutCertificate?.length || stocksWithoutLab?.length) {
                    /// send email to vendor for zero price stocks
                    customEmails.sendEmailToVendorForZeroPriceStocks(
                        stocksWithZeroPrice,
                        stocksWithoutCertificate,
                        stocksWithoutLab,
                        stocksWithNaturalDiamond
                    );
                }
            } catch (error: any) {
                logger.error(`zero price stock not uploaded error!!! ${error}`);
            }

            ////
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/stock
     *  @apiName deleteStock
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async deleteStock(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!DeleteStock function start!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const role = req[`role`];
            const reqId = req[`id`];
            const { stock_ids } = req.body;

            let deleteStockIds: string[] = [];
            const conditions: any = [];

            if (role === adminRole.vendor) {
                conditions.push({ vendor_id: reqId });
            }

            conditions.push({ id: { [Op.in]: stock_ids } });
            conditions.push({ status: stockStatus.available });

            /// find all available stock
            const stocks: any = await models.stocks.findAll({
                where: { [Op.and]: conditions }
            });

            if (!stocks.length) {
                throw new Error('Stock not found');
            }

            /// double check vendors stocks
            if (role === adminRole.vendor) {
                const isAllVendorStocks: boolean = stocks.every((stock: any) => stock?.vendor_id === reqId);
                if (!isAllVendorStocks) {
                    throw new Error('Unauthorized access!!!');
                }
            }

            /// create delete stock ids list
            deleteStockIds = stocks.map((stock: any) => stock?.id);

            /// check if stock is in offer
            const stockOffers = await models.stock_offers.findAll({
                where: {
                    [Op.and]: [
                        { stock_id: { [Op.in]: deleteStockIds } },
                        { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                    ]
                },
                attributes: ['id', 'stock_id']
            });

            /// filter out offer stocks
            if (stockOffers.length) {
                /// check if any of the delete stock ids are in offer stocks
                const isOfferCreatedOnSomeStocks: boolean = deleteStockIds.some((item: any) =>
                    stockOffers.map((offer: any) => offer?.stock_id).includes(item)
                );

                /// if offer is created on some stocks, throw error
                if (isOfferCreatedOnSomeStocks) {
                    throw new Error('Offer is created on some stocks!');
                }
            }

            /// delete stocks from table
            await models.stocks.destroy({
                where: {
                    id: { [Op.in]: deleteStockIds },
                    status: {
                        [Op.in]: [stockStatus.available, stockStatus.onHold]
                    }
                },
                transaction
            });

            /// create entries in deleted stock table
            const deletedStocks: any = stocks.filter((stock: any) => deleteStockIds.includes(stock?.id));

            /// create bulk create object for deleted stocks
            const deletedStocksBulkCreate = deletedStocks.map((stock: any) => {
                const { id, updatedAt, createdAt, ...rest } = JSON.parse(JSON.stringify(stock)); // Destructure and exclude 'id'
                return {
                    ...rest, // Spread the rest of the properties
                    stock_uuid: id, // Use the 'id' value as 'stock_uuid'
                    deleted_by_id: reqId, // Set the deleted_by_id
                    deleted_by_ip: req[`ip`],
                    delete_type: `Deleted by ${role}`,
                    createdAt: moment.utc(),
                    updatedAt: moment.utc()
                };
            });

            /// bulk create deleted stocks
            await models.deleted_stocks.bulkCreate(deletedStocksBulkCreate, { transaction });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock deleted successfully!'
            });

            await transaction.commit();
            /// deleteStocks
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/auth/admin/stock/status
     *  @apiName changeStockStatus
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async changeStockStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_active, is_featured, is_new_arrival } = req.body;

            const role = req[`role`];
            const vendorId = req[`vendor`]?.id;
            const updateObject: any = {};

            if (typeof is_active === 'boolean') {
                updateObject.is_active = is_active;
            } else if (typeof is_featured === 'boolean') {
                updateObject.is_featured = is_featured;
            } else if (typeof is_new_arrival === 'boolean') {
                updateObject.is_new_arrival = is_new_arrival;
            }

            if (Object.entries(updateObject).length === 0) {
                throw Error('Please provide all data');
            }

            const stock = await models.stocks.findOne({
                where: {
                    id,
                    _deleted: false
                }
            });

            if (!stock) {
                throw new Error('Stock not found');
            }

            if (adminRole.vendor === role) {
                if (stock.vendor_id !== vendorId) {
                    throw new Error('Unauthorized access!!');
                }
            }

            await models.stocks.update(updateObject, { where: { id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock User Status'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/admin/stock-details
     *  @apiName stockDetails
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockDetails(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;

            const stock = await models.stocks.findOne({ where: { id } });

            if (!stock) {
                throw new Error(`Stock details not found`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock details listed successfully',
                data: stock
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/admin/get-stock-details
     *  @apiName stockDetails
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockDetailsUsingStockId(req: Request, res: Response, next: NextFunction) {
        try {
            /// uuid
            const stockId = req.query.stock_id;
            const vendorId = req.query.vendor_id;
            const adminId = req.query.admin_id;

            if (!stockId) {
                throw new Error('stock_id required!!!');
            }

            if (!vendorId && !adminId) {
                throw new Error('vendor_id or admin_id are required!!!');
            }

            /// find stock using uuid
            const stockData: any = await models.stocks.findOne({
                where: {
                    [Op.and]: [
                        { stock_id: stockId },
                        vendorId ? { vendor_id: vendorId } : {},
                        adminId ? { admin_id: adminId } : {}
                    ]
                }
            });

            if (!stockData) {
                throw new Error(`Stock details not found`);
            }

            const offer: any = await models.stock_offers.findOne({
                where: {
                    [Op.and]: [
                        vendorId ? { vendor_id: vendorId } : {},
                        adminId ? { admin_id: adminId } : {},
                        { stock_id: stockData?.id },
                        { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                    ]
                }
            });

            const resultStock: any = JSON.parse(JSON.stringify(stockData));

            resultStock.is_offer_created = offer ? true : false;
            resultStock.offer_id = JSON.parse(JSON.stringify(offer))?.id;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock details listed successfully',
                data: resultStock
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    async getAllStocksCsv(req: Request, res: Response, next: NextFunction) {
        try {
            const allStocks = await models.stocks.findAll({
                where: {
                    _deleted: false
                }
            });

            const jsonData: any = [];
            for (const stock of allStocks) {
                const stockObject = stock.dataValues;
                delete stockObject.createdAt;
                delete stockObject.updatedAt;
                delete stockObject._deleted;
                delete stockObject.vendor_id;
                delete stockObject.admin_id;
                delete stockObject.id;
                delete stockObject.is_featured;
                delete stockObject.is_new_arrival;
                delete stockObject.is_active;
                delete stockObject.is_offer_created;
                delete stockObject.is_buy_request_created;
                delete stockObject.is_wishlist;
                jsonData.push(stockObject);
            }

            if (jsonData.length > 0) {
                const filePathToSaveCsv = path.join(__dirname, `../../../${Date.now()}.csv`);
                const fileName = `${Date.now()}.csv`;

                const csvData = parse(jsonData);
                fs.writeFileSync(filePathToSaveCsv, csvData, 'utf8');
                logger.info(`CSV file saved at ${filePathToSaveCsv}`);

                res.json({
                    status: httpStatusCodes.SUCCESS_CODE,
                    message: 'Stocks CSV file created successfully',
                    data: fileName
                });

                return;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'No stocks found'
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Stock();
