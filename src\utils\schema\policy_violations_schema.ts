import Joi from 'joi';

/**
 * Schema for listing policy violations
 */
export const policyViolationListSchema = Joi.object({
    query: Joi.object({
        page: Joi.number().integer().min(1).optional().default(1),
        limit: Joi.number().integer().min(1).max(100).optional().default(10),
        violation_type: Joi.string().valid('DUPLICATE_DIAMOND', 'UNFULFILLED_ORDER', 'LATE_SHIPMENT').optional(),
        vendor_id: Joi.string().uuid().optional()
    }).optional(),
    params: Joi.object({
        vendor_id: Joi.string().uuid().optional()
    }).optional()
});

/**
 * Schema for policy violation statistics
 */
export const policyViolationStatisticsSchema = Joi.object({
    query: Joi.object({
        vendor_id: Joi.string().uuid().optional()
    }).optional()
});
