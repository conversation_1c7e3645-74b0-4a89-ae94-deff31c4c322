import { NextFunction, Request, Response } from 'express';
import models from '../../models';

import { adminRole, buyRequestStatus, httpStatusCodes, OfferStatus, stockStatus } from '../../utils/constants';
import { Op } from 'sequelize';
import { logger } from '../../utils/logger';
import axios from 'axios';

class Dashboard {
    // constructor() { }

    /*
        --------------------------------------------------------------------------------
        Dashboard functions
    */

    /**
     *  @api {post} /v1/auth/user/kpi
     *  @apiName user kpi
     *  @apiGroup User
     *
     *  @apiSuccess {Object} User
     */

    async listCounts(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req[`role`];
            const id = req[`id`];

            if (role !== adminRole.user) {
                throw new Error('unauthorized error!!!');
            }

            /// count updated buy requests
            const buy_request_count = await models.buy_requests.count({
                where: {
                    [Op.and]: [
                        { [Op.or]: [{ status: buyRequestStatus.updated }, { is_capture_failed: true }] },
                        { user_id: id }
                    ]
                }
            });

            /// count unread count
            const unread_notification_count = await models.user_notifications.count({
                where: { user_id: id, is_read: false }
            });

            /// count users pending offers
            const user_pending_offers_count = await models.stock_offers.count({
                where: {
                    [Op.and]: [{ user_id: id }, { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }]
                }
            });

            /// count pending offers
            const pending_offers_count = await models.stock_offers.count({
                where: {
                    status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                }
            });

            /// total diamonds
            const diamonds_count = await models.stocks.count({
                where: {
                    [Op.and]: [{ status: stockStatus.available }]
                }
            });

            /// gold price
            const goldPriceResponse: any = await new Promise((resolve, reject) => {
                axios
                    .get(`https://data-asg.goldprice.org/dbXRates/USD`, {
                        headers: {
                            'User-Agent': 'Mozilla/5.0', // Mimic a browser request
                            Referer: 'https://data-asg.goldprice.org/'
                        }
                    })
                    .then(async (response: any) => {
                        resolve(JSON.parse(JSON.stringify(response.data.items[0])));
                    })
                    .catch((error) => {
                        logger.error(`gold price error: ${error}`);
                        resolve({});
                    });
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'successfully listed counts',
                data: {
                    show_offers_module: String(process.env.SHOW_OFFERS_MODULE).toLowerCase() === 'true',
                    buy_request_count,
                    unread_notification_count,
                    user_pending_offers_count,
                    pending_offers_count,
                    diamonds_count,
                    current_gold_rate: goldPriceResponse?.xauPrice ?? 0,
                    change_percent: goldPriceResponse?.pcXau ?? 0,
                    change_amount: goldPriceResponse?.chgXau ?? 0,
                    calculated_change_amount: parseFloat(
                        ((goldPriceResponse?.xauPrice ?? 0) - (goldPriceResponse?.xauClose ?? 0)).toFixed(2)
                    ),
                    gold_prices: goldPriceResponse
                }
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Dashboard();
