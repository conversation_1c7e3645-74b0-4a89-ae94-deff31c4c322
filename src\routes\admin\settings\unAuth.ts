import { IRouter, NextFunction, Request, Response, Router } from 'express';
import settings from '../../../controllers/admin/settings';
import { fieldsValidator } from '../../../middlewares/validator';
import { resetPasswordSchema, sendVerificationOtpSchema } from '../../../utils/schema/settings_schema';

const router: IRouter = Router();

// reset password
router.post(
    '/reset-password',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, resetPasswordSchema),
    settings.resetPassword
);

// send verification otp
router.put(
    '/send-verification-otp',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, sendVerificationOtpSchema),
    settings.sendVerificationOtp
);

export default router;
