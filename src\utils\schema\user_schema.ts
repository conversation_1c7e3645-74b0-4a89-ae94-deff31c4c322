import Joi from 'joi';
import { isPhoneNumber } from '../../utils/validators';

export const createUserSchema = Joi.object({
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().required(),
    username: Joi.string().allow(null).optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .required(),
    mobile: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .allow(null, '')
        .optional(),
    profile_image: Joi.string().allow(null).optional(),
    shipping_address: Joi.object().required(),
    billing_address: Joi.object().allow(null).optional(),
    is_address_same: Joi.boolean().allow(null).optional(),
    fax: Joi.string().allow(null).optional(),
    website: Joi.string().allow(null).optional(),
    zip_code: Joi.string().required(),
    state: Joi.string().optional(),
    city: Joi.string().optional(),
    country: Joi.string().required(),
    password: Joi.string().optional(),
    order_count: Joi.number().integer().allow(null).optional(),
    legal_registered_name: Joi.string().required(),
    company_operating_name: Joi.string().allow(null).optional(),
    fed_tax_id: Joi.string().required(),
    resale_tax: Joi.string().allow(null).optional(),
    jbt_id: Joi.string().allow(null).optional(),
    has_AMLprogram: Joi.boolean().default(false),
    how_they_heard: Joi.string().allow(null).optional(),
    facebook_id: Joi.string().allow(null).optional(),
    instagram_id: Joi.string().allow(null).optional(),
    type_of_business: Joi.string().required(),
    business_start_date: Joi.date().required(),
    years_at_present_location: Joi.string().required(),
    legal_organization_status: Joi.object({
        type: Joi.string().valid('Private Corp', 'Partnership', 'Individual').required(),
        registration_or_incorporation_country: Joi.string().required(),
        registration_or_incorporation_state: Joi.string().required()
    }).required(),
    order_authorized_person: Joi.array()
        .items(
            Joi.object({
                designation: Joi.string().required(),
                first_name: Joi.string().required(),
                last_name: Joi.string().required(),
                mobile_no: Joi.string()
                    .custom((value, helper) => isPhoneNumber(value, helper))
                    .required()
            }).required()
        )
        .required(),
    document_name: Joi.string().required(),
    trade_references: Joi.array()
        .items(
            Joi.object({
                company_name: Joi.string().required(),
                contact_person_name: Joi.string().required(),
                phone: Joi.string()
                    .custom((value, helper) => isPhoneNumber(value, helper))
                    .required(),
                email: Joi.string().email().optional()
            }).required()
        )
        .optional(),
    business_entity_name: Joi.string().required(),
    signature_image_name: Joi.string().required(),
    sign_date: Joi.date().required(),
    print_name: Joi.string().required()
});

export const updateUserSchema = Joi.object({
    // id: Joi.string().uuid().required(),
    first_name: Joi.string().optional(),
    last_name: Joi.string().optional(),
    email: Joi.string().email().optional(),
    username: Joi.string().allow(null, '').optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    mobile: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .allow(null, '')
        .optional(),
    profile_image: Joi.string().allow(null).optional(),
    shipping_address: Joi.object().optional(),
    billing_address: Joi.object().allow(null).optional(),
    is_address_same: Joi.boolean().allow(null).optional(),
    fax: Joi.string().allow(null, '').optional(),
    website: Joi.string().allow(null, '').optional(),
    zip_code: Joi.string().optional(),
    state: Joi.string().allow(null, '').optional(),
    city: Joi.string().allow(null, '').optional(),
    country: Joi.string().optional(),
    order_count: Joi.number().integer().allow(null).optional(),
    legal_registered_name: Joi.string().optional(),
    company_operating_name: Joi.string().allow(null, '').optional(),
    fed_tax_id: Joi.string().optional(),
    resale_tax: Joi.string().allow(null, '').optional(),
    jbt_id: Joi.string().allow(null, '').optional(),
    has_AMLprogram: Joi.boolean().default(false).optional(),
    how_they_heard: Joi.string().allow(null, '').optional(),
    facebook_id: Joi.string().allow(null, '').optional(),
    instagram_id: Joi.string().allow(null, '').optional(),
    type_of_business: Joi.string().optional(),
    business_start_date: Joi.date().optional(),
    years_at_present_location: Joi.string().optional(),
    legal_organization_status: Joi.object({
        type: Joi.string().valid('Private Corp', 'Partnership', 'Individual').required(),
        registration_or_incorporation_country: Joi.string().required(),
        registration_or_incorporation_state: Joi.string().required()
    }).optional(),
    order_authorized_person: Joi.array()
        .items(
            Joi.object({
                designation: Joi.string().required(),
                first_name: Joi.string().required(),
                last_name: Joi.string().required(),
                mobile_no: Joi.string()
                    .custom((value, helper) => isPhoneNumber(value, helper))
                    .required()
            }).required()
        )
        .optional(),
    document_name: Joi.string().optional(),
    trade_references: Joi.array()
        .items(
            Joi.object({
                company_name: Joi.string().required(),
                contact_person_name: Joi.string().required(),
                phone: Joi.string()
                    .custom((value, helper) => isPhoneNumber(value, helper))
                    .required(),
                email: Joi.string().email().optional()
            }).required()
        )
        .optional(),
    business_entity_name: Joi.string().optional(),
    signature_image_name: Joi.string().optional(),
    sign_date: Joi.date().optional(),
    print_name: Joi.string().optional()
});

export const changeUserStatusSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_active: Joi.boolean().required()
});

export const blockUserSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_blocked: Joi.boolean().required()
});

export const loginUserSchema = Joi.object({
    email: Joi.string().email().optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    password: Joi.string().min(4).required(),
    fcm_token: Joi.string().optional().allow('')
});

export const checkRegistrationStatusSchema = Joi.object({
    email: Joi.string().email().required()
});

export const changeUserEmailVerificationStatusSchema = Joi.object({
    email: Joi.string().email().required(),
    otp: Joi.number().min(6).required()
});

export const checkEmailPhoneExistsSchema = Joi.object({
    email: Joi.string().email().optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional()
});

export const changeUserPhoneVerificationStatusSchema = Joi.object({
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .required(),
    otp: Joi.number().min(6).required()
});

export const verifyUserSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_verified: Joi.boolean().required(),
    credit_limit: Joi.number().optional(),
    kyc_reject_reason: Joi.string().optional()
});

export const updateCreditLimitSchema = Joi.object({
    id: Joi.string().uuid().required(),
    credit_limit: Joi.number().required()
});

export const listUsersDetails = Joi.object({
    ids: Joi.array().required()
});

export const chargeCreditCard = Joi.object({
    buy_request_id: Joi.string().uuid().required(),
    card_number: Joi.string().creditCard().required(),
    exp_date: Joi.string()
        .pattern(/^(20[2-9][0-9]|2100)-(0[1-9]|1[0-2])$/) // Matches "YYYY-MM" format for years 2020-2100
        .required()
        .messages({ 'string.pattern.base': 'Expiration Date must be in YYYY-MM format' }),
    cvv: Joi.string()
        .pattern(/^[0-9]{3,4}$/)
        .required()
        .messages({ 'string.pattern.base': 'CVV must be 3 or 4 digits' })
});
