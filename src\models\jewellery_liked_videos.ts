import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface JewelleryLikedVideoAttributes {
    id?: string;
    jewellery_showcase_id?: string;
    user_id?: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface JewelleryLikedVideoCreationAttributes extends Optional<JewelleryLikedVideoAttributes, 'id'> { }

interface JewelleryLikedVideoInstance
    extends Model<JewelleryLikedVideoAttributes, JewelleryLikedVideoCreationAttributes>,
    JewelleryLikedVideoAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type JewelleryLikedVideoStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => JewelleryLikedVideoInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const jewellery_liked_videos = sequelize.define<JewelleryLikedVideoInstance>(
        'jewellery_liked_videos',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            jewellery_showcase_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as JewelleryLikedVideoStatic;

    // TODO: make common function to sync
    // await jewellery_liked_videos.sync({ alter: true });

    return jewellery_liked_videos;
};
