import { IRouter, NextFunction, Request, Response, Router } from 'express';

import user from '../../../controllers/user/user';

import { fieldsValidator } from '../../../middlewares/validator';
import { changeEmailSchema, changePasswordSchema } from '../../../utils/schema/settings_schema';
import { updateUserSchema } from '../../../utils/schema/user_schema';

const routes: IRouter = Router();

// user change email
routes.put(
    '/change-email',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeEmailSchema),
    user.changeUserEmail
);

// user change password
routes.put(
    '/change-password',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changePasswordSchema),
    user.changeUserPassword
);

// update user
routes.post(
    '/update',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateUserSchema),
    user.updateUser
);

// get user details
routes.get('/details', user.userDetails);

// delete user
routes.put('/delete', user.deleteUser);

export default routes;
