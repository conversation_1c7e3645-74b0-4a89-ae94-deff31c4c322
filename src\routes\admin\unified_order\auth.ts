import { fieldsValidator } from '../../../middlewares/validator';
import unifiedOrder from '../../../controllers/admin/unified_order';

import { Request, Response, IRouter, NextFunction, Router } from 'express';
import { cancelOrderStatusSchema, updateOrderStatusSchema } from '../../../utils/schema/order_schema';

const router: IRouter = Router();

// list unified orders
router.get('/unified-order', unifiedOrder.listUnifiedOrder);

// list unified order details
router.get('/unified-order-details', unifiedOrder.unifiedOrderDetails);

// update order status
router.put(
    '/unified-order-status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateOrderStatusSchema),
    unifiedOrder.updateUnifiedOrderStatus
);

// cancel order
router.put(
    '/unified-cancel-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, cancelOrderStatusSchema),
    unifiedOrder.cancelUnifiedOrder
);

export default router;
