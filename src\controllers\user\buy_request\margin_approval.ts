import { Request, Response, NextFunction } from 'express';
import { adminRole, buyRequestStatus, httpStatusCodes } from '../../../utils/constants';
import { logger } from '../../../utils/logger';
import models from '../../../models';
import { Op } from 'sequelize';

class MarginBuyRequest {
    /**
     * @api {put} /v1/auth/buy-request/margin-approval
     * @apiName createNewBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async approveMarginBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info(`calling approve margin buy request`);
        try {
            const { stock_id, buy_request_id, updated_price } = req.body;
            const role = req[`role`];

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error(`You are not authorized to approve margin buy request`);
            }

            /// fetch the buy request details
            const buyRequestDetails = await models.buy_requests.findOne({
                where: {
                    id: buy_request_id,
                    status: buyRequestStatus.pending
                }
            });

            if (!buyRequestDetails) {
                throw new Error(`Buy request not found`);
            }

            /// check all vendors margin is approved
            if (buyRequestDetails?.is_margin_approved) {
                throw new Error(`Buy request margin already approved`);
            }

            /// check for vendor id margin approval
            const isAlreadyApproved =
                buyRequestDetails.stock_ids?.find((stockId: any) => stockId?.stock_id === stock_id)
                    ?.is_margin_approved ?? false;

            if (isAlreadyApproved) {
                throw new Error(`Margin already approved`);
            }

            /// stocks
            const stock: any = await models.stocks.findOne({
                where: { id: stock_id },
                attributes: ['id', 'weight', 'final_price_ori', 'price_per_caret_ori', 'final_price', 'stock_margin']
            });

            if (!stock) {
                throw new Error(`Stock not found`);
            }

            /// update vendor prices and margin in stock ids
            const updatedStockIds = buyRequestDetails.stock_ids.map((stockId: any) => {
                if (stockId?.stock_id === stock_id) {

                    /// updated price can not be grater than vendor stock price
                    if (parseFloat(parseFloat(updated_price).toFixed(2)) > parseFloat(parseFloat(stock?.final_price_ori).toFixed(2))) {
                        throw new Error(`Updated price can not be greater than vendor stock price`);
                    }


                    // find the vendor stock id from vendor stocks
                    /// return same values if vendor margin is same as stock margin
                    if (parseFloat(stock?.final_price_ori).toFixed(2) === parseFloat(updated_price).toFixed(2)) {
                        return {
                            ...stockId,
                            is_margin_approved: true,
                            stock: {
                                price_per_caret_ori: parseFloat(stock?.price_per_caret_ori.toFixed(2)),
                                final_price_ori: parseFloat(stock?.final_price_ori.toFixed(2)),
                                stock_margin: parseFloat(stock?.stock_margin.toFixed(2))
                            },
                            vendor_margin: 0
                        };
                    }

                    /// update vendor stock margin
                    const finalPriceOrigin = parseFloat(updated_price);
                    const updatedStockMargin =
                        ((parseFloat(stock?.final_price) - finalPriceOrigin) / finalPriceOrigin) * 100;
                    const pricePerCaretOrigin = finalPriceOrigin / stock?.weight;
                    const vendorMargin =
                        ((parseFloat(stock?.price_per_caret_ori) - pricePerCaretOrigin) /
                            parseFloat(stock?.price_per_caret_ori)) *
                        100;

                    return {
                        ...stockId,
                        stock: {
                            price_per_caret_ori: parseFloat(pricePerCaretOrigin.toFixed(2)),
                            final_price_ori: parseFloat(finalPriceOrigin.toFixed(2)),
                            stock_margin: parseFloat(updatedStockMargin.toFixed(2))
                        },
                        vendor_margin: parseFloat(vendorMargin.toFixed(2)),
                        is_margin_approved: true
                    };
                }
                return stockId;
            });

            /// check if all vendors margin is approved
            const isAllVendorsMarginApproved = updatedStockIds.every((stockId: any) => stockId?.is_margin_approved);

            /// update stock ids in buy request
            await models.buy_requests.update(
                { stock_ids: updatedStockIds, is_margin_approved: isAllVendorsMarginApproved },
                { where: { id: buy_request_id } }
            );

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Vendor margin updated successfully`
            });

            ///
        } catch (error) {
            logger.error(error);
            next(error);
        }
    }
}

export default new MarginBuyRequest();
