import moment from 'moment';

export const validatePhoneNumber = (phoneNumber: string) => {
    const regex = /^\+(?:[0-9] ?){6,14}[0-9]$/;
    return regex.test(phoneNumber);
};

export const isPhoneNumber = (value: any, helpers: any) => {
    if (!validatePhoneNumber(value)) {
        // return helpers.error('invalid phone number');
    }
    return value;
};

export const isOTPExpired = (timeStamp: string) => {
    const parsedTimeStamp = moment(timeStamp);

    const expirationTime = parsedTimeStamp.clone().add(10, 'minutes');

    return moment().isAfter(expirationTime);
};

export const booleanStringToBool = (value: any, helpers: any) => {
    if (typeof value === 'string') {
        if (value.toLowerCase() === 'true') {
            return true;
        } else if (value.toLowerCase() === 'false') {
            return false;
        }
    }
    return helpers.error('Something went wrong!!!'); // This will produce a validation error if the conversion fails
};
