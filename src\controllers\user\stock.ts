import { NextFunction, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import { col, fn, Op, Sequelize } from 'sequelize';
import models, { sequelize } from '../../models';
import {
    adminRole,
    cleanObject,
    countryAlias,
    httpStatusCodes,
    isImageUrl,
    OfferStatus,
    stockStatus
} from '../../utils/constants';
import getFilterStockFields from '../../controllers/admin/stock/getFilterStockFields';

class Stock {
    /**
     * @api {post} /v1/auth/user/stock
     * @apiName listStocks
     * @apiGroup UserStocks
     *
     *
     * @apiSuccess {Object} Stocks.
     */
    async listStocks(req: Request, res: Response, next: NextFunction) {
        logger.info(`!!!!!!listStocks function start!!!!! ${req[`id`]}`);
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            const is_featured = req.query.is_featured?.toString().toLowerCase() === 'true';
            const is_new_arrival = req.query.is_new_arrival?.toString().toLowerCase() === 'true';
            const filterObject = req.body.filterObject;
            const sortBy = req.body.sortBy;
            const sortOrder = req.body.sortOrder;
            const status = req.body.status;

            const conditions: any = [
                {
                    is_active: true
                },
                {
                    _deleted: false
                }
            ];

            if (is_featured) {
                conditions.push({ is_featured });
            }

            /// fetch last 7 days data and is_new_arrival true
            if (is_new_arrival) {
                conditions.push({
                    [Op.or]: [
                        {
                            updatedAt: {
                                [Op.gte]: sequelize.literal("NOW() - INTERVAL '7 days'")
                            }
                        },
                        {
                            is_new_arrival: true
                        }
                    ]
                });
            }

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (filterObject) {
                const filterObjectArray = await getFilterStockFields.getFilters(cleanObject(filterObject));
                conditions.push({
                    [Op.and]: filterObjectArray
                });
            }

            if (status) {
                conditions.push({ status });
            } else {
                /// exclude RETURNED diamonds
                conditions.push({ status: { [Op.ne]: 'RETURNED' } });
            }

            /// excludes stock with 0 price
            conditions.push({
                [Op.or]: [{ final_price: { [Op.gt]: 0 } }, { final_price_ori: { [Op.gt]: 0 } }]
            });

            const { rows, count } = await models.stocks.findAndCountAll({
                where: {
                    [Op.and]: [
                        ...conditions,
                        {
                            [Op.or]: [{ vendor_id: null }, { '$vendor.is_verified$': true }]
                        }
                    ]
                },
                include: [
                    {
                        model: models.vendors,
                        required: false,
                        attributes: []
                    },
                    {
                        model: models.wishlist_stocks,
                        as: 'wishlist_stocks',
                        where: {
                            user_id: req[`id`]
                        },
                        required: false,
                        attributes: []
                    },
                    {
                        model: models.stock_offers,
                        as: 'stock_offers',
                        where: {
                            [Op.and]: [
                                { user_id: req[`id`] },
                                { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                            ]
                        },
                        required: false, // Allows stocks without offers to be returned
                        attributes: []
                    }
                ],
                attributes: {
                    include: [
                        [Sequelize.col('wishlist_stocks'), 'is_wishlist'],
                        [
                            Sequelize.literal('CASE WHEN "stock_offers"."id" IS NOT NULL THEN true ELSE false END'),
                            'is_offer_created'
                        ]
                    ]
                },
                distinct: true,
                order: [
                    ...(sortBy && sortOrder ? [[sortBy, sortOrder]] : []),
                    ['createdAt', 'DESC'],
                    ['sku_number', 'ASC']
                ],
                subQuery: false,
                offset: skip,
                limit
            });

            rows.forEach((stock: any) => {
                stock.is_wishlist = !!stock.is_wishlist;
            });

            /// manage image video urls
            for (const stock of rows) {
                let tempImage = stock?.diamond_image;
                let tempVideo = stock?.diamond_video;
                if (isImageUrl(stock?.diamond_video)) {
                    tempImage = stock?.diamond_video;
                }
                if (!isImageUrl(stock?.diamond_image)) {
                    tempVideo = stock?.diamond_image;
                }
                stock.diamond_image = tempImage;
                stock.diamond_video = tempVideo;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stocks successfully listed',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/stock
     * @apiName listStocks
     * @apiGroup UserStocks
     *
     *
     * @apiSuccess {Object} Stocks.
     */
    async listStocksOptimized(req: Request, res: Response, next: NextFunction) {
        logger.info(`!!!!!!listStocks function start!!!!! ${req[`id`]}`);
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            const is_featured = req.query.is_featured?.toString().toLowerCase() === 'true';
            const is_new_arrival = req.query.is_new_arrival?.toString().toLowerCase() === 'true';
            const filterObject = req.body.filterObject;
            const sortBy = req.body.sortBy;
            const sortOrder = req.body.sortOrder;
            const status = req.body.status;
            const userId = req[`id`];
            const userData = req[`user`];

            const conditions: any = [
                {
                    is_active: true
                },
                {
                    _deleted: false
                }
            ];

            if (is_featured) {
                conditions.push({ is_featured });
            }

            /// fetch last 7 days data and is_new_arrival true
            if (is_new_arrival) {
                conditions.push({
                    [Op.or]: [
                        {
                            updatedAt: {
                                [Op.gte]: sequelize.literal("NOW() - INTERVAL '7 days'")
                            }
                        },
                        {
                            is_new_arrival: true
                        }
                    ]
                });
            }

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (filterObject) {
                const filterObjectArray = await getFilterStockFields.getFilters(cleanObject(filterObject));
                conditions.push({
                    [Op.and]: filterObjectArray
                });
            }

            if (status) {
                conditions.push({ status });
            } else {
                /// exclude RETURNED diamonds
                conditions.push({ status: { [Op.ne]: 'RETURNED' } });
            }

            /// excludes stock with 0 price
            conditions.push({
                [Op.or]: [{ final_price: { [Op.gt]: 0 } }, { final_price_ori: { [Op.gt]: 0 } }]
            });

            /// fetch all stocks provided conditions
            const allStocksResult: any = await models.stocks.findAndCountAll({
                where: {
                    [Op.and]: conditions
                },
                order: [
                    // Prioritize stocks with non-null image_data or diamond_image
                    [
                        Sequelize.literal(`
                            CASE
                                WHEN (image_data IS NOT NULL AND image_data != '' AND image_data != 'null')
                                  OR (diamond_image IS NOT NULL AND diamond_image != '' AND diamond_image != 'null')
                                THEN 0
                                ELSE 1
                            END
                        `),
                        'ASC'
                    ],
                    ...(sortBy && sortOrder ? [[sortBy, sortOrder]] : []),
                    ['createdAt', 'DESC'],
                    ['sku_number', 'ASC']
                ],
                offset: skip,
                limit
            });

            let rows: any = allStocksResult?.rows;
            const count = allStocksResult?.count;

            /// extract vendor ids
            const vendorsIds = rows?.filter((stock: any) => stock?.vendor_id).map((item: any) => item?.vendor_id);

            if (vendorsIds?.length) {
                /// vendors
                const vendors: any = await models.vendors.findAll({
                    where: {
                        id: { [Op.in]: vendorsIds }
                    },
                    attributes: ['id', 'is_verified', 'is_test_vendor', 'is_active', 'is_blacklisted'],
                    raw: true
                });

                /// to be removed vendor ids
                const toBeRemovedVendorIds = vendors
                    ?.filter((vendor: any) => {
                        /// remove non verified vendors stocks
                        const isUnverified = !vendor?.is_verified;

                        /// remove test vendors stocks for normal users
                        /// keep test vendors stocks for test users
                        const isInvalidTestVendor = !userData?.is_test_user && vendor?.is_test_vendor;

                        /// remove inactive vendors stocks
                        const isInactive = !vendor?.is_active;

                        /// remove blacklisted vendors stocks
                        const isBlacklisted = vendor?.is_blacklisted;

                        return isUnverified || isInvalidTestVendor || isInactive || isBlacklisted;
                    })
                    .map((vendor: any) => vendor?.id);

                /// remove non verified vendors stocks
                rows = rows?.filter((stock: any) => !toBeRemovedVendorIds.includes(stock?.vendor_id));
            }

            /// is_wishlist conditions
            const wishlistStocksConditions = rows?.map((item: any) => ({
                [Op.and]: [{ admin_id: item?.admin_id }, { vendor_id: item?.vendor_id }, { stock_id: item?.stock_id }]
            }));

            /// find wishlist_stocks
            const wishlistStocks: any = await models.wishlist_stocks.findAll({
                where: {
                    [Op.and]: [{ user_id: userId }, { [Op.or]: wishlistStocksConditions }]
                },
                attributes: ['id', 'stock_id', 'vendor_id', 'admin_id']
            });

            /// is_offer_created conditions
            const stockOfferConditions = rows?.map((item: any) => ({
                [Op.and]: [{ stock_id: item?.id }, { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }]
            }));

            /// find stock offers
            const stockOffers: any = await models.stock_offers.findAll({
                where: {
                    [Op.and]: [{ user_id: userId }, { [Op.or]: stockOfferConditions }]
                },
                attributes: ['id', 'user_id', 'stock_id', 'vendor_id', 'admin_id']
            });

            rows?.forEach((stock: any) => {
                /// set is_offer_created
                stock.is_offer_created = !!stockOffers?.find(
                    (item: any) =>
                        item?.stock_id === stock?.id &&
                        item?.vendor_id === stock?.vendor_id &&
                        item?.admin_id === stock?.admin_id
                );
                /// set is_wishlist
                stock.is_wishlist = !!wishlistStocks?.find(
                    (item: any) =>
                        item?.stock_id === stock?.stock_id &&
                        item?.vendor_id === stock?.vendor_id &&
                        item?.admin_id === stock?.admin_id
                );

                /// manage image video urls
                let tempImage = stock?.diamond_image;
                let tempVideo = stock?.diamond_video;
                if (isImageUrl(stock?.diamond_video)) {
                    tempImage = stock?.diamond_video;
                }
                if (!isImageUrl(stock?.diamond_image)) {
                    tempVideo = stock?.diamond_image;
                }
                stock.diamond_image = tempImage;
                stock.diamond_video = tempVideo;
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stocks successfully listed',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/user/stock-details
     *  @apiName stockDetails
     *  @apiGroup UserStocks
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockDetails(req: Request, res: Response, next: NextFunction) {
        try {
            /// uuid
            const id = req.query.id;

            /// find stock using uuid
            const stockData: any = await models.stocks.findOne({
                where: { id },
                attributes: ['id', 'stock_id', 'status']
            });

            if (!stockData) {
                throw new Error(`Stock details not found`);
            }

            /// include wish list using stock_id
            const stock = await models.stocks.findOne({
                where: { id },
                include: [
                    {
                        model: models.wishlist_stocks,
                        as: 'wishlist_stocks',
                        where: {
                            user_id: req[`id`],
                            stock_id: stockData?.stock_id
                        },
                        required: false,
                        attributes: []
                    }
                ],
                attributes: {
                    include: [[Sequelize.col('wishlist_stocks'), 'is_wishlist']]
                }
            });

            if (!stock) {
                throw new Error(`Stock details not found`);
            }

            stock.is_wishlist = !!stock.is_wishlist;

            const offer: any = await models.stock_offers.findOne({
                where: {
                    [Op.and]: [
                        { user_id: req[`id`] },
                        { stock_id: stockData?.id },
                        { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                    ]
                }
            });

            const resultStock: any = JSON.parse(JSON.stringify(stock));

            resultStock.is_offer_created = offer ? true : false;
            resultStock.offer_id = JSON.parse(JSON.stringify(offer))?.id;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock details listed successfully',
                data: resultStock
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/user/get-stock-details
     *  @apiName stockDetails
     *  @apiGroup UserStocks
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockDetailsFromStockId(req: Request, res: Response, next: NextFunction) {
        logger.info(`!!!!!!getStockDetailsFromStockId function start!!!!! ${req[`id`]}`);
        try {
            /// uuid
            const stockId = req.query.stock_id;
            const vendorId = req.query.vendor_id;
            const adminId = req.query.admin_id;
            const sku = req.query.sku;

            /// validate stock_id, vendor_id, admin_id
            if (!sku) {
                /// validate stock_id, vendor_id, admin_id
                if (!stockId) {
                    throw new Error('stock_id required!!!');
                }

                /// validate vendor_id, admin_id
                if (!vendorId && !adminId) {
                    throw new Error('vendor_id or admin_id are required!!!');
                }
            }

            /// find stock using uuid
            const stockData: any = await models.stocks.findOne({
                where: {
                    [Op.and]: [
                        sku ? { sku_number: sku } : {},
                        stockId ? { stock_id: stockId } : {},
                        vendorId ? { vendor_id: vendorId } : {},
                        adminId ? { admin_id: adminId } : {}
                    ]
                },
                attributes: ['id', 'stock_id', 'status']
            });

            if (!stockData) {
                throw new Error(`Stock details not found`);
            }

            /// include wish list using stock_id
            const stock = await models.stocks.findOne({
                where: { id: stockData.id },
                include: [
                    {
                        model: models.wishlist_stocks,
                        as: 'wishlist_stocks',
                        where: {
                            user_id: req[`id`],
                            stock_id: stockData?.stock_id
                        },
                        required: false,
                        attributes: []
                    }
                ],
                attributes: {
                    include: [[Sequelize.col('wishlist_stocks'), 'is_wishlist']]
                }
            });

            if (!stock) {
                throw new Error(`Stock details not found`);
            }

            stock.is_wishlist = !!stock.is_wishlist;

            const offer: any = await models.stock_offers.findOne({
                where: {
                    [Op.and]: [
                        { user_id: req[`id`] },
                        { stock_id: stockData?.id },
                        { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                    ]
                }
            });

            const resultStock: any = JSON.parse(JSON.stringify(stock));

            resultStock.is_offer_created = offer ? true : false;
            resultStock.offer_id = JSON.parse(JSON.stringify(offer))?.id;

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock details listed successfully',
                data: resultStock
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/user/stock-status
     *  @apiName stockStatus
     *  @apiGroup UserStocks
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const stockIds = req.body.ids;

            const results: any = [];

            const stocks = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: ['id', 'stock_id', 'status']
            });

            if (stockIds.length) {
                for (const id of stockIds) {
                    const stock: any = stocks?.find((item: any) => item?.id === id);
                    results.push({ id, stock_id: stock?.stock_id, status: stock?.status ?? 'HOLD' });
                }
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock status listed successfully',
                data: results
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/user/stock-countries
     *  @apiName stockCountries
     *  @apiGroup UserStocks
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockCountries(req: Request, res: Response, next: NextFunction) {
        try {
            // Fetch distinct locations
            const locations = await models.stocks.findAll({
                attributes: [[fn('DISTINCT', fn('LOWER', col('location'))), 'location']],
                distinct: true,
                where: {
                    location: {
                        [Op.ne]: '',
                        [Op.not]: null
                    }
                },
                raw: true
            });

            // Fetch distinct cities
            const cities = await models.stocks.findAll({
                attributes: [[fn('DISTINCT', fn('LOWER', col('city'))), 'city']],
                distinct: true,
                where: {
                    city: {
                        [Op.ne]: '',
                        [Op.not]: null
                    }
                },
                raw: true
            });

            // Fetch distinct countries
            const countries = await models.stocks.findAll({
                attributes: [[fn('DISTINCT', fn('LOWER', col('country'))), 'country']],
                distinct: true,
                where: {
                    country: {
                        [Op.ne]: '',
                        [Op.not]: null
                    }
                },
                raw: true
            });

            // Combine results into a single list of unique values
            const rawValues = [
                ...locations.map((loc: any) => loc.location.toLowerCase()),
                ...cities.map((city: any) => city.city.toLowerCase()),
                ...countries.map((country: any) => country.country.toLowerCase())
            ];

            const filteredLocations: string[] = [];

            rawValues.forEach((location) => {
                for (const key in countryAlias) {
                    if (countryAlias[key].includes(location.toLowerCase())) {
                        if (!filteredLocations.includes(key.toLowerCase())) {
                            filteredLocations.push(key);
                        }
                    }
                }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock countries listed successfully',
                data: filteredLocations // Array.from(uniqueValues)
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Stock();
