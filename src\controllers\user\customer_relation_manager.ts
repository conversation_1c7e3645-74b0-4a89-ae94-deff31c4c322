import { NextFunction, Request, Response } from 'express';
import models from '../../models';
import { Op } from 'sequelize';
import { adminRole, httpStatusCodes } from '../../utils/constants';
import bcrypt from 'bcrypt';
import { logger } from '../../utils/logger';
import userNotifications from '../user/user_notifications/user_notification';

class CustomerRelationManager {
    /**
     * @api {get} /v1/user/crm
     * @apiName CustomerRelationManager
     * @apiGroup CustomerRelationManager
     *
     *
     * @apiSuccess {Object} CustomerRelationManager.
     */
    async getCRMDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('calling getCRMDetails!!');
        try {
            
            const is_vendor = String(req.query.is_vendor).toLowerCase() === 'true';

            const {
                CRM_WHATS_APP,
                CRM_TELEGRAM,
                CRM_PHONE,
                CRM_APPOINTMENT,
                CRM_VENDOR_WHATS_APP,
                CRM_VENDOR_TELEGRAM,
                CRM_VENDOR_PHONE,
                CRM_VENDOR_APPOINTMENT
            } = process.env;

            let crmDetails: any = {};

            if (is_vendor) {
                crmDetails = {
                    whats_app: CRM_VENDOR_WHATS_APP,
                    telegram: CRM_VENDOR_TELEGRAM,
                    phone: CRM_VENDOR_PHONE,
                    appointment: CRM_VENDOR_APPOINTMENT
                };
            } else {
                crmDetails = {
                    whats_app: CRM_WHATS_APP,
                    telegram: CRM_TELEGRAM,
                    phone: CRM_PHONE,
                    appointment: CRM_APPOINTMENT
                };
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Customer Relation Manager details listed successfully`,
                data: crmDetails
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new CustomerRelationManager();
