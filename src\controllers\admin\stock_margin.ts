import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import { httpStatusCodes, OfferStatus, stockStatus } from '../../utils/constants';
import models, { sequelize } from '../../models';
import { Op, Sequelize, where } from 'sequelize';
import getStockFields from './stock/getStockFields';
import { PriceNetService, roundAlias, StockItem } from './stock/pricenet';

class StockMargin {
    /**
     * @api {post} /v1/auth/admin/stock-margin
     * @apiName AddStockMargin
     * @apiGroup AddStockMargin
     *
     *
     * @apiSuccess {Object} AddStockMargin.
     */
    async addStockMargin(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!add Stock Margin function start!!!!!');
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const { stock_margin_array } = req.body;

            await models.stock_margins.bulkCreate(
                stock_margin_array.map((item) => ({
                    ...item,
                    margin: parseFloat(item.margin)
                }))
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock Margins created successfully!!!!'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {delete} /v1/auth/admin/stock-margin
     * @apiName deleteStockMargin
     * @apiGroup deleteStockMargin
     *
     *
     * @apiSuccess {Object} deleteStockMargin.
     */
    async deleteStockMargin(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!delete stock margin function start!!!!!');
        try {
            // const role = req[`role`];
            // const id = req[`id`];

            // remove all entries from table
            await models.stock_margins.destroy({ truncate: true });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock margins deleted successfully!!!!!'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/stock-margin
     * @apiName getStockMargin
     * @apiGroup getStockMargin
     *
     *
     * @apiSuccess {Object} getStockMargin.
     */
    async getStockMargin(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!get stock margin function start!!!!!');
        try {
            const { shape, clarity, color, weight } = req.body;

            // remove all entries from table
            const stockMargin = await models.stock_margins.findOne({
                where: {
                    [Op.and]: [
                        { shape: { [Op.iLike]: shape } },
                        { clarity: { [Op.iLike]: clarity } },

                        // Case-Insensitive Color Matching
                        Sequelize.literal(`LOWER('${color}') = ANY(string_to_array(LOWER(color), ','))`),

                        // Weight Comparison for a single value
                        Sequelize.literal(
                            `CAST((regexp_split_to_array(weight, '-') )[1] AS NUMERIC) <= ${weight} AND CAST((regexp_split_to_array(weight, '-') )[2] AS NUMERIC) >= ${weight}`
                        )
                    ]
                }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock margins listed successfully!!!!!',
                data: stockMargin
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/update-stock-margin
     * @apiName updateStockMargin
     * @apiGroup updateStockMargin
     *
     *
     * @apiSuccess {Object} updateStockMargin.
     */
    async updateStockMargin() {
        logger.info('!!!!!!update stock margin function start!!!!!');
        const batchSize = 1000; // Process stocks in batches
        let offset = 0;
        let hasMoreStocks = true;
        
        try {
            // Fetch vendors and stock margins once to avoid repeated DB calls
            const vendors = await models.vendors.findAll({
                attributes: ['id', 'margin_percentage']
            });
    
            const stockMargins: any = await models.stock_margins.findAll();

            const allStockOffers = await models.stock_offers.findAll({
                where: {
                    status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                },
                attributes: ['stock_id', 'status']
            })

            const offerStockIds = allStockOffers.map((stockOffer) => stockOffer.stock_id);
            logger.info(`Offer Stock Ids: ${offerStockIds.length}`);

            while (hasMoreStocks) {
                /// Fetch batch of stocks
                const stocks = await models.stocks.findAll({
                    where: { vendor_id: { [Op.not]: null }, status: stockStatus.available, stock_margin: 0 },
                    limit: batchSize,
                    offset
                });
    
                if (stocks.length === 0) {
                    hasMoreStocks = false; // Stop if no more stocks
                    break;
                }
    
                logger.info(`Processing batch with ${stocks.length} stocks, Offset: ${offset}`);

                const transaction = await sequelize.transaction();

                try {
                    for (const stock of stocks) {
                        const vendor = vendors.find(vendorItem => vendorItem.id === stock.vendor_id);
                        const isOfferCreated = offerStockIds.includes(stock.id);
                        if (vendor && !isOfferCreated) {
                            /// Find stock margin for this stock
                            const stockMargin = stockMargins.find((marginItem: any) => {
                                const [minWeight, maxWeight] = marginItem.weight.split('-').map(Number);
            
                                const isShapeMatch = String(marginItem.shape).toLowerCase() === String(stock.shape).toLowerCase();
                                const isColorMatch = String(marginItem.color).toLowerCase().split(',').includes(String(stock.color).toLowerCase());
                                const isClarityMatch = String(marginItem.clarity).toLowerCase() === String(stock.clarity).toLowerCase();
                                const isWeightMatch = stock.weight >= minWeight && stock.weight <= maxWeight;
            
                                return isShapeMatch && isColorMatch && isClarityMatch && isWeightMatch;
                            })?.margin ?? vendor.margin_percentage ?? 0;
            
                            if (stockMargin !== undefined) {
                                /// Batch updates using bulk update instead of multiple individual queries
                                const offsetPricePerCaretOrigin = stock?.price_per_caret_ori;
                                const finalDiscountOrigin = stock?.discounts_ori;
                                const finalPriceOrigin = stock?.final_price_ori;
                                const priceNetService = PriceNetService.getInstance();
                                const pushArray = roundAlias.round;
                                const pushInArray = pushArray.map((data) => data.toLowerCase());
                                const isRoundShape = pushInArray.includes(stock?.shape.toLowerCase());
                                let finalUpdatedStockItem: StockItem = {
                                    stockId: stock?.stock_id,
                                    weight: stock?.weight,
                                    color: stock?.color,
                                    clarity: stock?.clarity,
                                    shape: stock?.shape,
                                    pricePerCaret: offsetPricePerCaretOrigin,
                                    final_price: finalPriceOrigin,
                                    discount: finalDiscountOrigin
                                };
                                if (isRoundShape) {
                                    finalUpdatedStockItem = await priceNetService.getPriceNetJsonForRoundWithInsetData({
                                        stockId: stock?.stock_id,
                                        weight: stock?.weight,
                                        color: stock?.color,
                                        clarity: stock?.clarity,
                                        shape: stock?.shape,
                                        pricePerCaret: offsetPricePerCaretOrigin,
                                        final_price: finalPriceOrigin,
                                        discount: finalDiscountOrigin
                                    });
                                } else {
                                    finalUpdatedStockItem = await priceNetService.getPriceNetJsonForPearWithInsetData({
                                        stockId: stock?.stock_id,
                                        weight: stock?.weight,
                                        color: stock?.color,
                                        clarity: stock?.clarity,
                                        shape: stock?.shape,
                                        pricePerCaret: offsetPricePerCaretOrigin,
                                        final_price: finalPriceOrigin,
                                        discount: finalDiscountOrigin
                                    });
                                }
                                const priceReference =
                                    parseFloat(String(finalUpdatedStockItem.ref_per_caret) || '0') &&
                                    parseFloat(String(finalUpdatedStockItem.ref_per_caret) || '0') > 0
                                        ? parseFloat(String(finalUpdatedStockItem.ref_per_caret) || '0')
                                        : offsetPricePerCaretOrigin / (1 - (finalDiscountOrigin * -1) / 100);
                                logger.info(`Price Reference: ${priceReference}, Discount: ${finalDiscountOrigin}, Offset Price Per Caret: ${offsetPricePerCaretOrigin}, Final Price: ${finalPriceOrigin}`);

                                let offsetPricePerCaret = offsetPricePerCaretOrigin;
                                if (stockMargin) {
                                    offsetPricePerCaret = offsetPricePerCaret + (offsetPricePerCaret * stockMargin) / 100;
                                }
                                const finalPrice = getStockFields.getFinalPrice(stock.weight, offsetPricePerCaret);
                                const updatedFinalDiscount = (offsetPricePerCaret / priceReference) * 100 - 100;

                                logger.info(`Stock ID: ${stock.id}, Stock Margin: ${stockMargin}, Offset Price Per Caret: ${offsetPricePerCaret}, Final Price: ${finalPrice}, Updated Final Discount: ${updatedFinalDiscount}`);

                                await models.stocks.update(
                                    { 
                                        stock_margin: stockMargin,
                                        price_per_caret: offsetPricePerCaret,
                                        final_price: finalPrice,
                                        discount: updatedFinalDiscount
                                    },
                                    { where: { id: stock.id }, transaction }
                                );
                            }
                        }
                    }
        
                    await transaction.commit();
                    offset += batchSize; // Move to the next batch
                } catch (error: any) {
                    await transaction.rollback();
                    logger.error(error);
                }
            }
    
            logger.info('Stock margins updated successfully');
        } catch (error: any) {
            logger.error(error);
        }
    }    
}

export default new StockMargin();
