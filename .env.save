NODE_ENV=development

PORT=6756

### Sendgrid Mail
MAIL_TRANSPORT_METHOD=SMTP
MAIL_HOST=smtp.sendgrid.net
MAIL_SECURE_CONNECTION=true
MAIL_FROM_NAME=Flynix
MAIL_USERNAME=apikey
MAIL_PASSWORD=*********************************************************************
MAIL_PORT=465
MAIL_SENDER=<EMAIL>

### Database - MySql
# DATABASE_NAME=xazina
# DATABASE_PASS=DHARMA1test
# DATABASE_USER=postgres
# DATABASE_HOST=localhost
# DATABASE_PORT = 5432
DATABASE_NAME=diamond_company
DATABASE_PASS=DHARMA1test
DATABASE_USER=postgres
DATABASE_HOST=*************
DATABASE_PORT=5433
DATABASE_ENV=none

### Firebase push notification
SERVERKRY=AAAACVB33yA:APA91bF9f0hiaPMFUz5X5Bk1ctu8lrsU9GnCAGOUFU5Rml191MHJDtMkJks8fHmk2uKlnC-fKD7SqLMGarkPop0szOLNLPArIcY1MIqvSwRcmI51De1_0HTA6lYSg6F3rkT9-z0tH_O-
COLLPASE_KEY=org.sixtech.app

### Redis For Queue
REDIS_PORT=6379
REDIS_URL=*************
REDIS_PASS=va1EmxsNF0iQtL8hy+LOcqQQnn2/iSaa34JqJauKcUU/foKeAdaahzxJQ1qG3W+QZDAlUZqNWs7+03Fk/eQ192

### For Encryption
ENCRYPTION_KEY=62660f804bfa5c6e9a823b95531b7f33
SECRET_KEY=[ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 ]
SECRET_IV=[ 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36 ]

### Common Link
MEDIA_LINK=http://localhost:3001/
SERVER_URL=http://localhost:3001/
AWS_URL=https://sixprod.s3.ap-south-1.amazonaws.com/
AWS_ID=AWS_ID
AWS_SECRET=AWS_SECRET
BUCKETNAME=BUCKETNAME
USESERVER=true
MODE=development

### For Encryption
PUBLIC_KEY = MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC65J6uDUcoNQV33Q11qs+F2oZ5N7O73HjD0ouSlnDqJHzt4K/74Gd/qBljK1OxOi5EmBl1AiuYFwEzK8ail801Job1kFj3T9AQLKh6wySy+8LkvUkI6oiALwPdSi53IMdJu4EHxDLFH5ub722EVs/VdUCy55WQP3wCCDyei9EH4wIDAQAB
PRIVATE_KEY = MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALrknq4NRyg1BXfdDXWqz4Xahnk3s7vceMPSi5KWcOokfO3gr/vgZ3+oGWMrU7E6LkSYGXUCK5gXATMrxqKXzTUmhvWQWPdP0BAsqHrDJLL7wuS9SQjqiIAvA91KLncgx0m7gQfEMsUfm5vvbYRWz9V1QLLnlZA/fAIIPJ6L0QfjAgMBAAECgYBmQcSYkudGFoyhYq+EnCrjoAdHt7R9q0ngSwjW8b1iPwij1vYzKDs4267YVdJ6+8xo95emL8POeXfMmb0bJVe/N1x1OjS1BrRBpvAXjK0tKpb+O7vh1ElK7SafW/O1UpI0AID4g1qx/0kE2cAmKS9GA7N44nsKRkGSWfxLXSoZIQJBAOary7io6UKJcHdJZy9PcBCJNjXfOQFhMWeg3g1X/EjhOCjW+fM4Shp3HkIR3E7P7ZDZqWMSmDrcB+oqSYsStOkCQQDPajdJxhPwVqCoGBvPim296xnertZ2obHawGr6vBzRMPD0pqX+pZlnq+hcZ+TKjH+CU6vZyJmSDhMUW4rze4brAkEArtS265badNvGqiKwWmNTBLV6qQovIqSP1YNPyb1OMc7ByfJmL7oSMEzoWhs/Z1yhKpBOmhCV4Ma0GP9EDmx3QQJBAKKoVnFpgAtztAv+1Bh/I67Tp3cFv7RK+9JkIcE+Mo8vBVenNPgtX894dgG5jCf35KsM+PESqrRqizXk0GQ/lD8CQFr7bIoMSMt2CQM2nqwuWU8xJVF5pUYHGB6aZXrAFHT7W/slzgwzvLFPrdzSLgp1c4pHmT+clWrylAoU4F4p3uU=

### For Sentry Log
#SENTRY_DSN="https://<EMAIL>/6046721"
#DISCORD_DSN="https://discord.com/api/webhooks/905412486839304212/uDjXaeJJWDfstlpA9GTIb0VlOmTozSjD2Os8qEZI_HALflzOEASYU-k_rcrPDhBVtu-p"

BASE_URL=https://diamond-company.api.dharmatech.in

LOGIN_ID=8Xe8R7MaN
TRANSACTION_KEY=2rD7DX3b2b8KJ6sK

ADD_STOCK_KEY=f9Zx3LwQ7N
CREATE_FTP_URL=https://diamond-company.api.dharmatech.in/api/v1/vendor/create-ftp-credentials
UPDATE_FTP_URL=https://diamond-company.api.dharmatech.in/api/v1/vendor/change-ftp-password
























