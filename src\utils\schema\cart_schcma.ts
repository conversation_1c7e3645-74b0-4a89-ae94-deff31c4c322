import Joi from 'joi';

export const addToCartSchema = Joi.object({
    type: Joi.string().valid('diamond', 'jewellery', 'melee').required(),
    diamond_id: Joi.alternatives().conditional('type', {
        is: 'diamond',
        then: Joi.string().uuid().required(),
        otherwise: Joi.string().uuid().optional()
    }),
    jewellery_id: Joi.alternatives().conditional('type', {
        is: 'jewellery',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
    melee_id: Joi.alternatives().conditional('type', {
        is: 'melee',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
    data: Joi.object().optional(),
});


export const removeCartSchema = Joi.object({
    type: Joi.string().valid('diamond', 'jewellery', 'melee', 'all').required(),
    diamond_id: Joi.alternatives().conditional('type', {
        is: 'diamond',
        then: Joi.string().uuid().required(),
        otherwise: Joi.string().uuid().optional()
    }),
    jewellery_id: Joi.alternatives().conditional('type', {
        is: 'jewellery',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
    melee_id: Joi.alternatives().conditional('type', {
        is: 'melee',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
});

export const updateCartSchema = Joi.object({
    type: Joi.string().valid('diamond', 'jewellery', 'melee').required(),
    diamond_id: Joi.alternatives().conditional('type', {
        is: 'diamond',
        then: Joi.string().uuid().required(),
        otherwise: Joi.string().uuid().optional()
    }),
    jewellery_id: Joi.alternatives().conditional('type', {
        is: 'jewellery',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
    melee_id: Joi.alternatives().conditional('type', {
        is: 'melee',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
    quantity: Joi.alternatives().conditional('type', {
        is: 'jewellery',
        then: Joi.number().integer().min(1).required(),
        otherwise: Joi.number().integer().min(1).optional()
    }),
    notes: Joi.alternatives().conditional('type', {
        is: 'melee',
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    }),
});

/// remove all cart items
export const removeAllCartSchema = Joi.object({
    type: Joi.string().valid('diamond', 'jewellery', 'melee', 'all').required(),
});