import getStockFields from './getStockFields';
import { logger } from '../../../utils/logger';
import { statusNames } from '../../../utils/constants';
import { PriceNetService, roundAlias, StockItem } from './pricenet';
import models from '../../../models';
import { Op, Sequelize } from 'sequelize';

export const getStockModalFields = async (item, offset, clientName, vendorId, stocksMargins, vendorMargin) => {
    try {
        // if stock Id is there then insert
        const stockId = getStockFields.getStringValue(
            item,
            'Stock Number, stock number, STOCK-ID, stock-id, Lot Name, lot name, ReferenceNum, ReferenceNumber, Stock, Stock Num, Stock_no, StockNo, stockno, StockNum, StockNumber, VendorStockNumber, VenderStockNumber, StockId, stockid, stock_id, Stock #, stock #, STOCK #, BarCode, barcode, PACKET_NO, packet_no, STOCK, stock, stock id, STOCK ID, PACKET NO, packet no, Stock#, stock#, Stock ID, Stone ID, stone id, Stock Id, stockNo, Stone_NO, stockId, stock_num, Stone No, Stone no, Stone_no, stone_no, stone no, Document No, document no, Document_No, document_no, Customer Ref No., customer ref no., NewBarcode, newbarcode, StoneID, stoneID, stoneid, Ref, ref, MFGID'
        );
        logger.info(`stockId ${stockId}`);

        let intensity =
            getStockFields.getObjectValue(
                item,
                'fluor.intesity, Fluor.Intesity, FluorescenceIntensity, fluorescenceintensity, Flr, FlrIntensity, Fluo Intensity, Fluor, Fluor Intensity, Fluorescence, Fluorescence Intensity, FluorIntensity, fluorescence_intensity, floint, Floint, fluo, FloInt, Fls, fls, fluorescence intensity, fluorescence, Fluorescence / Fluorescence Intensity, FLUO, FLS, flur, Flur, Flo., flo. FLO, flo, fluro, FlrIntens, FLOINT, Fluo, fluor_intensity'
            ) &&
                getStockFields.getObjectValue(
                    item,
                    'fluor.intesity, Fluor.Intesity, FluorescenceIntensity, fluorescenceintensity, Flr, FlrIntensity, Fluo Intensity, Fluor, Fluor Intensity, Fluorescence, Fluorescence Intensity, FluorIntensity, fluorescence_intensity, floint, Floint, fluo, FloInt, Fls, fls, fluorescence intensity, fluorescence, Fluorescence / Fluorescence Intensity, FLUO, FLS, flur, Flur, Flo., flo. FLO, flo, fluro, FlrIntens, FLOINT, Fluo, fluor_intensity'
                ).Intesity
                ? getStockFields.getObjectValue(
                    item,
                    'fluor.intesity, Fluor.Intesity, FluorescenceIntensity, fluorescenceintensity, Flr, FlrIntensity, Fluo Intensity, Fluor, Fluor Intensity, Fluorescence, Fluorescence Intensity, FluorIntensity, fluorescence_intensity, floint, Floint, fluo, FloInt, Fls, fls, fluorescence intensity, fluorescence, Fluorescence / Fluorescence Intensity, FLUO, FLS, flur, Flur, Flo., flo. FLO, flo, fluro, FlrIntens, FLOINT, Fluo, fluor_intensity'
                ) &&
                getStockFields.getObjectValue(
                    item,
                    'fluor.intesity, Fluor.Intesity, FluorescenceIntensity, fluorescenceintensity, Flr, FlrIntensity, Fluo Intensity, Fluor, Fluor Intensity, Fluorescence, Fluorescence Intensity, FluorIntensity, fluorescence_intensity, floint, Floint, fluo, FloInt, Fls, fls, fluorescence intensity, fluorescence, Fluorescence / Fluorescence Intensity, FLUO, FLS, flur, Flur, Flo., flo. FLO, flo, fluro, FlrIntens, FLOINT, Fluo, fluor_intensity'
                ).Intesity
                : getStockFields.getStringValue(
                    item,
                    'fluor.intesity, Fluor.Intesity, FluorescenceIntensity, fluorescenceintensity, Flr, FlrIntensity, Fluo Intensity, Fluor, Fluor Intensity, Fluorescence, Fluorescence Intensity, FluorIntensity, fluorescence_intensity, floint, Floint, fluo, FloInt, Fls, fls, fluorescence intensity, fluorescence, Fluorescence / Fluorescence Intensity, FLUO, FLS, flur, Flur, Flo., flo. FLO, flo, fluro, FlrIntens, FLOINT, Fluo, fluor_intensity'
                );
        const typeOfIntensity = typeof intensity;
        // logger.info(`typeOfIntensity ${typeOfIntensity} and ${intensity}`);

        if (typeOfIntensity === 'string') {
            try {
                const intensityValue = JSON.parse(intensity);
                intensity =
                    typeof intensityValue === 'object'
                        ? intensityValue.Intesity || intensityValue.intesity || 'None'
                        : intensityValue;
            } catch (e) {
                // tslint:disable-next-line
            }
        }
        // logger.info(`typeOfIntensity ${intensity}`);

        if (intensity.includes(`{"`)) {
            try {
                const intensityValue = JSON.parse(intensity);
                intensity =
                    typeof intensityValue === 'object'
                        ? intensityValue.Intesity || intensityValue.intesity || 'None'
                        : intensityValue;
            } catch (e) {
                // tslint:disable-next-line
            }
        }
        // logger.info(`typeOfIntensity ${intensity}`);

        if (stockId && stockId !== '') {
            let discount = getStockFields.getDoubleValue(
                item,
                'disc(%), Disc(%), PctRapNetDiscount, Rap netDisc, RapnetDiscount, rapnetdiscount, RapnetDiscountPct, RapnetDiscountPercent, RapnetDiscPct, RapnetDpx, RapnetRapPct, RDisc, RDiscount, RDiscountPct, RDiscountPercent, RDiscPct, RDpx, RRapPct, RapNet Discount Price, discounts, Rapnet  Discount %, Disc %, disc %, Cash Price Discount %, cash price discount %, discount %, Rapnet Discount %, discount, Discount, RapNet Discount %, RapNet  Discount %, rapNet discount %, rapnet discount, rapnet discount %, rapnet  discount %, discount percent, Discount Percent, Discounts, DISCOUNT, Dis %, dis %, discount_percent, SaleDis, PartyDisc'
            );
            const stockStatus = getStockFields.getStringValue(item, 'Status, status, availability, StockStatus');
            let pricePerCaret = getStockFields.getDoubleValue(
                item,
                '$/ct, $/Ct, $/CT, RapnetAskingPrice, AskingPrice, PerCarat, PerCt, Prc, Price, price, PriceCarat, PriceCt, PricePerCarat, pricepercarat, PricePerCt, Px, price_per_caret, P/CARATE, p/carate, RapNet Price, ppc, Price/ct, price/ct, Ppc, PPC, Rapnet Price, rapnet price, price per carat, price per carat / price, price per Carat / Price, Price Per Carat, NET_RATE, net_rate, pricePerCt, usdPerCarat, SaleRate, pricePerCt, price_per_carat, price_per_cara, Dollar_Per_Carat, PCT, pc, price per ct, Price Per Ct, PRICE PER CT, Price/Carat, price/carat, PartyRate'
            );
            const weight = getStockFields.getDoubleValue(
                item,
                'Carat, CaratSize, CaratWeight, Ct, CtSize, CtWeight, Weight, Sz, weight, carat, weight / carat weight, carat weight, Weight / Carat Weight, CARAT, Carats, carats, SIZE, size, CTS, cts, Cts, crt, Crt, Size'
            );
            const shape = getStockFields.getStringValue(item, 'SHAPE, Shape, Shp, shape');
            const color = getStockFields.getStringValue(
                item,
                'Color, Colr, Colour, colour, color, COLOR, col, Col, colors'
            );
            const clarity = getStockFields.getStringValue(
                item,
                'Clar, Clarity, Clearity, Purity, clarity, PURUTY, puruty, PURITY, CLARITY, cla'
            );

            const finalPriceFromFile = getStockFields.getDoubleValue(
                item,
                'SaleAmt, NetAmount, netamount, final price, Final Price, Amount, amount, AMOUNT, total price, Total Price, Total Amt., total amt., NET_VALUE, net_value, Total Price, TotalPrice, totalprice, TOTAL PRICE, FInalPrice, finalprice, Value, FINAL PRICE, Amt, amt, $Amount, $amount, $ TOTAL, $ total, total_price, total_sales_price, Net Value, net value, Diamond_Value, NET, net, Total $, total $, $, PartyValue'
            )
                ? getStockFields.getDoubleValue(
                    item,
                    'SaleAmt, NetAmount, netamount, final price, Final Price, Amount, amount, AMOUNT, total price, Total Price, Total Amt., total amt., NET_VALUE, net_value, Total Price, TotalPrice, totalprice, TOTAL PRICE, FInalPrice, finalprice, Value, FINAL PRICE, Amt, amt, $Amount, $amount, $ TOTAL, $ total, total_price, total_sales_price, Net Value, net value, Diamond_Value, NET, net, Total $, total $, $, PartyValue'
                )
                : getStockFields.getFinalPrice(weight, pricePerCaret);

            if (!pricePerCaret && finalPriceFromFile && finalPriceFromFile > 0) {
                logger.info(`!!!!!!!!!finalPriceFromFile!!!!!!!!! ${finalPriceFromFile}`);
                pricePerCaret = finalPriceFromFile / weight;
            }

            /// find stock margin for this stock
            const stockMargin =
                JSON.parse(JSON.stringify(stocksMargins)).find((marginItem: any) => {
                    // const [minWeight, maxWeight] = marginItem.weight.split('-').map(Number);
                    const minWeight = parseFloat(String(marginItem?.weight?.split('-')[0]) || '0');
                    const maxWeight = parseFloat(String(marginItem?.weight?.split('-')[1]) || '0');

                    let stockMarginShape = 'Fancy';

                    /// check if the shape is round or not
                    if (roundAlias.round.some(s => s.toLowerCase() === shape.toLowerCase())) {
                        stockMarginShape = 'Round';
                    }


                    /// match the shape with the stock margin fancy or round
                    const isShapeMatch = String(stockMarginShape).toLowerCase() === String(marginItem?.shape).toLowerCase();

                    /// check if the color is in the margin
                    const isColorMatch = String(marginItem?.color)
                        .toLowerCase()
                        .split(',')
                        .includes(String(color).toLowerCase());

                    /// check if the clarity is in the margin
                    const isClarityMatch = String(marginItem?.clarity).toLowerCase() === String(clarity).toLowerCase();

                    /// check if the weight is in the margin
                    const isWeightMatch = weight >= minWeight && weight <= maxWeight;

                    // Check if the weight is within the range
                    return isShapeMatch && isColorMatch && isClarityMatch && isWeightMatch;
                })?.margin ?? vendorMargin; // Default to vendorMargin if no match found

            const priceNetService = PriceNetService.getInstance();
            const pushArray = roundAlias.round;
            const pushInArray = pushArray.map((data) => data.toLowerCase());
            const isRoundShape = pushInArray.includes(shape.toLowerCase());
            let finalUpdatedStockItem: StockItem = {
                stockId,
                weight,
                color,
                clarity,
                shape,
                pricePerCaret,
                final_price: finalPriceFromFile,
                discount
            };

            if (isRoundShape) {
                finalUpdatedStockItem = await priceNetService.getPriceNetJsonForRoundWithInsetData({
                    stockId,
                    weight,
                    color,
                    clarity,
                    shape,
                    pricePerCaret,
                    final_price: finalPriceFromFile,
                    discount
                });
            } else {
                finalUpdatedStockItem = await priceNetService.getPriceNetJsonForPearWithInsetData({
                    stockId,
                    weight,
                    color,
                    clarity,
                    shape,
                    pricePerCaret,
                    final_price: finalPriceFromFile,
                    discount
                });
            }

            discount = parseFloat(String(finalUpdatedStockItem.discount) || '0');
            pricePerCaret = parseFloat(String(finalUpdatedStockItem.pricePerCaret) || '0');
            const priceReference =
                parseFloat(String(finalUpdatedStockItem.ref_per_caret) || '0') &&
                    parseFloat(String(finalUpdatedStockItem.ref_per_caret) || '0') > 0
                    ? parseFloat(String(finalUpdatedStockItem.ref_per_caret) || '0')
                    : pricePerCaret / (1 - (discount * -1) / 100);
            logger.info(
                `TESTING ${stockId} and ${weight} and ${pricePerCaret} and ${priceReference} and ${discount} and ${offset} and ${stockMargin}`
            );

            // const finalDiscount =
            //     offset && offset > 0 ? parseFloat(String(discount)) - parseFloat(offset) : parseFloat(String(discount));
            let offsetPricePerCaret = pricePerCaret + (pricePerCaret * offset) / 100;
            const offsetPricePerCaretOrigin = pricePerCaret + (pricePerCaret * offset) / 100;
            const finalDiscountOrigin = (offsetPricePerCaret / priceReference) * 100 - 100;
            const finalPriceOrigin = getStockFields.getFinalPrice(weight, offsetPricePerCaret);
            // logger.info(`TESTING ${finalPriceOrigin} and ${offsetPricePerCaretOrigin} and ${finalDiscountOrigin}`);

            if (stockMargin) {
                offsetPricePerCaret = offsetPricePerCaret + (offsetPricePerCaret * stockMargin) / 100;
            }
            const finalPrice = getStockFields.getFinalPrice(weight, offsetPricePerCaret);
            const updatedFinalDiscount = (offsetPricePerCaret / priceReference) * 100 - 100;
            // logger.info(`TESTING ${finalPrice} and ${offsetPricePerCaret} and ${updatedFinalDiscount}`);

            const createStockObject = {
                client_name: clientName,
                stock_id: stockId,
                stock_margin: stockMargin,
                availability: getStockFields.getStringValue(
                    item,
                    'Availability, Avail, Availability, Available, Status, availability, status, StockStatus, avail, STATUS, StnStatus'
                ),
                shape,
                weight,
                color,
                clarity,
                cut: getStockFields.getStringValue(
                    item,
                    'Cut, CutGrade, cut, cut grade, Cut Grade, cut / cut grade, Cut / Cut Grade, CUT'
                ),
                polish: getStockFields.getStringValue(
                    item,
                    'Finish, Pol, Pol., Polish, polish, pol., pol, POLISH, POL'
                ),
                symmetry: getStockFields.getStringValue(
                    item,
                    'Symmetry, Sym, Sym., Symetry, Sym-metry, symmetry, sym., Symm, symm, sym, sy, Sy, SYM, SYMM, SYMMETRY'
                ),
                fluorescence_intensity: intensity,
                fluorescence_color: getStockFields.getStringValue(
                    item,
                    'fluor.color, Fluor.Color, FlrColor, fluorescencecolor, Fluo Color, Fluor Color, FluorColor, FluorescenceColor, fluorescence_color, Fluorescence Color, fluorescence color, fluroColor, FlrColor'
                ),
                measurements: getStockFields.getStringValue(
                    item,
                    'Measurement, Measurements, measurements, Dimensions, Meas, measurement, dimensions, dimensions / measurements / measurement, Dimensions / Measurements / Measurement, MEASUREMENT, MEASUREMENTS'
                ),
                shade: getStockFields.getStringValue(item, 'Shade, shade'),
                milky: getStockFields.getStringValue(item, 'Milky, milky, Cloudy'),
                eye_clean: getStockFields.getStringValue(
                    item,
                    'Eye Clean, EyeClean, eyeclean, eye clean, Eyeclean, EC'
                ),
                lab: getStockFields.getStringValue(
                    item,
                    'Lab, LAB, Cert, Certificate, Laboratory, lab, lab - cert, cert, certI_NAME'
                ),
                certificate_number: getStockFields.getStringValue(
                    item,
                    'certificate no, Certificate No, Cert Num, CertID, CertificateID, CertificateNum, CertificateNumber, CertNo, CertNum, CmPub, certificatenumber, certificate_number, Certificate #, certificate id, report #, report no, Report No, certificate#, Certificate#, certificate #, certificate number / certificate id / certificate #, Certificate Number / Certificate ID / Certificate #, CERTIFICATE #, REPORT_NO, report_no, Report #, Cert. No, cert. no, report #, reportNo, Lab_Report_No, report, Report, report_number, cert_num, igi no., IGI No., Lab_Certificate, certi no, certi no., ReportNo, reportno, certino'
                ),
                location: getStockFields.getStringValue(item, 'location, Location, PresentLocation'),
                // treatment: getStockFields.getStringValue(
                //   item,
                //   "Enhance, Enhancement, Enhancements, Enhance-ments, Treatments, treatments"
                // ),
                treatment:
                    'Lab Grown, Treatment, Enhance, Enhancement, Enhancements, Enhance-ments, Treatments, treatments, treatment, Treatment',
                discounts: parseFloat(String(updatedFinalDiscount)).toFixed(2),
                discounts_ori: finalDiscountOrigin,
                price_per_caret: offsetPricePerCaret,
                price_per_caret_ori: offsetPricePerCaretOrigin,
                /**
                 * Calculation
                 * weight * pricePerCaret
                 * This is the final discounted amount
                 */
                final_price: parseFloat(parseFloat(String(finalPrice)).toFixed(2)),
                // final_price_ori: getStockFields.getDoubleValue(item, 'amount, Amount'),
                final_price_ori: parseFloat(parseFloat(String(finalPriceOrigin)).toFixed(2)),
                depth: getStockFields.getDoubleValue(
                    item,
                    'Depth, Depth Percent, DepthPct, DepthPercent, Dpth, TotalDepth, depth, Depth%, TD, td, Depth %, depth %, depth percent / depth % / depth percentage, Depth Percent / Depth % / Depth Percentage, DEPTH_PER, depth_per, Depth [%], depth [%], depth percent, depthPt, Total_Depth_Per, DEPTH %, depth %, depth_percent'
                ),
                meas_depth: getStockFields.getDoubleValue(
                    item,
                    'Measurements Depth, Height, MeasurementsDepth, MeasHeight, measurementsdepth, measurements_depth, measurements depth, height, DEPTH, depth, measDepth'
                ),
                // pavilion_depth: getStockFields.getDoubleValue(
                //   item,
                //   "Pavil, Pavilion, Pavilion Percent, PavilionDepth, PavilionPct"
                // ),
                table: getStockFields.getDoubleValue(
                    item,
                    'Table, Table Percent, TablePct, TablePercent, Tbl, table, Table%, Table %, table %, table percent / table % / table percentage, Table Percent / Table % / Table Percentage, TABLE, TABLE_PER, table_per, Table [%], table [%], Table %, table %, tablePt, Table_Diameter_Per, TABLE %, Table_Per, table_percent'
                ),
                girdle_thin: getStockFields.getStringValue(
                    item,
                    'Girdle, GirdleMin, GirdleThin, girdlethin, girdle_thin, Girdle Thin, girdle thin, girdle (thinnest), girdle (thinnest) / girdle thin, Girdle (thinnest) / Girdle Thin, girdleThin, GirdleThin_ID'
                ),
                girdle_thick: getStockFields.getStringValue(
                    item,
                    'GirdleMax, GirdleThick, girdlethick, girdle_thick, Girdle Thick, girdle thick, girdle (thickest), girdle (thickest) / girdle thick, Girdle (thickest) / Girdle Thick, GirdleThin_ID'
                ),
                girdle_per: getStockFields.getStringValue(
                    item,
                    'Girdle Percent, GirdlePct, GirdleThicknessInPct, GirdlePercent, girdlepercent, girdle_per, Girdle%, girdle, Girdle %, girdle %, girdle % / girdle percent / girdle percentage, Girdle % / Girdle Percent / Girdle Percentage, girdle%, girdlePt, Girdle_Per'
                ),
                girdle_condition: getStockFields.getStringValue(
                    item,
                    'Girdle Condition, GirdleCondition, girdlecondition, girdle, girdle_condition, girdle condition, Girdle, girdleCondition, GirdleName'
                ),
                culet_size: getStockFields.getStringValue(
                    item,
                    'Culet, CuletSize, Culet_size, CuletGrade, Cullet, CulletGrade, culetsize, culet_size, Culet Size, culet size, culet, CuletSize'
                ),
                culet_condition: getStockFields.getStringValue(
                    item,
                    'CuletCondition, Culet Condition, culetcondition, cul, culet_condition, culet condition, culet condition / culet, Culet, culet, CULET, culetCondition, CuletCon'
                ),
                crown_height: getStockFields.getDoubleValue(
                    item,
                    'Crown, Crown Percent, CrownHeight, Crown Height, CrownPct, crownheight, crown height, crown_height, Crown %, crown %, crown % / crown height, Crown % / Crown Height, CROWN HEIGHT, CR_HEIGHT, cr_height, Cr.Height %, cr.height %, crHt, Crown_Height'
                ),
                crown_angle: getStockFields.getDoubleValue(
                    item,
                    'crown_angle, CrownAngle, Crown Angle, crownangle, crown angle, Crown Anglea, Crown angle, CROWN ANGLE, CR_ANGLE, cr_angle, Cr.Angle, cr.angle, crAg, Crown_Angle'
                ),
                pavilion_depth: getStockFields.getDoubleValue(
                    item,
                    'Pavil, Pavilion, Pavilion Percent, PavilionDepth, Pavilion Depth, PavilionPct, paviliondepth, pavillion depth, pavilion_depth, pavilion depth, pavilion %, pavilion % / pavilion height / pavilion depth, Pavilion % / pavilion Height / Pavilion Depth, PAVILIAN DEPTH, PAV_HEIGHT, pav_height, Pavallion Height, pavallion height, Pav.Depth, pav.depth, pavDp, PavillionHeight'
                ),
                pavilion_angle: getStockFields.getDoubleValue(
                    item,
                    'PavilionAngle, Pavilion Angle, pavilionangle, pavillion angle, pavilion_angle, pavilion angle, Pavilion angle, PAVILIAN ANGLE, PAV_ANGLE, pav_angle, Pavallion Angle, pavallion angle, Pav.Angle, pav.angle, pavAg, PavillionAngle, Pavilion_Angle'
                ),
                inscription: getStockFields.getStringValue(
                    item,
                    'LaserInscription, Laser Inscription, laserinscription, laser_inscription, laserInscription, Laser_Inscription'
                ),
                certificate_comment: getStockFields.getStringValue(
                    item,
                    'Comment, Comments, comments, Remarks, Lab comment, Cert comment, Certificate comment, Laboratory comment, certificate_comment, Cert Comments, Report Comments, report comments, Cert Comment, cert comment, lab remarks, cert comment / report comment, Cert Comment / Report Comment, cert comments, Lab_Report_Comment, comment, cert_comment'
                ),
                certfile: getStockFields.getStringValue(
                    item,
                    'CertFile, Lab - Cert, Cert File, certfile, CertFilename, CertificateFile, CertificateFilename, CertificateImage, CertImage, File, certificate_image, PDF, pdf, LabLink, Certificate_file_url, Certificate Url, CertificateLink, certificatelink, Lab_Certificate_URL, CertPDFURL, certpdfurl, ReportLink, cert_url'
                ),
                keytosymbols: getStockFields.getStringValue(
                    item,
                    'KeyToSymbols, keytosymbols, Key, key_to_symbol, Key To Symbols, Key to symbols, key to symbols, key to symbol, Key To Symbol, keyToSymbol'
                ),
                white_inclusion: getStockFields.getStringValue(
                    item,
                    'White Inclusions, White Inclusion, WhiteInclusions, whiteinclusions, whiteInclusion'
                ),
                black_inclusion: getStockFields.getStringValue(
                    item,
                    'Black Inclusion, BlackInclusion, blackinclusion, black_inclusion, black inclusion, blackInclusion'
                ),
                open_inclusion: getStockFields.getStringValue(
                    item,
                    'Open Inclusion, OpenInclusion, openinclusion, openInclusion'
                ),
                fancy_color: getStockFields.getStringValue(
                    item,
                    'FancyColor, Fancy Color, FancyColorMainBody, FC-Main Body, FCMainBody, fancycolor, fancy_color, fancy color, fancy color main body, fancy color / fancy color main body, Fancy Color / Fancy Color Main Body, fancyColor, FColor, FANCY COLOR'
                ),
                fancy_color_intensity: getStockFields.getStringValue(
                    item,
                    'FancyColorIntensity, FCIntensity, FC-Intensity, fancycolorintensity, fancy_color_intensity, Fancy Color Intensity, fancy color intensity, fancy color intensity / fancy intensity, Fancy Color Intensity / Fancy Intensity, fancyIntensity, FCIntens, FANCY COLOR INTENSITY'
                ),
                fancy_color_overtone: getStockFields.getStringValue(
                    item,
                    'FancyColorOvertone, FancyColorOvertones, FCOvertone, fancycolorovertone, fancy_color_overtone, Fancy Color Overtone, fancy color overtone, fancy color overtone / fancy Overtone, Fancy Color Overtone / Fancy Overtone, fancyOvertone, FCOverton, FANCY OVERTONE'
                ),
                country: getStockFields.getStringValue(
                    item,
                    'Country, CountryLocation, LotCountry, country, location, stone location country, diamond location / stone location country / country, Diamond Location / Stone Location Country / Country, LOCATION, Location, BranchCountry, branchcountry'
                ),
                state: getStockFields.getStringValue(
                    item,
                    'StateLocation, State, state, stone location state prov, stone location state prov / state, Stone Location State Prov / State'
                ),
                city: getStockFields.getStringValue(
                    item,
                    'City, CityLocation, city, stone location city, CITY, Branch, branch'
                ),
                diamond_video: getStockFields.getStringValue(
                    item,
                    'v360, V360, Video Link, VideoLink, videolink, diamond_video, Video, video, Vid, vid, VideoURL 1, videourl 1, videoURL 1, Videourl 1, external url, Diamond Video, diamond video, video link, video link / external url, Video Link / External Url, VIDEO LINK, VIDEO_LINK, video_link, View Video-, view video-, videoUrl, Video_url, video_url, VIDEOLINK, DIAMOND VIDEOS, diamond videos, AmazonVideoLink, amazonvideolink, new video, 360URL',
                    true
                ),
                diamond_image: getStockFields.getStringValue(
                    item,
                    'Photo, photo, Img, img, image 1, Image 1, diamond image, Diamond Image, additional image, image link, image link 1, Image Link 1, additional image / image link, Additional Image / Image Link, DiamondImage, Image, ImageFile, Photo, diamondimage, image, IMAGE LINK, View Image-, view image-, imagelink, ImageLink, imageUrl, Stone_Img_url, DIAMOND IMAGES, diamond images, AWSDiamondImageLink, awsdiamondimagelink, new image, ImageURL, image_url'
                ),
                diamond_image_2: getStockFields.getStringValue(
                    item,
                    'DiamondImage2, diamondimage2,image link 2, Image Link 2'
                ),
                hearts_image: getStockFields.getStringValue(item, 'HeartsImage, heartsimage'),
                arrows_image: getStockFields.getStringValue(item, 'ArrowImage, arrowimage'),
                aset_image: getStockFields.getStringValue(item, 'AsetImage, asetimage'),
                idealscope_image: getStockFields.getStringValue(item, 'IdealscopeImage, idealscopeimage'),
                diamond_type: getStockFields.getStringValue(
                    item,
                    'DiamondType, diamond type, diamondtype, diamond_type, Stone Type, stone type'
                )
                    ? getStockFields.getStringValue(
                        item,
                        'DiamondType, diamond type, diamondtype, diamond_type, Stone Type, stone type'
                    )
                    : 'CVD',
                lab_grown_type: getStockFields.getStringValue(
                    item,
                    'Enhance, Enhancement, Enhancements, Enhance-ments, Treatments, LabGrownType, labgrowntype, diamond type, Treatment'
                )
                    ? getStockFields.getStringValue(
                        item,
                        'Enhance, Enhancement, Enhancements, Enhance-ments, Treatments, LabGrownType, labgrowntype, diamond type, Treatment'
                    )
                    : 'CVD',
                // lab_grown_type: 'CVD',
                growth_type: getStockFields.getStringValue(
                    item,
                    'growth type, growth_type, CVD_TYPE, cvd_type, Growth Type, type, Type, Growth, growth, Sub Type, sub type, Lg Type, lg type, diamondType, CVD_HPHT'
                )
                    ? getStockFields.getStringValue(
                        item,
                        'growth type, growth_type, CVD_TYPE, cvd_type, Growth Type, type, Type, Growth, growth, Sub Type, sub type, Lg Type, lg type, diamondType, CVD_HPHT'
                    )
                    : 'CVD',
                is_lab_grown: true,
                // is_lab_grown: getStockFields.getStringValue(item, 'DiamondType, diamondtype, diamond_type')
                //     ? getStockFields.getStringValue(item, 'DiamondType, diamondtype, diamond_type') === 'lab_grown'
                //         ? true
                //         : false
                //     : false,
                // calculation (Old One) (Can Ignore)
                // weight * pricePerCaret
                ref_per_caret: getStockFields.getDoubleValue(
                    item,
                    'rap price, Rap Price, rap_per_caret, rap price / base price, Rap Price / Base Price, rap, RATE, rate, Price/Carat, price/carat, Rap Rate, rap rate, RAP_Price'
                ),
                // Old One (Can Ignore)
                lw_ratio: getStockFields.getDoubleValue(
                    item,
                    'l/w ratio, L/W Ratio, Ratio, RATIO, LengthByWidthRatio, lengthbywidthratio, ratio'
                ),
                brand: getStockFields.getStringValue(item, 'Brand, brand, origin, Origin'),
                status: statusNames.available.includes(stockStatus.toLocaleUpperCase())
                    ? 'AVAILABLE'
                    : statusNames.memo.includes(stockStatus.toLocaleUpperCase())
                        ? 'ON MEMO'
                        : statusNames.hold.includes(stockStatus.toLocaleUpperCase())
                            ? 'ON HOLD'
                            : statusNames.sold.includes(stockStatus.toLocaleUpperCase())
                                ? 'SOLD'
                                : 'AVAILABLE',
                hearts_and_arrow: getStockFields.getBooleanValueForHeartsAndArrow(
                    item,
                    ' hearts and arrow,Hearts and Arrow,HEARTS AND ARROW,hearts and arrows,Hearts and Arrows, HEARTS AND ARROWS,hearts And Arrow,Hearts And Arrow,HEARTS And ARROW,hearts And Arrows,Hearts And Arrows,HEARTS And ARROWS, HeartsAndArrows, heartsandarrows'
                ),
                is_active: true,
                _deleted: false
            };

            return createStockObject;
        }

        return null;
    } catch (e) {
        throw e;
    }
};
