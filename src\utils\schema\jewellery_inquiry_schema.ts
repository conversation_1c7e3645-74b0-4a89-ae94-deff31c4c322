import Joi from 'joi';

export const createJewelleryInquirySchema = Joi.object({
    product_id: Joi.string().required(),
    variant_id: Joi.string().required(),
    inquiry_id: Joi.string().required(),
    product_details: Joi.object().required()
    // variant_price: Joi.number().required().greater(0),
});

export const updateJewelleryInquirySchema = Joi.object({
    id: Joi.string().required(),
    variant_price: Joi.number().required().greater(0)
});
