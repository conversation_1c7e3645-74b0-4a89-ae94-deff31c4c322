// return type : String
const getStringValue = (dataObject, fields, shouldNotLowerCase = false) => {
    const fieldsArray = fields.split(', ');
    let returnValue = '';

    for (const item of fieldsArray) {
        if (dataObject[item] && dataObject[item] !== '') {
            returnValue = String(dataObject[item]);
            break;
        }
    }

    if (shouldNotLowerCase) {
        return returnValue;
    }

    return returnValue.toLowerCase();
};

// return type : Double
const getDoubleValue = (dataObject, fields) => {
    const fieldsArray = fields.split(', ');
    let returnValue = 0.0;

    for (const item of fieldsArray) {
        const regex = /[+-]?\d+(\.\d+)?/g;
        if (dataObject[item] && dataObject[item] !== '') {
            dataObject[item] = typeof dataObject[item] !== 'string' ? String(dataObject[item]).replace('$', '').replace(",", "").trim() : dataObject[item].replace('$', '').replace(",", "").trim();
            let value = [];
            value =
                dataObject[item].match(regex) &&
                dataObject[item].match(regex).map((v) => {
                    return parseFloat(v);
                });

            if (value && value.length > 0 && value[0] !== '' && parseFloat(value[0]) && !isNaN(parseFloat(value[0]))) {
                returnValue = parseFloat(value[0]);
                break;
            }
        }
    }

    return returnValue;
};

// return type : Object
const getObjectValue = (dataObject, fields) => {
    const fieldsArray = fields.split(', ');
    let returnValue: any = {};

    for (const item of fieldsArray) {
        if (dataObject[item]) {
            returnValue = dataObject[item];
            break;
        }
    }

    return returnValue;
};

const getFinalPrice = (weight, pricePerCaret) => {
    return weight * pricePerCaret;
};

const getRefPerCaretPrice = (weight, pricePerCaret) => {
    return weight * pricePerCaret;
};

const getFieldKeys = (keysArray, fieldName) => {
    const index = keysArray.findIndex((data, i) => {
        return data === fieldName;
    });

    return index;
};

const getBooleanValueForHeartsAndArrow = (dataObject, fields, shouldNotLowerCase = false) => {
    const fieldsArray = fields.split(', ');
    let returnValue = '';

    for (const item of fieldsArray) {
        if (dataObject[item] && dataObject[item] !== '') {
            returnValue = String(dataObject[item]);
            break;
        }
    }

    if (shouldNotLowerCase) {
        return returnValue;
    }

    // Convert value to lowercase string for case-insensitive comparison
    const normalizedValue = returnValue.trim().toLowerCase();

    // Return true if value is 'yes', '1', or 'true'
    if (normalizedValue === 'yes' || normalizedValue === '1' || normalizedValue === 'true') {
        return true;
    }

    return false;
};

export = {
    getStringValue,
    getDoubleValue,
    getFinalPrice,
    getRefPerCaretPrice,
    getFieldKeys,
    getObjectValue,
    getBooleanValueForHeartsAndArrow
};
