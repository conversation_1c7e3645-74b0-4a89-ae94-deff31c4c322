import { Router, IRouter, Request, Response, NextFunction } from 'express';
import melle from '../../../controllers/admin/melles';
import { fieldsValidator } from '../../../middlewares/validator';
import { addMelleSchema } from '../../../utils/schema/melle_schema';

const router: IRouter = Router();

router.get('/melle', melle.getMelleData);

router.post(
    '/melle',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addMelleSchema),
    melle.addMelle
);

router.delete('/melle', melle.deleteMelle);

export default router;
