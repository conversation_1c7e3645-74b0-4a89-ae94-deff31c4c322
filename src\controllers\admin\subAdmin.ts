import bcrypt from 'bcrypt';
import { AdminAttributes } from '../../models/admins';
import models from '../../models';
import express, { Request, Response, NextFunction } from 'express';
import { TOKEN, httpStatusCodes, adminRole } from '../../utils/constants';
import jwt from 'jsonwebtoken';
import { Op } from 'sequelize';
import { logger } from '../../utils/logger';

class SubAdmin {
    /**
     * @api {get} /v1/permissions
     * @apiName subadmin permissions
     * @apiGroup Admin
     *
     * @apiSuccess [array] String
     */

    async getPermissions(req: Request, res: Response, next: NextFunction) {
        const permissions: string[] = ['read', 'write', 'execute'];
        try {
            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Successfully fetched admin permissions',
                data: permissions
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/subAdmin
     *  @apiName subAdmin create
     *  @apiGroup SubAdmin
     *
     *  @apiSuccess {Object} Admin
     */

    async subAdminCreate(req: Request, res: Response, next: NextFunction) {
        try {
            const { first_name, last_name, phone, password, permission } = req.body;

            let email = req.body.email;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            const existingUserEmail = await models.admins.findOne({
                where: {
                    [Op.or]: [email ? { email } : {}, phone ? { phone } : {}]
                }
            });

            if (existingUserEmail) {
                throw new Error(`Admin is already registered`);
            }

            const hashPassword = await bcrypt.hash(password, 12);
            const createSubAdmin = await models.admins.create({
                first_name,
                last_name,
                permission,
                email,
                phone,
                password: hashPassword,
                role: adminRole.subAdmin
            });
            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Successfully Registered',
                data: {
                    first_name,
                    last_name,
                    email,
                    phone,
                    role: createSubAdmin.role,
                    permission
                }
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/auth/admin/subAdmin
     *  @apiName subAdmin
     *  @apiGroup SubAdmin
     *
     *  @apiSuccess {Object} Success Object
     */
    async updateSubAdmin(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, first_name, last_name, permission } = req.body;

            let email = req.body.email;

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            const subAdminExists = await models.admins.findOne({
                where: { id, _deleted: false }
            });

            if (!subAdminExists) {
                throw new Error('Sub-admin not found');
            }

            const dataObject: any = {};

            if (first_name) {
                dataObject.first_name = first_name;
            }
            if (last_name) {
                dataObject.last_name = last_name;
            }
            if (email) {
                dataObject.email = email;
            }
            if (permission) {
                dataObject.permission = permission;
            }

            await models.admins.update(dataObject, {
                where: { id }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Sub-admin Updated successfully!'
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
    /**
     *  @api {delete} /v1/auth/admin/subAdmin
     *  @apiName admin Register
     *  @apiGroup Admin
     *
     *  @apiSuccess {Object} Admin
     */
    async deleteSubAdmin(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;

            const existingSubAdmin = await models.admins.findOne({
                where: {
                    id,
                    _deleted: false
                }
            });

            if (!existingSubAdmin) {
                throw new Error('Sub-admin not found');
            }

            await models.admins.update(
                {
                    _deleted: true
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Sub-admin Deleted Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/subAdmin/updateStatus
     *  @apiName subAdmin Active/InActive Status
     *  @apiGroup subAdmin
     *
     *  @apiSuccess {Object} SuccessResponse
     */
    async activeInActiveSubAdmin(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, isActive } = req.body;
            // const id = req.query.id;
            // const isActive = req.query.isActive;

            const existingSubAdmin = await models.admins.findOne({
                where: {
                    id,
                    _deleted: false
                }
            });

            if (!existingSubAdmin) {
                throw new Error('Sub-admin not found');
            }

            await models.admins.update(
                {
                    isActive
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Status changed successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/admin/subAdmin
     *  @apiName Get SubAdmins
     *  @apiGroup subAdmin
     *
     *  @apiSuccess {Object} array of subAdmin
     */
    async getAllSubAdmin(req: Request, res: Response, next: NextFunction) {
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;

            const q = req.query.q;
            const keyword = `%${q}%`;
            let whereClause: any = {};

            if (q) {
                whereClause = {
                    [Op.or]: [
                        { first_name: { [Op.iLike]: keyword } },
                        { last_name: { [Op.iLike]: keyword } },
                        { email: { [Op.iLike]: keyword } }
                    ],
                    _deleted: false,
                    role: adminRole.subAdmin
                };
            } else {
                whereClause = {
                    _deleted: false,
                    role: adminRole.subAdmin
                };
            }

            const admins: any = await models.admins.findAndCountAll({
                where: whereClause,
                offset: skip,
                limit,
                order: [['createdAt', 'DESC']],
                attributes: { exclude: ['password', 'otp'] }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Successfully listed',
                data: admins.rows,
                count: admins.count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new SubAdmin();
