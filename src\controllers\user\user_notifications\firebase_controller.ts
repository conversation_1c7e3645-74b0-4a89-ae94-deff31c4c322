import admin from 'firebase-admin';
import { logger } from '../../../utils/logger';

class FirebaseController {
    private static instance: FirebaseController;

    /**
     * Implement Singleton Approach
     */

    static get(): FirebaseController {
        if (!FirebaseController.instance) {
            FirebaseController.instance = new FirebaseController();
        }
        return FirebaseController.instance;
    }

    adminApp() {
        let app: admin.app.App | null;
        if (admin.apps.length === 0) {
            app = admin.initializeApp({
                credential: admin.credential.cert('./diamond-company-firebase.json')
                // credential: admin.credential.cert(firebase_credentials)
            });
        } else {
            app = admin.apps[0];
        }

        return app;
    }

    async sendToDevice(token: any, payload: any) {
        try {
            if (typeof token !== 'string') {
                if (token && token.length) {
                    const notifications: any = [];
                    for (const subToken of token) {
                        notifications.push({
                            token: subToken,
                            notification: { title: payload.notification.title, body: payload.notification.message },
                            data: {
                                payload: JSON.stringify(payload)
                            }
                        });
                    }
                    await new FirebaseController().adminApp()?.messaging().sendEach(notifications);
                }
            } else {
                await new FirebaseController()
                    .adminApp()
                    ?.messaging()
                    .send({
                        token,
                        notification: { title: payload.notification.title, body: payload.notification.message },
                        data: {
                            payload: JSON.stringify(payload)
                        }
                    });
            }
            logger.info('sendToDevice:- success');
        } catch (e: any) {
            logger.error('Error:- sendToDevice:-', e);
            // throw new Error(e);
        }
    }
}

const awsController = FirebaseController.get();

export { awsController as FirebaseController };
