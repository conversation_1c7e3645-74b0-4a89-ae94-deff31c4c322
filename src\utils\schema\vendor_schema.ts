import Joi from 'joi';
import { isPhoneNumber } from '../../utils/validators';

export const createVendorSchema = Joi.object({
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    profile_image: Joi.string().allow(null),
    password: Joi.string().min(4).required(),
    order_count: Joi.number().integer().allow(null),
    vendor_type: Joi.string().valid('API', 'UPLOAD', 'FTP').required(),
    api_url: Joi.string().allow(null).optional(),
    billing_details: Joi.object().allow(null).default({}),
    company_name: Joi.string().required(),
    document_name: Joi.string().optional(),
    margin_percentage: Joi.number().precision(2).min(0).max(100).optional()
});

export const updateVendorSchema = Joi.object({
    id: Joi.string().uuid().required(),
    first_name: Joi.string().optional(),
    last_name: Joi.string().allow(null).optional(),
    email: Joi.string().email().allow(null).optional(),
    phone: Joi.string()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    profile_image: Joi.string().allow(null).optional(),
    vendor_type: Joi.string().valid('API', 'UPLOAD', 'FTP').optional(),
    api_url: Joi.string().allow(null).optional(),
    billing_details: Joi.object({
        account_holder_name: Joi.string().min(1).max(100).required(),
        account_number: Joi.string().required(),
        bank_name: Joi.string().min(1).max(100).required(),
        ifsc: Joi.string().required()
    })
        .allow(null)
        .default({})
        .optional(),
    address: Joi.object({
        street: Joi.string().required(),
        city: Joi.string().required(),
        state: Joi.string().required(),
        country: Joi.string().required(),
        zip_code: Joi.string().required()
    })
        .allow(null)
        .default({})
        .optional(),
    is_terms_accepted: Joi.boolean().optional(),
    company_name: Joi.string().allow(null).optional(),
    document_name: Joi.string().optional(),
    margin_percentage: Joi.number().precision(2).min(0).max(100).optional()
});

export const changeVendorStatusSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_active: Joi.boolean().required()
});

export const blackListVendorSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_blacklisted: Joi.boolean().required()
});

export const verifyVendorSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_verified: Joi.boolean().required(),
    kyc_reject_reason: Joi.string().optional(),
    margin_percentage: Joi.number().precision(2).min(0).max(100).optional()
});

export const createDRCVendorSchema = Joi.object({
    vendors: Joi.array()
        .items(
            Joi.object({
                email: Joi.string().email().required(),
                company_name: Joi.string().trim().required(),
                first_name: Joi.string().trim().required(),
                last_name: Joi.string().trim().required(),
                phone: Joi.string().trim().required(),
                document_url: Joi.string().trim().required(),
                api_url: Joi.string().trim().required(),
                api_key: Joi.string().trim().required()
            })
        )
        .min(1)
        .required()
});
