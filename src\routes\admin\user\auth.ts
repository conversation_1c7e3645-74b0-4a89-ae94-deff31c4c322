import user from '../../../controllers/admin/user';
import creditHistory from '../../../controllers/user/credit_history';

import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    blockUserSchema,
    changeUserStatusSchema,
    createUserSchema,
    listUsersDetails,
    updateCreditLimitSchema,
    updateUserSchema,
    verifyUserSchema
} from '../../../utils/schema/user_schema';

const router: IRouter = Router();

// add user
router.post(
    '/user',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createUserSchema),
    user.addUser
);

// list users
router.get('/user', user.listUsers);

// get user details
router.get('/user/details', user.userDetails);

// update user
router.put(
    '/user',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateUserSchema),
    user.updateUser
);

// change status
router.put(
    '/user/status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeUserStatusSchema),
    user.changeUserStatus
);

// block user
router.put(
    '/user/block',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, blockUserSchema),
    user.blockUser
);

// delete user
router.delete('/user', user.deleteUser);

// user verify
router.put(
    '/user/verify',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, verifyUserSchema),
    user.verifyUser
);

// update credit limit
router.put(
    '/user/update-credit-limit',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateCreditLimitSchema),
    user.updateCreditLimit
);

// update credit limit
router.post(
    '/user/list-users-details',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, listUsersDetails),
    user.listUsersDetails
);

// user available credit
router.get('/available-credit', creditHistory.userAvailableCredit);

export default router;
