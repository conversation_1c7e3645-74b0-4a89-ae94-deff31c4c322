import { Request, Response, NextFunction } from 'express';
import models, { sequelize } from '../../models';
import {
    adminRole,
    buyRequestStatus,
    buyRequestType,
    calculateFinancialYear,
    getInvoiceNumber,
    httpStatusCodes,
    isStocksAvailable,
    makeUniqueKey,
    NotificationType,
    PaymentMode,
    unifiedOrderType
} from '../../utils/constants';
import { Op } from 'sequelize';
import { logger } from '../../utils/logger';
import authorizePayment from './authorize_payment/authorize_payment';
import userNotification from './user_notifications/user_notification';
import { OrderAttributes } from '../../models/orders';

class UnifiedOrder {
    /**
     * @api {post} /v1/auth/unified-order
     * @apiName createBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async createUnifiedOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling function create unified order!!!!!');
        try {
            const role = req[`role`];
            const userData: any = req[`user`];

            const { diamonds, jewellery, melee } = req.body;

            /// create buy req by user ony
            if (role !== adminRole.user) {
                throw new Error('Unauthorized access');
            }

            /// check user is blocked
            if (userData.is_blocked) {
                throw new Error('Please contact admin. User is blocked!!!');
            }

            const successArray: any = [];
            const errorArray: any = [];

            /// create buy request for diamonds
            if (diamonds && diamonds?.stock_ids?.length) {
                try {
                    const successResponse = await new UnifiedOrder().createBuyRequestOnly(req);
                    logger.info('Create buy request for diamond success');
                    successArray.push({
                        type: unifiedOrderType.DIAMONDS,
                        data: successResponse
                    });
                } catch (error: any) {
                    logger.error(`create buy request error ${error}`);
                    errorArray.push({
                        type: unifiedOrderType.DIAMONDS,
                        error:
                            typeof error?.message === 'string'
                                ? error?.message || 'something went wrong'
                                : 'something went wrong'
                    });
                }
            }

            /// create order for melee
            if (melee && melee?.melee_array?.length) {
                try {
                    const successResponse = await new UnifiedOrder().createOrderForMeleeOnly(req);
                    logger.info('Create order for melle success');
                    successArray.push({
                        type: unifiedOrderType.MELEE,
                        data: successResponse
                    });
                } catch (error: any) {
                    logger.error(`create melee error ${error}`);
                    errorArray.push({
                        type: unifiedOrderType.MELEE,
                        error:
                            typeof error?.message === 'string'
                                ? error?.message || 'something went wrong'
                                : 'something went wrong'
                    });
                }
            }

            /// create order for jewellery
            if (jewellery && jewellery?.jewellery_array?.length) {
                try {
                    const successResponse = await new UnifiedOrder().createOrderForJewelleryOnly(req);
                    logger.info('Create order for jewellery success');
                    successArray.push({
                        type: unifiedOrderType.JEWELLERY,
                        data: successResponse
                    });
                } catch (error: any) {
                    logger.error(`create jewellery error ${error}`);
                    errorArray.push({
                        type: unifiedOrderType.JEWELLERY,
                        error:
                            typeof error?.message === 'string'
                                ? error?.message || 'something went wrong'
                                : 'something went wrong'
                    });
                }
            }

            logger.info(
                `Unified order response ${JSON.stringify(
                    {
                        success_data: successArray,
                        error_data: errorArray
                    },
                    null,
                    2
                )}`
            );

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Unified order created successfully`,
                data: {
                    success_data: successArray,
                    error_data: errorArray
                }
            });

            // if (updatedBuyRequest?.stock_ids?.length) {
            //     /// send notification for stock status change
            //     try {
            //         userNotification.stockStatusChangeNotification(
            //             updatedBuyRequest.stock_ids.map((item: any) => item.stock_id)
            //         );
            //     } catch (error: any) {
            //         logger.error(`!!!error sending notification for stock status!!!!! ${error}`);
            //     }
            // }

            // /// notify admin/vendor
            // try {
            //     userNotification.buyRequestCreatedNotificationToVendorAndAdmin(
            //         NotificationType.buyRequestCreated,
            //         updatedBuyRequest.id
            //     );
            // } catch (error: any) {
            //     logger.error(`!!!error sending notification to admin and user!!!!! ${error}`);
            // }

            ///
        } catch (error: any) {
            /// await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /// create buy request only
    createBuyRequestOnly = async (req: Request) => {
        logger.info('Calling function create buy request only!!!!!');
        const transaction = await sequelize.transaction();
        try {
            const userId = req[`id`];
            const userData: any = req[`user`];

            const {
                card_number,
                card_holder_name,
                exp_date,
                cvv,
                payment_mode,
                diamonds,
                shipment_id,
                shipment_price,
                apple_pay_response
            } = req.body;

            const taxPrice: number = userData?.is_verified
                ? 0
                : parseFloat(diamonds.total_amount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

            const grandTotal = parseFloat(diamonds.total_amount) + parseFloat(shipment_price) + taxPrice;

            if (payment_mode === PaymentMode.creditLimit) {
                logger.info(`Calculating available credit`);
                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: userId
                    }
                });

                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: userId
                    }
                });

                const availableCreditLimit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                if (parseFloat(grandTotal.toFixed(2)) > availableCreditLimit) {
                    throw new Error('Not enough credit');
                }
            }

            const availableStocks = await models.stocks.findAll({
                where: {
                    id: { [Op.in]: diamonds.stock_ids.map((stockIds: any) => stockIds.stock_id) },
                    status: 'AVAILABLE'
                }
            });

            if (availableStocks.length !== diamonds.stock_ids.length) {
                throw new Error('Please refresh stock list');
            }

            // extract vendors ids if includes vendor stocks
            const vendor_ids = [
                ...new Set(
                    diamonds.stock_ids
                        .map((item: any) => item.vendor_id)
                        .filter((vendorId: any) => vendorId !== null && vendorId !== undefined)
                )
            ];

            const vendorsList = await models.vendors.findAll({
                where: {
                    [Op.and]: [
                        { id: { [Op.in]: vendor_ids } },
                        {
                            [Op.or]: [
                                { is_blacklisted: true },
                                { is_active: false }
                            ]
                        }
                    ]
                }
            });

            /// check vendors blacklisted
            if (vendorsList && vendorsList.length) {
                throw new Error('Please refresh your stock list');
            }

            /// generate order code
            const orderCode = makeUniqueKey(10);

            logger.info('Creating buy requests!!!!');
            /// create buy request with PENDING status
            /// is_available true
            const buyRequest = await models.buy_requests.create(
                {
                    ...req.body,
                    user_id: userId,
                    stock_ids: diamonds.stock_ids.map((item: any) => ({
                        ...item,
                        is_available: true,
                        is_action_taken: item.vendor_id ? false : true,
                        is_margin_approved: item.vendor_id ? false : true,
                    })),
                    vendor_ids,
                    is_margin_approved: !vendor_ids?.length,
                    status: 'PENDING',
                    updated_by_id: userId,
                    order_code: orderCode,
                    amount: diamonds.total_amount,
                    shipment_id,
                    shipment_price,
                    tax_price: parseFloat(taxPrice.toFixed(2)),
                    grand_total: grandTotal
                },
                { transaction }
            );

            if (!buyRequest) {
                throw new Error(`Something went wrong!!`);
            }

            logger.info('Initiating payment for diamonds!!');
            ////////////////// MAKE PAYMENT //////////////////////////
            if (payment_mode === PaymentMode.creditCard) {
                /// check for customer profile id exists
                if (!req[`user`].customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }

                /// authorize credit card
                await authorizePayment.authorizeCreditCard(
                    false,
                    card_number,
                    card_holder_name,
                    exp_date,
                    cvv,
                    userId,
                    buyRequest,
                    transaction,
                    buyRequestStatus.pending
                );
            }
            /// apple pay
            else if (payment_mode === PaymentMode.applePay) {
                /// check for customer profile id exists
                if (!req[`user`].customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }
                /// ApplePay
                await authorizePayment.createApplePayTransaction(
                    buyRequest.id,
                    apple_pay_response,
                    userId,
                    transaction
                );
            }
            /// credit limit
            else if (payment_mode === PaymentMode.creditLimit) {
                logger.info('Debiting credit limit for diamonds!!!!');
                /// debit credit limit
                await models.credit_histories.create(
                    {
                        user_id: buyRequest.user_id,
                        credit: parseFloat(parseFloat(buyRequest.grand_total).toFixed(2)),
                        type: 'CREATE-BUY-REQUEST',
                        transaction_type: 'DEBIT',
                        buy_request_id: buyRequest.id
                    },
                    { transaction }
                );
            }

            const updatedBuyRequest: any = await models.buy_requests.findOne({
                where: { id: buyRequest.id },
                transaction
            });

            logger.info('Creating buy request trail');
            /// add to trail
            await models.buy_request_trails.create(
                {
                    user_id: userId,
                    buy_request_id: updatedBuyRequest.id,
                    status: updatedBuyRequest.status,
                    updated_by_id: userId,
                    payload: JSON.stringify(updatedBuyRequest)
                },
                { transaction }
            );

            logger.info('Putting stocks on HOLD');
            /// change stock status to HOLD
            await models.stocks.update(
                { status: 'HOLD' },
                { where: { id: { [Op.in]: diamonds.stock_ids.map((item: any) => item.stock_id) } }, transaction }
            );

            /// update user buy_request_count
            await models.users.update(
                { buy_request_count: sequelize.literal('buy_request_count + 1') },
                { where: { id: userId }, transaction }
            );

            /// update vendor buy_request_count
            if (vendor_ids) {
                await models.vendors.update(
                    { buy_request_count: sequelize.literal('buy_request_count + 1') },
                    { where: { id: { [Op.in]: vendor_ids } }, transaction }
                );
            }

            await transaction.commit();

            // res.send({
            //     status: httpStatusCodes.SUCCESS_CODE,
            //     message: `Buy request created successfully`,
            //     data: buyRequest
            // });

            if (updatedBuyRequest?.stock_ids?.length) {
                /// send notification for stock status change
                try {
                    userNotification.stockStatusChangeNotification(
                        updatedBuyRequest.stock_ids.map((item: any) => item.stock_id)
                    );
                } catch (error: any) {
                    logger.error(`!!!error sending notification for stock status!!!!! ${error}`);
                }
            }

            /// notify admin/vendor
            try {
                userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                    NotificationType.buyRequestCreated,
                    updatedBuyRequest.id
                );
            } catch (error: any) {
                logger.error(`!!!error sending notification buy request created!!!!! ${error}`);
            }

            logger.info('Returning buy request response for unified order');
            return {
                buy_request: {
                    id: updatedBuyRequest.id,
                    order_code: updatedBuyRequest.order_code,
                    grand_total: updatedBuyRequest.grand_total
                }
            };

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(`create buy request error ${error}`);
            throw error;
        }
    };

    /// create order for jewellery
    createOrderForJewelleryOnly = async (req: Request) => {
        logger.info('Calling function create jewellery only!!!!!');
        const transaction = await sequelize.transaction();

        try {
            const userId = req[`id`];
            const role = req[`role`];
            const userData: any = req[`user`];

            const {
                card_number,
                card_holder_name,
                exp_date,
                cvv,
                payment_mode,
                jewellery,
                total_amount,
                billing_address,
                shipping_address,
                shipment_id,
                shipment_price,
                apple_pay_response
            } = req.body;

            const taxPrice: number = userData?.is_verified
                ? 0
                : parseFloat(jewellery?.total_amount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

            const grandTotal = parseFloat(jewellery?.total_amount) + parseFloat(shipment_price) + taxPrice;

            if (payment_mode === PaymentMode.creditLimit) {
                logger.info(`Calculating available credit`);
                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: userId
                    }
                });

                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: userId
                    }
                });

                const availableCreditLimit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                if (parseFloat(grandTotal.toFixed(2)) > availableCreditLimit) {
                    throw new Error('Not enough credit');
                }
            }

            /// fetch user for user details
            const user = await models.users.findOne({
                where: { id: userId },
                attributes: { exclude: ['password', 'otp', 'fcm_token'] },
                transaction
            });

            /// fetch latest generate invoice number
            const lastInvoice: any = await models.orders.findOne({
                order: [['createdAt', 'DESC']],
                attribute: ['invoice_number', 'financial_year']
            });

            /// generate invoice number
            const invoiceNumber: string = `${process.env.INVOICE_PREFIX}${getInvoiceNumber(
                lastInvoice
            )}${calculateFinancialYear().replace('-', '')}`;

            /// generate order code
            const orderCode = makeUniqueKey(10);

            /// order data
            const orderData: any = {
                user_id: userId,
                // buy_request_id: buyRequest.id,
                amount: jewellery?.total_amount,
                unified_order_type: unifiedOrderType.JEWELLERY,
                shipment_id,
                shipment_price,
                tax_price: taxPrice,
                grand_total: grandTotal,
                payment_status: 'PAID',
                jewellery_array: jewellery?.jewellery_array,
                order_code: orderCode,
                order_status: 'PENDING',
                billing_address,
                shipping_address,
                payment_mode,
                user_details: user,
                invoice_number: invoiceNumber,
                financial_year: calculateFinancialYear()
            };

            logger.info('Creating order for jewellery!!');
            /// create order
            const order = await models.orders.create(orderData, { transaction });

            logger.info('Initiating payment for jewellery order');
            /// capture amount for order
            if (payment_mode === PaymentMode.creditCard) {
                /// check for customer profile id exists
                if (!userData?.customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }

                /// authorize credit card
                await authorizePayment.authorizeCreditCard(
                    false,
                    card_number,
                    card_holder_name,
                    exp_date,
                    cvv,
                    userId,
                    null,
                    transaction,
                    null,
                    order
                );

                /// capture amount
                await authorizePayment.capturePreviouslyAuthorizedAmount(null, transaction, null, order.id);
            }
            /// apple pay
            else if (payment_mode === PaymentMode.applePay) {
                /// check for customer profile id exists
                if (!userData.customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }
                /// ApplePay
                await authorizePayment.createApplePayTransaction(
                    null,
                    apple_pay_response,
                    userId,
                    transaction,
                    order.id
                );
            }
            /// credit limit
            else if (payment_mode === PaymentMode.creditLimit) {
                logger.info('Debiting credit-limit for jewellery');
                /// debit credit limit
                await models.credit_histories.create(
                    {
                        user_id: userId,
                        credit: grandTotal,
                        type: 'CREATE-ORDER',
                        transaction_type: 'DEBIT',
                        order_id: order?.id
                    },
                    { transaction }
                );
            }

            /// fetch order for order trail
            const orderPayload = await models.orders.findOne({ where: { id: order.id }, transaction });

            logger.info('Creating order trails for jewellery');
            /// create order trail
            await models.order_trails.create(
                {
                    order_id: orderPayload?.id,
                    user_id: orderPayload?.user_id,
                    buy_request_id: orderPayload?.buy_request_id,
                    updated_by_id: userId,
                    payload: JSON.stringify(orderPayload),
                    payment_status: orderPayload?.payment_status, // pending, paid, failed, canceled
                    order_status: orderPayload?.order_status // pending, processing, shipped, delivered, canceled
                },
                { transaction }
            );

            /// update user order count
            await models.users.update(
                { order_count: sequelize.literal('order_count + 1') },
                { where: { id: userId }, transaction }
            );

            /// commit transaction
            await transaction.commit();

            /// notify user
            try {
                userNotification.sendOderCreatedNotifications(
                    NotificationType.orderCreated,
                    orderPayload?.user_id,
                    orderPayload?.id
                );
            } catch (error: any) {
                logger.error(`!!!error sending notification jewellery order created!!!!! ${error}`);
            }

            return {
                jewellery: {
                    id: orderPayload.id,
                    order_code: orderPayload.order_code,
                    grand_total: orderPayload.grand_total
                }
            };
        } catch (e) {
            /// rollback transaction
            await transaction.rollback();
            logger.error(`error creating jewellery order ${e}`);
            throw e;
        }
    };

    /// create order for melee
    createOrderForMeleeOnly = async (req: Request) => {
        logger.info('Calling function create melee order only!!!!!');
        const transaction = await sequelize.transaction();

        try {
            const userId = req[`id`];
            const userData: any = req[`user`];

            const {
                card_number,
                card_holder_name,
                exp_date,
                cvv,
                payment_mode,
                melee,
                total_amount,
                billing_address,
                shipping_address,
                shipment_id,
                shipment_price,
                apple_pay_response
            } = req.body;

            const taxPrice: number = userData?.is_verified
                ? 0
                : parseFloat(melee?.total_amount) * (parseFloat(process.env.TAX_PERCENT || '0') / 100);

            const grandTotal = parseFloat(melee?.total_amount) + parseFloat(shipment_price) + taxPrice;

            if (payment_mode === PaymentMode.creditLimit) {
                logger.info(`Calculating available credit`);
                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: userId
                    }
                });

                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: userId
                    }
                });

                const availableCreditLimit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                if (parseFloat(grandTotal.toFixed(2)) > availableCreditLimit) {
                    throw new Error('Not enough credit');
                }
            }

            /// fetch user for user details
            const user = await models.users.findOne({
                where: { id: userId },
                attributes: { exclude: ['password', 'otp', 'fcm_token'] },
                transaction
            });

            /// fetch latest generate invoice number
            const lastInvoice: any = await models.orders.findOne({
                order: [['createdAt', 'DESC']],
                attribute: ['invoice_number', 'financial_year']
            });

            /// generate invoice number
            const invoiceNumber: string = `${process.env.INVOICE_PREFIX}${getInvoiceNumber(
                lastInvoice
            )}${calculateFinancialYear().replace('-', '')}`;

            /// generate order code
            const orderCode = makeUniqueKey(10);

            logger.info('Creating order for melle');
            /// order data
            const orderData: any = {
                user_id: userId,
                // buy_request_id: buyRequest.id,
                amount: melee?.total_amount,
                shipment_id,
                shipment_price,
                tax_price: taxPrice,
                grand_total: grandTotal,
                payment_status: 'PAID',
                melee_array: melee?.melee_array,
                order_code: orderCode,
                unified_order_type: unifiedOrderType.MELEE,
                order_status: 'PENDING',
                billing_address,
                shipping_address,
                payment_mode,
                user_details: user,
                invoice_number: invoiceNumber,
                financial_year: calculateFinancialYear()
            };

            /// create order
            const order = await models.orders.create(orderData, { transaction });

            logger.info('Initiating payment for melle order');
            /// capture amount for order
            if (payment_mode === PaymentMode.creditCard) {
                /// check for customer profile id exists
                if (!userData?.customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }

                /// authorize credit card
                await authorizePayment.authorizeCreditCard(
                    false,
                    card_number,
                    card_holder_name,
                    exp_date,
                    cvv,
                    userId,
                    null,
                    transaction,
                    null,
                    order
                );

                /// capture amount
                await authorizePayment.capturePreviouslyAuthorizedAmount(null, transaction, null, order?.id);
            }

            /// apple pay
            else if (payment_mode === PaymentMode.applePay) {
                /// check for customer profile id exists
                if (!userData.customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }
                /// ApplePay
                await authorizePayment.createApplePayTransaction(
                    null,
                    apple_pay_response,
                    userId,
                    transaction,
                    order?.id
                );
            }
            /// credit limit
            else if (payment_mode === PaymentMode.creditLimit) {
                logger.info('Debiting credit-limit for melle');
                /// debit credit limit
                await models.credit_histories.create(
                    {
                        user_id: userId,
                        credit: grandTotal,
                        type: 'CREATE-ORDER',
                        transaction_type: 'DEBIT',
                        order_id: order?.id
                    },
                    { transaction }
                );
            }

            /// fetch order for order trail
            const orderPayload = await models.orders.findOne({ where: { id: order?.id }, transaction });

            logger.info('Creating order trails!!!');
            /// create order trail
            await models.order_trails.create(
                {
                    order_id: orderPayload?.id,
                    user_id: orderPayload?.user_id,
                    buy_request_id: orderPayload?.buy_request_id,
                    updated_by_id: userId,
                    payload: JSON.stringify(orderPayload),
                    payment_status: orderPayload?.payment_status, // pending, paid, failed, canceled
                    order_status: orderPayload?.order_status // pending, processing, shipped, delivered, canceled
                },
                { transaction }
            );

            /// update user order count
            await models.users.update(
                { order_count: sequelize.literal('order_count + 1') },
                { where: { id: userId }, transaction }
            );

            await transaction.commit();

            /// notify user
            try {
                userNotification.sendOderCreatedNotifications(
                    NotificationType.orderCreated,
                    orderPayload?.user_id,
                    orderPayload?.id
                );
            } catch (error: any) {
                logger.error(`!!!error sending notification melle order created!!!!! ${error}`);
            }

            return {
                melee: {
                    id: orderPayload.id,
                    order_code: orderPayload.order_code,
                    grand_total: orderPayload.grand_total
                }
            };

            /// commit transaction
        } catch (e) {
            /// rollback transaction
            await transaction.rollback();
            logger.error(`error creating jewellery order ${e}`);
            throw e;
        }
    };

    /**
     * @api {get} /v1/auth/user/unified-order
     * @apiName ListUnifiedOrder
     * @apiGroup UnifiedOrders
     *
     *
     * @apiSuccess {Object} UnifiedOrders.
     */
    async listUnifiedOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list unified order');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const order_status = req.query.order_status;
            const payment_status = req.query.payment_status;
            const userId = req[`id`];
            const conditions: any = [];

            if (order_status) {
                conditions.push({ order_status });
            }
            if (payment_status) {
                conditions.push({ payment_status });
            }

            conditions.push({ user_id: userId });

            const { rows, count } = await models.orders.findAndCountAll({
                where: { [Op.and]: conditions },
                include: [
                    {
                        model: models.return_orders,
                        required: false
                    }
                ],
                offset: skip,
                limit,
                order: [['updatedAt', 'DESC']]
            });

            const buyRequestIds = rows.map((order: any) => order.buy_request_id);

            const buyRequests = await models.buy_requests.findAll({ where: { id: { [Op.in]: buyRequestIds } } });

            const result = rows.map((order: any) => ({
                ...JSON.parse(JSON.stringify(order)),
                buy_request_details: buyRequests.find((item: any) => item.id === order.buy_request_id)
            }));

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order listed successfully`,
                data: result,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/unified-order-details
     * @apiName UnifiedOrderDetails
     * @apiGroup UnifiedOrders
     *
     *
     * @apiSuccess {Object} UnifiedOrders.
     */
    async unifiedOrderDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling unified order details');
        try {
            const id = req.query.id;
            const userId = req[`id`];

            const order: OrderAttributes = await models.orders.findOne({
                where: { id },
                include: [
                    {
                        model: models.return_orders,
                        required: false
                    }
                ]
            });

            if (!order) {
                throw new Error(`Order not found!!`);
            }

            if (order.user_id !== userId) {
                throw new Error('unauthorized access');
            }

            let buyRequest: any;
            let stocks: any[];

            if ((order?.unified_order_type ?? unifiedOrderType.DIAMONDS) === unifiedOrderType.DIAMONDS) {
                buyRequest = await models.buy_requests.findOne({
                    where: { id: order.buy_request_id }
                });

                const stockIds = buyRequest.stock_ids.map((item: any) => item.stock_id);

                /// fetch stocks
                stocks = await models.stocks.findAll({
                    where: { id: { [Op.in]: stockIds } },
                    attributes: [
                        'id',
                        'stock_id',
                        'status',
                        'weight',
                        'color',
                        'clarity',
                        'shape',
                        'cut',
                        'polish',
                        'symmetry',
                        'fluorescence_intensity',
                        'discounts',
                        'price_per_caret',
                        'final_price',
                        'diamond_type',
                        'is_lab_grown',
                        'growth_type',
                        'admin_id',
                        'vendor_id',
                        'sku_number'
                    ]
                });

                /// replace stock final price with offer price
                if (order.type === buyRequestType.offer) {
                    for (const stock of stocks) {
                        const offer: any = await models.stock_offers.findOne({ where: { stock_id: stock.id } });

                        /// set offer price for user
                        stock.final_price = offer?.offer_price;
                        stock.price_per_caret = offer?.offer_price / (stock?.weight ?? 0);
                    }
                }
            }

            // /// total amount
            // const totalAmount = buyRequest.stock_ids
            //     .filter((item: any) => isStocksAvailable(item))
            //     .map((item: any) => item.stock_id)
            //     .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price)
            //     .reduce((a: any, b: any) => a + b, 0);

            /// total cts
            const totalCTS = buyRequest
                ? buyRequest?.stock_ids
                      .filter((item: any) => isStocksAvailable(item))
                      .map((item: any) => item.stock_id)
                      .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.weight ?? 0)
                      .reduce((a: any, b: any) => a + b, 0)
                : 0;

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order details listed successfully`,
                data: {
                    ...JSON.parse(JSON.stringify(order)),
                    total_cts: totalCTS,
                    total_amount: order.amount,
                    price_per_carat: parseFloat((parseFloat(order.amount) / parseFloat(totalCTS)).toFixed(2)),
                    buy_request_details: buyRequest
                        ? {
                              ...JSON.parse(JSON.stringify(buyRequest ?? {})),
                              stock_ids: buyRequest?.stock_ids.map((item: any) => ({
                                  ...item,
                                  stock: stocks.find((stock: any) => stock.id === item.stock_id)
                              }))
                          }
                        : {}
                }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new UnifiedOrder();
