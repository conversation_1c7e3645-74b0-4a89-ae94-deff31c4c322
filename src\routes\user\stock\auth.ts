import { IRouter, NextFunction, Request, Response, Router } from 'express';

import stock from '../../../controllers/user/stock';
import { fieldsValidator } from '../../../middlewares/validator';
import { getStockStatusSchema } from '../../../utils/schema/stock_schema';

const routes: IRouter = Router();

// list stock
routes.post('/stock', stock.listStocksOptimized);

// list stock details
routes.get('/stock-details', stock.getStockDetails);

// list stock details
routes.get('/get-stock-details', stock.getStockDetailsFromStockId);

// list stock status
routes.post(
    '/stock-status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, getStockStatusSchema),
    stock.getStockStatus
);

export default routes;
