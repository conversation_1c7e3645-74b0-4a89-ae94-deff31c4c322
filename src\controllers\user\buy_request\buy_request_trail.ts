import { NextFunction, Request, Response } from 'express';
import models from '../../../models';
import { httpStatusCodes } from '../../../utils/constants';
import { logger } from '../../../utils/logger';

class BuyRequestTrail {
    /**
     * @api {get} /v1/auth/buy-request-trail
     * @apiName listBuyRequestTrail
     * @apiGroup BuyRequestTrail
     *
     *
     * @apiSuccess {Object} BuyRequestTrail
     */

    async listTrailsForBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling list buy requests trails!!!!');
        try {
            const id = req.query.id;

            if (!id) {
                throw new Error(`id required`);
            }

            const trail = await models.buy_request_trails.findAndCountAll({
                where: {
                    buy_request_id: id
                },
                order: [['createdAt', 'ASC']]
            });

            if (!trail) {
                throw new Error(`buy request data not found`);
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Buy request trail listed successfully',
                data: trail.rows,
                count: trail.count
            });

            //////
        } catch (error) {
            logger.error(error);
            next(error);
        }
    }
}

export default new BuyRequestTrail();
