import { Request, Response, IRouter, NextFunction, Router } from 'express';
import offers from '../../../controllers/user/stock_offers';
import { fieldsValidator } from '../../../middlewares/validator';
import { createOfferSchema, placeOrderSchema, updateOfferSchema } from '../../../utils/schema/offer_schema';

const routes: IRouter = Router();

// create offer
routes.post(
    '/stock-offer',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createOfferSchema),
    offers.createStockOffer
);

// update offer
routes.put(
    '/update-offer',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateOfferSchema),
    offers.updateOffer
);

// stock offer
routes.get('/stock-offer', offers.listStockOffer);

// offer buy requests
routes.get('/offer-buy-request', offers.getOfferBuyRequest);

// place order
routes.post(
    '/place-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, placeOrderSchema),
    offers.placeOrder
);

// list offer trails
routes.get('/offer-trails', offers.listOfferTrails);

// list offer details
routes.get('/offer-details', offers.listOfferDetails);

export default routes;
