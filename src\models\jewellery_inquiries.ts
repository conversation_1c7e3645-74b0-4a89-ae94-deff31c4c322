import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface JewelleryInquiryAttributes {
    id?: string;
    product_id: string;
    variant_id: string;
    inquiry_id: string;
    product_details: object;
    variant_price: number;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface JewelleryInquiryCreationAttributes extends Optional<JewelleryInquiryAttributes, 'id'> {}

interface JewelleryInquiryInstance
    extends Model<JewelleryInquiryAttributes, JewelleryInquiryCreationAttributes>,
        JewelleryInquiryAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type JewelleryInquiryStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => JewelleryInquiryInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const jewellery_inquiries = sequelize.define<JewelleryInquiryInstance>(
        'jewellery_inquiries',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            product_id: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            variant_id: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            inquiry_id: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            product_details: {
                type: DataTypes.JSONB,
                allowNull: false
            },
            variant_price: {
                type: DataTypes.DOUBLE,
                allowNull: true,
                defaultValue: 0
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as JewelleryInquiryStatic;

    // TODO: make common function to sync
    // await jewellery_inquiries.sync({ alter: true });

    return jewellery_inquiries;
};
