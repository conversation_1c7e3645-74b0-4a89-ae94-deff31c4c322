import { NextFunction, Request, Response } from 'express';
import { NotificationType, buyRequestType, httpStatusCodes, isStocksAvailable } from '../../utils/constants';
import models from '../../models';
import { Op } from 'sequelize';
import { logger } from '../../utils/logger';
import { OrderAttributes } from '../../models/orders';
import { StockAttributes } from '../../models/stocks';
import { BuyRequestAttributes } from '../../models/buy_requests';
import { FirebaseController } from './user_notifications/firebase_controller';

class Order {
    /**
     * @api {get} /v1/auth/user/order
     * @apiName ListOrder
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} Orders.
     */
    async listOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list order');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const order_status = req.query.order_status;
            const payment_status = req.query.payment_status;
            const userId = req[`id`];
            const conditions: any = [];

            if (order_status) {
                conditions.push({ order_status });
            }
            if (payment_status) {
                conditions.push({ payment_status });
            }

            conditions.push({ user_id: userId });

            const { rows, count } = await models.orders.findAndCountAll({
                where: { [Op.and]: conditions },
                include: [
                    {
                        model: models.return_orders,
                        required: false
                    }
                ],
                offset: skip,
                limit,
                order: [['createdAt', 'DESC']]
            });

            const buyRequestIds = rows.map((order: any) => order.buy_request_id);

            const buyRequests = await models.buy_requests.findAll({ where: { id: { [Op.in]: buyRequestIds } } });

            const result = rows
                .map((order: any) => ({
                    ...JSON.parse(JSON.stringify(order)),
                    buy_request_details: buyRequests.find((item: any) => item.id === order.buy_request_id)
                }))
                .filter((order: any) => order.buy_request_details);

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order listed successfully`,
                data: result,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/order-details
     * @apiName OrderDetails
     * @apiGroup Orders
     *
     *
     * @apiSuccess {Object} Orders.
     */
    async orderDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling orderDetails');
        try {
            const id = req.query.id;
            const userId = req[`id`];

            const order: OrderAttributes = await models.orders.findOne({
                where: { id },
                include: [
                    {
                        model: models.return_orders,
                        required: false
                    }
                ]
            });

            if (!order) {
                throw new Error(`Order not found!!`);
            }

            if (order.user_id !== userId) {
                throw new Error('unauthorized access');
            }

            const buyRequest: BuyRequestAttributes = await models.buy_requests.findOne({
                where: { id: order.buy_request_id }
            });

            const stockIds = buyRequest.stock_ids.map((item: any) => item.stock_id);

            /// fetch stocks
            const stocks: StockAttributes[] = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'price_per_caret',
                    'final_price',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type',
                    'admin_id',
                    'vendor_id',
                    'sku_number'
                ]
            });

            /// replace stock final price with offer price
            if (order.type === buyRequestType.offer) {
                for (const stock of stocks) {
                    const offer: any = await models.stock_offers.findOne({ where: { stock_id: stock.id } });

                    /// set offer price for user
                    stock.final_price = offer?.offer_price;
                    stock.price_per_caret = offer?.offer_price / (stock?.weight ?? 0);
                }
            }

            // /// total amount
            // const totalAmount = buyRequest.stock_ids
            //     .filter((item: any) => isStocksAvailable(item))
            //     .map((item: any) => item.stock_id)
            //     .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price)
            //     .reduce((a: any, b: any) => a + b, 0);

            /// total cts
            const totalCTS = buyRequest.stock_ids
                .filter((item: any) => isStocksAvailable(item))
                .map((item: any) => item.stock_id)
                .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.weight ?? 0)
                .reduce((a: any, b: any) => a + b, 0);

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order details listed successfully`,
                data: {
                    ...JSON.parse(JSON.stringify(order)),
                    total_cts: totalCTS,
                    total_amount: order.amount,
                    price_per_carat: parseFloat((parseFloat(order.amount) / parseFloat(totalCTS)).toFixed(2)),
                    buy_request_details: {
                        ...JSON.parse(JSON.stringify(buyRequest)),
                        stock_ids: buyRequest.stock_ids.map((item: any) => ({
                            ...item,
                            stock: stocks.find((stock: any) => stock.id === item.stock_id)
                        }))
                    }
                }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Order();
