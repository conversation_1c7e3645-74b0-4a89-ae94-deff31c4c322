import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface VendorRejectedHoldRequestsAttributes {
    id?: string;
    status?: string;
    buy_request_id: string;
    vendor_id: string;
    stock_id: string;
    reject_reason: string;
    rejected_by: string;
    approved_margin: object;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface VVendorRejectedHoldRequestsCreationAttributes extends Optional<VendorRejectedHoldRequestsAttributes, 'id'> { }

interface VendorRejectedHoldRequestsInstance
    extends Model<VendorRejectedHoldRequestsAttributes, VVendorRejectedHoldRequestsCreationAttributes>,
    VendorRejectedHoldRequestsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type VendorRejectedHoldRequestsStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => VendorRejectedHoldRequestsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const vendor_rejected_hold_requests = sequelize.define<VendorRejectedHoldRequestsInstance>(
        'vendor_rejected_hold_requests',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            status: {
                type: DataTypes.ENUM('ACCEPTED', 'DECLINED'),
                defaultValue: 'DECLINED',
                allowNull: true
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            rejected_by: {
                type: DataTypes.UUID,
                allowNull: false
            },
            reject_reason: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            approved_margin: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as VendorRejectedHoldRequestsStatic;

    vendor_rejected_hold_requests.associate = (models) => {
        vendor_rejected_hold_requests.belongsTo(models.buy_requests, {
            foreignKey: 'buy_request_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        vendor_rejected_hold_requests.belongsTo(models.vendors, {
            foreignKey: 'vendor_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        vendor_rejected_hold_requests.belongsTo(models.stocks, {
            foreignKey: 'stock_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    // TODO: make common function to sync
    // await vendor_rejected_hold_requests.sync({ alter: true });

    return vendor_rejected_hold_requests;
};
