import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import cart from '../../../controllers/user/cart';
import { addToCartSchema, removeAllCartSchema, removeCartSchema, updateCartSchema } from '../../../utils/schema/cart_schcma';

const routes: IRouter = Router();

// list cart items
routes.get('/list-cart', cart.listCartItems);

// add to cart
routes.post('/add-to-cart', (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addToCartSchema), cart.addToCart);

// remove from cart post
routes.post('/remove-from-cart', (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, removeCartSchema), cart.removeFromCart);

/// update quantity
routes.post('/update-cart-item', (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateCartSchema), cart.updateCartItem);

/// remove all cart items
routes.post('/remove-all-from-cart', (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, removeAllCartSchema), cart.removeAllFromCart);

export default routes;
