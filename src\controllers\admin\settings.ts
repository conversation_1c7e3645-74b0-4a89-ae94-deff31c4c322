import { NextFunction, Request, Response } from 'express';
import { adminRole, httpStatusCodes } from '../../utils/constants';
import models from '../../models';
import { Op } from 'sequelize';
import bcrypt from 'bcrypt';
import { isOTPExpired } from '../../utils/validators';
import { logger } from '../../utils/logger';
import mailServices from '../user/user_notifications/mail_services';
import { decryptBody, encryptBody } from '../../utils/encrypt';
import axios from 'axios';
import smsServices from '../user/user_notifications/sms_services';

class Settings {
    /**
     *  @api {PUT} /v1/auth/admin/change-email
     *  @apiName changeEmail
     *  @apiGroup ChangeEmail
     *
     *  @apiSuccess {Object} ChangeEmail
     */
    async changeEmail(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req[`role`] as adminRole;
            const email = req.body.email;

            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                const admin = req[`admin`];
                const oldEmail = admin.email;
                const reqId = admin.id;

                const adminData = await models.admins.findOne({ where: { email: oldEmail } });

                if (reqId !== adminData.id) {
                    throw new Error('Unauthorized access');
                }

                const emailExists = await models.admins.findOne({ where: { email, id: { [Op.ne]: reqId } } });

                if (emailExists) {
                    throw new Error('Email already exists');
                }

                await models.admins.update({ email }, { where: { email: oldEmail, id: reqId } });

                ///
            } else if (role === adminRole.vendor) {
                const vendor = req[`vendor`];
                const oldEmail = vendor.email;
                const reqId = vendor.id;

                const vendorData = await models.vendors.findOne({ where: { email: oldEmail } });

                if (reqId !== vendorData.id) {
                    throw new Error('Unauthorized access');
                }

                const emailExists = await models.vendors.findOne({ where: { email, id: { [Op.ne]: reqId } } });

                if (emailExists) {
                    throw new Error('Email already exists');
                }

                await models.vendors.update({ email }, { where: { email: oldEmail, id: reqId } });
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Email has been changed successfully'
            });

            return;
        } catch (error) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/change-password
     *  @apiName changePassword
     *  @apiGroup Password
     *
     *  @apiSuccess {Object} Password
     */
    async changePassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { old_password, new_password } = req.body;
            const role = req[`role`] as adminRole;
            let id: string;

            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                id = req[`admin`].id;

                const admin = await models.admins.findOne({
                    where: { id }
                });

                if (!admin) {
                    throw new Error('Admin not found');
                }

                const isPasswordMatched = await bcrypt.compare(old_password, admin.password);

                if (!isPasswordMatched) {
                    throw new Error("Old password doesn't matched");
                }

                const newPassword = await bcrypt.hash(new_password, 12);

                await models.admins.update(
                    {
                        password: newPassword
                    },
                    {
                        where: { id }
                    }
                );
            } else if (role === adminRole.vendor) {
                id = req[`vendor`].id;

                const vendor = await models.vendors.findOne({
                    where: { id }
                });

                if (!vendor) {
                    throw new Error('Vendor not found');
                }

                const isPasswordMatched = await bcrypt.compare(old_password, vendor.password);

                if (!isPasswordMatched) {
                    throw new Error("Old password doesn't matched");
                }

                const newPassword = await bcrypt.hash(new_password, 12);

                await models.vendors.update(
                    {
                        password: newPassword
                    },
                    {
                        where: { id }
                    }
                );
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Password Updated Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/auth/admin/change-ftp-password
     *  @apiName changePassword
     *  @apiGroup ChangePassword
     *
     *  @apiSuccess {Object} ChangePassword
     */
    async changeFTPVendorPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { old_password, new_password, vendor_id } = req.body;
            const role = req[`role`] as adminRole;
            let id = req[`id`];

            if (![adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                throw new Error('Unauthorized access');
            }

            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                if (!vendor_id) {
                    throw new Error('vendor_id is required');
                }
                id = vendor_id;
            }

            const vendor = await models.vendors.findOne({
                where: { id },
                attributes: ['id', 'ftp_username', 'ftp_password']
            });

            if (!vendor) {
                throw new Error('Vendor not found');
            }

            const oldPassword = await decryptBody(vendor?.ftp_password);

            if (oldPassword !== old_password) {
                throw new Error("Old password doesn't matched");
            }

            /// check if ftp created or not
            logger.info('Changing FTP password for vendor on LOGIN');
            const encryptedNewPassword = encryptBody(new_password);

            const isSuccess: boolean = await new Promise(async (resolve, reject) => {
                try {
                    /// post request
                    await axios.post(
                        process.env.UPDATE_FTP_URL ?? '',
                        {
                            ftp_username: vendor?.ftp_username,
                            new_password
                        },
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                authorization: process.env.ADD_STOCK_KEY
                            }
                        }
                    );
                    resolve(true);
                    ///
                } catch (error) {
                    logger.error(error);
                    reject(error);
                    /// resolve(false);
                }
            });

            /// if ftp updated successfully then update password in db
            if (isSuccess) {
                await models.vendors.update({ ftp_password: encryptedNewPassword }, { where: { id } });
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Password Updated Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/admin/reset-password
     *  @apiName resetPassword
     *  @apiGroup Password
     *
     *  @apiSuccess {Object} ResetPassword
     */
    async resetPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, phone, otp, new_password } = req.body;

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            const admin = await models.admins.findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false }
            });

            if (admin) {
                if (isOTPExpired(admin.updatedAt)) {
                    throw new Error('OTP Expired');
                }

                if (otp !== admin.otp) {
                    throw new Error('Incorrect OTP');
                }

                const hashPassword = await bcrypt.hash(new_password, 12);

                await models.admins.update(
                    {
                        password: hashPassword
                    },
                    {
                        where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                    }
                );
            } else {
                const vendor = await models.vendors.findOne({
                    where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false }
                });

                if (!vendor) {
                    throw new Error(`${email ? 'email' : 'phone'} not found`);
                }

                if (isOTPExpired(vendor.updatedAt)) {
                    throw new Error('OTP Expired');
                }

                if (otp !== vendor.otp) {
                    throw new Error('Incorrect OTP');
                }

                const hashPassword = await bcrypt.hash(new_password, 12);

                await models.vendors.update(
                    {
                        password: hashPassword
                    },
                    {
                        where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                    }
                );
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Password Updated Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/admin/send-verification-otp
     *  @apiName changeVerificationOtp
     *  @apiGroup Password
     *
     *  @apiSuccess {Object} VerificationOtp
     */
    async sendVerificationOtp(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, phone } = req.body;
            const otp = Math.floor(100000 + Math.random() * 900000);

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            const admin = await models.admins.findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false }
            });

            if (admin) {
                await models.admins.update(
                    {
                        otp
                    },
                    {
                        where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                    }
                );
            } else {
                ///
                const vendor = await models.vendors.findOne({
                    where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false }
                });

                if (!vendor) {
                    throw new Error('Vendor not found');
                }

                await models.vendors.update(
                    {
                        otp
                    },
                    {
                        where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                    }
                );
            }
            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Verification OTP sent successfully'
                // otp
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/update-system-settings
     *  @apiName UpdateSystemSettings
     *  @apiGroup SystemSettings
     *
     *  @apiSuccess {Object} UpdateSystemSettings
     */
    async updateSystemSettings(req: Request, res: Response, next: NextFunction) {
        try {
            const { billing_details, return_within } = req.body;
            const role = req[`role`] as adminRole;

            const dataObject: any = {};

            if (role !== adminRole.superAdmin) {
                throw new Error('Unauthorized access');
            }

            if (!billing_details && !return_within) {
                throw new Error('billing_details or return_within required');
            }

            if (billing_details) {
                dataObject.billing_details = billing_details;
            }
            if (return_within) {
                dataObject.return_within = return_within;
            }

            await models.system_settings.update(dataObject, { where: {} }, { limit: 1 });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'System Settings updated successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/vendor/send-verification-otp
     *  @apiName sendVerificationOtp
     *  @apiGroup sendVerificationOTP
     *
     *  @apiSuccess {Object} VerificationOtp
     */
    async sendVendorVerificationOtp(req: Request, res: Response, next: NextFunction) {
        try {
            const { phone } = req.body;
            let email = req.body.email;

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            const vendor = await models.vendors.findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false },
                attributes: ['id','email', 'phone', 'first_name', 'last_name']
            });

            if (!vendor) {
                throw new Error('We could not find your account');
            }

            const otp = Math.floor(100000 + Math.random() * 900000);

            await models.vendors.update(
                {
                    otp
                },
                {
                    where: { id: vendor?.id }
                }
            );

            let isOTPSent = false;

            /// send email
            if (vendor?.email) {
                try {
                    const emailMessage =
                        'We received a request to verify your identity. To proceed, please use the One-Time Password (OTP) code below:' +
                        `<br><br><b>OTP Code: ${otp}<b><br>`;

                    /// send mail
                    await mailServices.send({
                        to: vendor?.email,
                        subject: 'Your One-Time Password (OTP) Code',
                        data: { name: `${vendor.first_name} ${vendor.last_name}`, message: emailMessage }
                    });

                    /// set otp sent flag
                    isOTPSent = true;
                } catch (error) {
                    logger.error(`send otp mail error ${JSON.stringify(error)}`);
                }
            }

            /// send otp
            if (vendor?.phone) {
                try {
                    /// add mobile and otp to req.body
                    req.body.mobile = vendor?.phone;
                    req.body.OTP = otp;

                    /// call sendOTP function
                    await smsServices.sendOTP(req, res, next, true);

                    /// set otp sent flag
                    isOTPSent = true;
                } catch (error) {
                    logger.error(`send otp SMS error ${JSON.stringify(error)}`);
                }
            }

            res.json({
                status: isOTPSent? httpStatusCodes.SUCCESS_CODE:httpStatusCodes.SERVER_ERROR_CODE,
                message: isOTPSent? 'Verification OTP sent successfully': 'Failed to send OTP'
                // otp
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/vendor/verify-email
     *  @apiName VerifyEmail
     *  @apiGroup verifyEmail
     *
     *  @apiSuccess {Object} verifyEmail
     */
    async verifyVendorEmail(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, otp } = req.body;

            const vendor = await models.vendors.findOne({
                where: {
                    email: email.toString().toLowerCase().trim(),
                    _deleted: false
                }
            });

            if (!vendor) {
                throw new Error('Vendor not found');
            }

            if (isOTPExpired(vendor.updatedAt)) {
                throw new Error('OTP Expired');
            }

            if (otp !== vendor.otp) {
                throw new Error('Incorrect OTP');
            }

            // await models.vendors.update({ is_email_verified: true }, { where: { id: vendor.id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'email successfully verified'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/vendor/reset-password
     *  @apiName resetPassword
     *  @apiGroup ResetPassword
     *
     *  @apiSuccess {Object} ResetPassword
     */
    async resetVendorPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, phone, otp, new_password } = req.body;

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            const vendor = await models.vendors.findOne({
                where: {
                    [Op.or]: [email ? { email: email.toString().toLowerCase().trim() } : {}, phone ? { phone } : {}],
                    is_blacklisted: false
                }
            });

            if (!vendor) {
                throw new Error('Vendor not found');
            }

            if (isOTPExpired(vendor.updatedAt)) {
                throw new Error('OTP Expired');
            }

            if (otp !== vendor?.otp) {
                throw new Error('Incorrect OTP');
            }

            const hashPassword = await bcrypt.hash(new_password, 12);

            await models.vendors.update(
                {
                    password: hashPassword
                },
                {
                    where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Password Updated Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Settings();
