import { IRouter, NextFunction, Request, Response, Router } from 'express';
import jewelleryShowcase from '../../../controllers/admin/jewellery_showcase';
import { fieldsValidator } from '../../../middlewares/validator';
import { addJewellerySeenVideoSchema } from '../../../utils/schema/jewellery_showcase_schema';


const routes: IRouter = Router();

// jewellery showcase video
routes.get('/jewellery-showcase', jewelleryShowcase.listJewelleryShowcaseVideos);

// add like to jewellery showcase
routes.put('/add-remove-like-jewellery', jewelleryShowcase.addRemoveLikeJewelleryShowcase);

// add seen to jewellery showcase
routes.put('/add-seen-jewellery',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addJewellerySeenVideoSchema),
    jewelleryShowcase.addSeenJewelleryShowcase
);

// list jewellery tags
routes.get('/jewellery-showcase-tags', jewelleryShowcase.listJewelleryTags);



export default routes;
