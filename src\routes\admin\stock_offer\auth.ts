import { Request, Response, IRouter, NextFunction, Router } from 'express';
import offers from '../../../controllers/user/stock_offers';
import { fieldsValidator } from '../../../middlewares/validator';
import { updateOfferSchema } from '../../../utils/schema/offer_schema';

const routes: IRouter = Router();

// get offers
routes.get('/stock-offer', offers.listStockOffer);

// get group by offers
routes.get('/stock-offers-group', offers.listStockOfferGroup);

// update offer
routes.put(
    '/update-offer',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateOfferSchema),
    offers.updateOffer
);

// list offer trails
routes.get('/offer-trails', offers.listOfferTrails);

// list offer details
routes.get('/offer-details', offers.listOfferDetails);

export default routes;
