import { Request, Response, NextFunction } from 'express';
import models, { sequelize } from '../../models';
import {
    adminRole,
    buyRequestType,
    getNotificationType,
    httpStatusCodes,
    NotificationType,
    orderStatus,
    PaymentMode,
    stockStatus,
    unifiedOrderType,
    vendorOrderStatus
} from '../../utils/constants';
import { Op, Sequelize } from 'sequelize';
import { logger } from '../../utils/logger';
import { OrderAttributes } from '../../models/orders';
import userNotification from '../../controllers/user/user_notifications/user_notification';
import authorizePayment from '../user/authorize_payment/authorize_payment';
import { FilterOrderBy, getTestUserIds } from '../../utils/filter_order';
import { BuyRequestAttributes } from '../../models/buy_requests';
import { StockAttributes } from '../../models/stocks';
import policyViolationsService from './policy_violations';

class UnifiedOrder {
    /**
     * @api {get} /v1/auth/admin/unified-order
     * @apiName ListUnifiedOrder
     * @apiGroup UnifiedOrders
     *
     *
     * @apiSuccess {Object} UnifiedOrders.
     */
    async listUnifiedOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list admin unified order');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.order_status;
            const paymentStatus = req.query.payment_status;
            const orderType = req.query.unified_order_type;
            const is_sandbox = String(req.query.is_sandbox).toLowerCase() === 'true';
            const filter_by = req.query.filter_by;
            const filter_keyword = req.query.filter_keyword;
            const conditions: any = [];

            /// order status
            if (status) {
                conditions.push({ order_status: status.toString().toUpperCase() });
            }

            /// payment status
            if (paymentStatus) {
                conditions.push({ payment_status: paymentStatus.toString().toUpperCase() });
            }

            if (!orderType) {
                throw new Error(`unified_order_type required!!!`);
            }

            /// fetch only jewellery and melee order
            conditions.push({ unified_order_type: orderType.toString().toLocaleUpperCase() });

            if (filter_by && filter_keyword) {
                /// filter order conditions
                await UnifiedOrder.orderFilter(filter_by, filter_keyword, conditions, is_sandbox);
            }

            /// fetch only is_test_user orders for sandbox
            /// get test user ids from getTestUserIds function
            const testUserIds = await getTestUserIds();

            /// add test user ids into conditions
            conditions.push({ user_id: is_sandbox ? { [Op.in]: testUserIds } : { [Op.notIn]: testUserIds } });

            /// fetch orders
            const { rows, count } = await models.orders.findAndCountAll({
                where: { [Op.and]: conditions },
                offset: skip,
                limit,
                order: [['updatedAt', 'DESC']]
            });

            /// final orders list
            let finalOrders: any[] = rows;

            /// if order unified order type is DIAMONDS
            if (orderType === unifiedOrderType.DIAMONDS) {
                /// fetch all return orders
                const returnOrders = await models.return_orders.findAll({
                    where: { order_id: { [Op.in]: rows?.map((order: any) => order?.id) } }
                });

                /// extract buy request ids
                const buyRequestIds = rows.map((order: any) => order.buy_request_id);

                /// fetch buy requests
                const buyRequests = await models.buy_requests.findAll({
                    where: { id: { [Op.in]: buyRequestIds } }
                });

                /// add buy request details in result
                const result = rows
                    .map((order: any) => ({
                        ...JSON.parse(JSON.stringify(order)),
                        buy_request_details: JSON.parse(JSON.stringify(buyRequests)).find(
                            (item: any) => item.id === order.buy_request_id
                        ),
                        return_orders: returnOrders?.find((item: any) => item?.order_id === order?.id)
                    }))
                    .filter((order: any) => order?.buy_request_details);

                /// extract all stock ids
                const stockIds = [
                    ...new Set(
                        [].concat(
                            ...result.map((row: any) =>
                                row?.buy_request_details.stock_ids?.map((item: any) => item?.stock_id)
                            )
                        )
                    )
                ];

                /// fetch stock details
                const stocks = await models.stocks.findAll({
                    where: { id: { [Op.in]: stockIds } },
                    attributes: [
                        'id',
                        'stock_id',
                        'price_per_caret',
                        'final_price',
                        'weight',
                        'diamond_type',
                        'is_lab_grown',
                        'growth_type',
                        'stock_margin'
                    ]
                });

                /// update users details
                finalOrders = result.map((row: any) => {
                    /// total cts = sum of weight
                    const totalCTS = stocks.length
                        ? row.buy_request_details.stock_ids
                              .map((item: any) => item.stock_id)
                              .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.weight)
                              .reduce((a: any, b: any) => a + b, 0)
                        : 0;

                    // /// total amount = sum of final price
                    // const totalAmount = stocks
                    //     ? row.buy_request_details.stock_ids
                    //           .map((item: any) => item.stock_id)
                    //           .map((stockId: any) => stocks.find((item: any) => item.id === stockId)?.final_price)
                    //           .reduce((a: any, b: any) => a + b, 0)
                    //     : 0;

                    return {
                        ...JSON.parse(JSON.stringify(row)),
                        buy_request_details: {
                            ...JSON.parse(JSON.stringify(row.buy_request_details)),
                            total_cts: totalCTS,
                            total_amount: row.amount,
                            price_per_carat: parseFloat((row.amount / totalCTS).toFixed(2))
                        }
                    };
                });
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Unified Orders listed successfully`,
                data: finalOrders,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /// fiter order conditions
    static async orderFilter(filter_by: any, filter_keyword: any, conditions: any, is_sandbox: boolean) {
        if (filter_by && filter_keyword) {
            /// fetch orders
            switch (filter_by) {
                /// filter by order code
                case FilterOrderBy.ORDER_CODE:
                    /// filter by order code
                    conditions.push({ order_code: { [Op.iLike]: `%${filter_keyword}%` } });

                    break;
                case FilterOrderBy.CERTIFICATE_NO:
                    /// fetch stocks using certificate no
                    const stocks = await models.stocks.findAll({
                        where: { certificate_number: { [Op.iLike]: `%${filter_keyword}%` } }
                    });

                    // Convert stockIds array to a string format for SQL IN clause
                    const stockIdList = JSON.parse(JSON.stringify(stocks))
                        .map((stock: any) => `'${stock.id}'`)
                        .join(',');

                    /// fetch buy requests using stock ids
                    const buyRequests = await models.buy_requests.findAll({
                        where: stocks?.length
                            ? Sequelize.literal(`
                                EXISTS (
                                    SELECT 1 FROM jsonb_array_elements(to_jsonb(stock_ids)) AS stock
                                    WHERE (stock->>'stock_id')::uuid IN (${stockIdList})
                                )
                            `)
                            : Sequelize.literal(`FALSE`)
                    });

                    conditions.push({
                        buy_request_id: { [Op.in]: buyRequests.map((buyRequest: any) => buyRequest?.id) }
                    });

                    break;
                case FilterOrderBy.VENDOR_NAME:
                    /// fetch vendor using vendor name
                    const vendors = await models.vendors.findAll({
                        where: {
                            [Op.and]: [
                                {
                                    [Op.or]: [
                                        { first_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { last_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { phone: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { email: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { company_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        ...(filter_keyword.split(' ').length > 1
                                            ? [
                                                  {
                                                      [Op.and]: [
                                                          {
                                                              first_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[0]}%`
                                                              }
                                                          },
                                                          {
                                                              last_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[1]}%`
                                                              }
                                                          }
                                                      ]
                                                  }
                                              ]
                                            : [])
                                    ]
                                },
                                { is_test_vendor: is_sandbox }
                            ]
                        }
                    });

                    /// fetch buy requests using vendor_ids present in buy requests
                    const buyRequestsByVendor = await models.buy_requests.findAll({
                        where: {
                            vendor_ids: { [Op.contains]: vendors.map((vendor: any) => vendor?.id) }
                        }
                    });

                    conditions.push({
                        buy_request_id: { [Op.in]: buyRequestsByVendor?.map((buyRequest: any) => buyRequest?.id) }
                    });

                    break;
                case FilterOrderBy.USER_NAME:
                    /// fetch users using user name
                    const users = await models.users.findAll({
                        where: {
                            [Op.and]: [
                                {
                                    [Op.or]: [
                                        { first_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { last_name: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { phone: { [Op.iLike]: `%${filter_keyword}%` } },
                                        { email: { [Op.iLike]: `%${filter_keyword}%` } },
                                        ...(filter_keyword.split(' ').length > 1
                                            ? [
                                                  {
                                                      [Op.and]: [
                                                          {
                                                              first_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[0]}%`
                                                              }
                                                          },
                                                          {
                                                              last_name: {
                                                                  [Op.iLike]: `%${filter_keyword.split(' ')[1]}%`
                                                              }
                                                          }
                                                      ]
                                                  }
                                              ]
                                            : [])
                                    ]
                                },
                                { is_test_user: is_sandbox }
                            ]
                        }
                    });

                    conditions.push({ user_id: { [Op.in]: users?.map((user: any) => user?.id) } });

                    break;
                case FilterOrderBy.STOCK_ID:
                    /// fetch stocks using certificate no
                    const stocksData = await models.stocks.findAll({
                        where: { stock_id: { [Op.iLike]: `%${filter_keyword}%` } }
                    });

                    // Convert stockIds array to a string format for SQL IN clause
                    const stockIdsList = JSON.parse(JSON.stringify(stocksData))
                        ?.map((stock: any) => `'${stock.id}'`)
                        .join(',');

                    /// fetch buy requests using stock ids
                    const buyRequestsList = await models.buy_requests.findAll({
                        where: stocksData?.length
                            ? Sequelize.literal(`
                                EXISTS (
                                    SELECT 1 FROM jsonb_array_elements(to_jsonb(stock_ids)) AS stock
                                    WHERE (stock->>'stock_id')::uuid IN (${stockIdsList})
                                )
                            `)
                            : Sequelize.literal(`FALSE`)
                    });

                    conditions.push({
                        buy_request_id: { [Op.in]: buyRequestsList.map((buyRequest: any) => buyRequest?.id) }
                    });

                    break;
                default:
                    break;
            }
        }
    }

    /**
     * @api {get} /v1/auth/admin/unified-order-details
     * @apiName UnifiedOrderDetails
     * @apiGroup UnifiedOrders
     *
     *
     * @apiSuccess {Object} UnifiedOrders.
     */
    async unifiedOrderDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling admin unified orderDetails');
        try {
            const id = req.query.id;
            const role = req[`role`];

            const orderData: any = await models.orders.findOne({
                where: { id }
            });

            const order: any = JSON.parse(JSON.stringify(orderData));

            if (!order) {
                throw new Error(`Order not found!!`);
            }

            /// if order unified order type is DIAMONDS
            if (order.unified_order_type === unifiedOrderType.DIAMONDS) {
                /// fetch buy request
                const buyRequest: BuyRequestAttributes = await models.buy_requests.findOne({
                    where: { id: order.buy_request_id }
                });

                const vendorIds = buyRequest.stock_ids.map((stock: any) => stock.vendor_id);

                const vendors = await models.vendors.findAll({
                    where: { id: { [Op.in]: vendorIds } }
                });

                /// fetch return orders
                const returnOrders = await models.return_orders.findAll({ where: { order_id: id } });

                const stockIds = buyRequest.stock_ids.map((item: any) => item.stock_id);

                /// fetch stocks
                const stocks: StockAttributes[] = await models.stocks.findAll({
                    where: { id: { [Op.in]: stockIds } },
                    attributes: [
                        'id',
                        'stock_id',
                        'status',
                        'weight',
                        'color',
                        'clarity',
                        'shape',
                        'cut',
                        'polish',
                        'symmetry',
                        'fluorescence_intensity',
                        'discounts',
                        'price_per_caret',
                        'final_price',
                        'diamond_type',
                        'is_lab_grown',
                        'growth_type',
                        'admin_id',
                        'vendor_id',
                        'sku_number',
                        'certificate_number',
                        'stock_margin'
                    ]
                });

                /// replace stock final price with offer price
                if (order.type === buyRequestType.offer) {
                    for (const stock of stocks) {
                        const offer: any = await models.stock_offers.findOne({ where: { stock_id: stock.id } });
                        if (role === adminRole.vendor) {
                            stock.final_price = offer?.updated_offer_price;
                        } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                            stock.final_price = offer?.offer_price;
                        }
                    }
                }

                /// update stock details in buy request
                order.buy_request_details = {
                    ...JSON.parse(JSON.stringify(buyRequest)),
                    stock_ids: buyRequest.stock_ids.map((item: any) => ({
                        ...item,
                        stock: stocks.find((stock: any) => stock.id === item.stock_id),
                        vendor: vendors.find((vendor: any) => vendor.id === item.vendor_id)
                    }))
                };

                /// update return orders in order
                order.return_orders = returnOrders?.find((item: any) => item?.order_id === id);
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order details listed successfully`,
                data: order
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/unified-order-status
     * @apiName UpdateUnifiedOrderStatus
     * @apiGroup UnifiedOrders
     *
     *
     * @apiSuccess {Object} UpdateUnifiedOrderStatus.
     */
    async updateUnifiedOrderStatus(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update unified order status');

        const transaction = await sequelize.transaction();

        try {
            const { ids, status, courier_company, awb_number } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];

            const updateObject: any = {};

            const orders = await models.orders.findAll({
                where: { id: { [Op.in]: ids } }
            });

            if (orders.length !== ids.length) {
                throw new Error('Order not found');
            }

            if (status) {
                updateObject.order_status = status;
                if (status === orderStatus.delivered) {
                    updateObject.payment_status = 'PAID';
                } else if (status === orderStatus.shipped) {
                    if (!courier_company || !awb_number) {
                        throw new Error('required courier_company and awb_number');
                    }

                    updateObject.courier_company = courier_company;
                    updateObject.awb_number = awb_number;
                }
            }

            /// check all diamonds order
            const isDiamondsOrder: boolean = orders?.every(
                (order: any) => order?.unified_order_type === unifiedOrderType.DIAMONDS
            );
            /// check all vendor orders are shipped for diamonds order
            if (isDiamondsOrder) {
                if (status === orderStatus.shipped) {
                    /// fetch vendor orders
                    const vendorOrders = await models.vendor_orders.findAll({
                        where: { order_id: { [Op.in]: ids } },
                        transaction
                    });

                    /// client order can move to shipped if vendor order shipped, paid or delivered
                    ids.map((orderId: string) => {
                        const vendorOrder = vendorOrders.find((order: any) => (order.order_id = orderId));
                        if (vendorOrder) {
                            if (
                                ![
                                    vendorOrderStatus.shipped,
                                    vendorOrderStatus.paid,
                                    vendorOrderStatus.delivered
                                ].includes(vendorOrder.status)
                            ) {
                                throw new Error('Vendor orders are not shipped!!!!!');
                            }
                        }
                    });
                }
            }

            logger.info(`Updating unified order status: ${status}`);
            /// update order status
            await models.orders.update(updateObject, { where: { id: { [Op.in]: ids } }, transaction });

            /// fetch orders for order trail
            const orderPayloads = await models.orders.findAll({ where: { id: { [Op.in]: ids } }, transaction });

            /// order trail bulk data
            const orderTrailData = orderPayloads.map((order: any) => ({
                order_id: order?.id,
                user_id: order?.user_id,
                buy_request_id: order?.buy_request_id,
                updated_by_id: reqId,
                payload: JSON.stringify(order),
                payment_status: order?.payment_status, // pending, paid, failed, canceled
                order_status: order?.order_status // pending, processing, shipped, delivered, canceled
            }));

            logger.info('Creating order trails for unified order status changed');
            /// create order trail
            await models.order_trails.bulkCreate(orderTrailData, { transaction });

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order updated successfully`
            });

            try {
                if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    if (
                        status === orderStatus.canceled ||
                        status === orderStatus.delivered
                        // status === orderStatus.shipped
                    ) {
                        /// send notifications to users when order updated
                        userNotification.sendNotification(getNotificationType(status), null, null, orderPayloads);

                        ////
                    }
                }
            } catch (error: any) {
                logger.error(`order update notification error ${JSON.stringify(error, null, 2)}`);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/unified-cancel-order
     * @apiName CancelUnifiedOrder
     * @apiGroup UnifiedOrders
     *
     *
     * @apiSuccess {Object} CancelUnifiedOrder.
     */
    async cancelUnifiedOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling cancel unified order');

        const transaction = await sequelize.transaction();

        try {
            const { id, cancel_reason } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];

            const order = await models.orders.findOne({
                where: { id },
                include: [
                    {
                        model: models.buy_requests,
                        required: false,
                        attributes: [
                            'id',
                            'status',
                            'user_id',
                            'stock_ids',
                            'payment_mode',
                            'trans_id',
                            'ref_trans_id',
                            'card_number',
                            'exp_date',
                            'authorized_amount',
                            'type'
                        ]
                    }
                ]
            });

            if (!order) {
                throw new Error('Order not found');
            }

            /// check vendor order status for vendor orders
            if (order?.unified_order_type === unifiedOrderType.DIAMONDS) {
                /// check all vendor orders are shipped
                const vendorOrders = await models.vendor_orders.findAll({ where: { order_id: id }, transaction });

                if (vendorOrders.length) {
                    /// check vendor order status
                    const isAllVendorOrdersPending: boolean = vendorOrders?.every(
                        (vendorOrder: any) => vendorOrder.status === vendorOrderStatus.pending
                    );

                    /// check all vendor orders are pending
                    if (!isAllVendorOrdersPending) {
                        throw new Error(`Couldn't cancel order, vendor orders are shipped or paid!!!!!`);
                    }
                }
            }

            logger.info('Update order status to cancelled');
            /// update order status
            await models.orders.update(
                { order_status: orderStatus.canceled, cancel_reason },
                { where: { id }, transaction }
            );

            /// fetch orders for order trail
            const orderPayload = await models.orders.findOne({ where: { id } }, { transaction });

            logger.info('Creating order trails for cancelled unified order');
            /// create order trail
            await models.order_trails.create(
                {
                    order_id: orderPayload.id,
                    user_id: orderPayload.user_id,
                    buy_request_id: orderPayload.buy_request_id,
                    updated_by_id: reqId,
                    payload: JSON.stringify(orderPayload),
                    payment_status: orderPayload.payment_status, // pending, paid, failed, canceled
                    order_status: orderPayload.order_status // pending, processing, shipped, delivered, canceled
                },
                { transaction }
            );

            /// move vendor orders to cancelled
            if (orderPayload?.unified_order_type === unifiedOrderType.DIAMONDS) {
                logger.info('Updating vendor orders to cancelled');
                const vendorOrders = await models.vendor_orders.findAll({
                    where: { order_id: id }
                });

                if (vendorOrders?.length) {
                    /// update vendor order status
                    await models.vendor_orders.update(
                        { status: orderStatus.canceled },
                        { where: { order_id: id }, transaction }
                    );

                    /// fetch orders for vendor order trail
                    const vendorOrderPayloads = await models.vendor_orders.findAll({
                        where: { order_id: id },
                        transaction
                    });

                    const vendorOrderBulkCreateObject = vendorOrderPayloads.map((vendorOrderPayload: any) => ({
                        vendor_order_id: vendorOrderPayload.id,
                        order_id: vendorOrderPayload.order_id,
                        buy_request_id: vendorOrderPayload.buy_request_id,
                        vendor_id: vendorOrderPayload.vendor_id,
                        updated_by_id: reqId,
                        payload: JSON.stringify(vendorOrderPayload),
                        status: vendorOrderPayload.status // pending, shipped, paid, canceled
                    }));

                    logger.info('Creating vendor order trails!!!');
                    /// create vendor order trail
                    await models.vendor_order_trails.bulkCreate(vendorOrderBulkCreateObject, { transaction });
                }

                logger.info('Making stocks available!!!!');

                /// make stock available
                await models.stocks.update(
                    { status: stockStatus.available },
                    {
                        where: {
                            id: { [Op.in]: order?.buy_request?.stock_ids?.map((stockId: any) => stockId?.stock_id) }
                        },
                        transaction
                    }
                );
            }

            logger.info('Initiating refund amount for cancelled unified order');
            logger.info(`authorized_amount: ${order?.authorize_payment_details?.authorized_amount}`);
            logger.info(`grand_total: ${order?.grand_total}`);
            /// initiate refund
            /// void transaction
            if (order?.payment_mode === PaymentMode.creditCard) {
                /// check refund amount is less than authorized amount
                if (
                    parseFloat(parseFloat(order?.authorize_payment_details?.authorized_amount).toFixed(2)) >=
                    parseFloat(parseFloat(order?.grand_total).toFixed(2))
                ) {
                    /// refund transaction
                    await authorizePayment.refundTransaction(
                        order?.authorize_payment_details?.authorized_amount,
                        order?.buy_request,
                        transaction,
                        order
                    );
                } else {
                    throw new Error('Refund amount exceeded authorized amount!!!');
                }
            } /// restore credit limit
            else if (order?.payment_mode === PaymentMode.creditLimit) {
                /// credit refund amount when return order accepted
                if (order?.grand_total) {
                    logger.info('Debiting credit limit');
                    /// make credit entry
                    await models.credit_histories.create(
                        {
                            user_id: order?.user_id,
                            credit: parseFloat(parseFloat(order?.grand_total).toFixed(2)),
                            type: 'RETURN-ORDER',
                            transaction_type: 'CREDIT',
                            buy_request_id: order?.buy_request?.id
                        },
                        { transaction }
                    );
                }
            } else if (order?.payment_mode === PaymentMode.applePay) {
                /// check refund amount is less than authorized amount
                if (
                    parseFloat(parseFloat(order?.authorize_payment_details?.authorized_amount).toFixed(2)) >=
                    parseFloat(parseFloat(order?.grand_total).toFixed(2))
                ) {
                    /// refund transaction
                    await authorizePayment.refundTransaction(
                        order?.authorize_payment_details?.authorized_amount,
                        order?.buy_request,
                        transaction,
                        order
                    );
                } else {
                    throw new Error('Refund amount exceeded authorized amount!!!');
                }
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order updated successfully`
            });

            try {
                if (order.unified_order_type === unifiedOrderType.DIAMONDS) {
                    /// fetch vendor orders
                    const vendorOrders = await models.vendor_orders.findAll({
                        where: { order_id: id },
                        attributes: ['id', 'vendor_id', 'stock_id'],
                        raw: true
                    });

                    if (vendorOrders?.length) {
                        /// canceled vendor stones object
                        const canceledVendorStones = vendorOrders?.map((item: any) => ({
                            stock_id: item.stock_id,
                            vendor_id: item.vendor_id,
                            reason: cancel_reason
                        }));

                        /// create unfulfilled order violation
                        policyViolationsService.createUnfulfilledOrderViolation(id, canceledVendorStones);
                    }
                }
            } catch (error: any) {
                logger.error(`Error creating unfulfilled order violation`);
                // Don't throw error to avoid breaking the return order process
            }

            try {
                if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    /// send notifications to users when order updated
                    userNotification.sendNotification(NotificationType.orderCanceled, null, null, [orderPayload]);
                }
            } catch (error: any) {
                logger.error(`order update notification error ${JSON.stringify(error, null, 2)}`);
            }

            try {
                /// send credit limit added notifications when order cancelled
                if (order?.payment_mode === PaymentMode.creditLimit) {
                    /// send notification when credit added
                    userNotification.sendCreditLimitNotifications(NotificationType.creditLimitAdded, order?.user_id);
                }
            } catch (error: any) {
                logger.error(error);
            }

            /// order cancelled notifications to vendor and admin
            try {
                if (order.unified_order_type === unifiedOrderType.DIAMONDS) {
                    userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                        NotificationType.vendorOrderRejected,
                        order?.buy_request_id
                    );
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }
}

export default new UnifiedOrder();
