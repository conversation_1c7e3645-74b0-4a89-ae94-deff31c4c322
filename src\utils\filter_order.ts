import models from "../models";
import moment from "moment";
import { logger } from "./logger";

/// filter order by enum
export enum FilterOrderBy {
    ORDER_CODE = 'ORDER_CODE',
    CERTIFICATE_NO = 'CERTIFICATE_NO',
    VENDOR_NAME = 'VENDOR_NAME',
    USER_NAME = 'USER_NAME',
    STOCK_ID = 'STOCK_ID'
}


/// returned cached users list for last 24 hours
/// pull is_test_user true from database and cache it for 24 hours
/// after 24 hours, pull from database again
export const getTestUserIds = async () => {
    try {
        /// cache duration
        const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

        /// const CACHE_DURATION = 1 * 60 * 1000; // 1 minute in milliseconds

        const now = moment().valueOf(); // Get current timestamp

        // Initialize global cache if it doesn't exist
        if (!global.testUserCache) {
            global.testUserCache = { userIds: null, lastFetchedTime: 0 };
        }

        // Check if cache is still valid
        if (global.testUserCache.userIds && (now - global.testUserCache.lastFetchedTime) < CACHE_DURATION) {
            logger.info("Returning cached user IDs");
            return global.testUserCache.userIds;
        }

        /// Fetch users from database
        const users = await models.users.findAll({
            where: { is_test_user: true },
            attributes: ["id"],
        });

        /// Extract user IDs
        const userIds = users.map((user) => user.id);

        /// Store user IDs in cache for 24 hours
        global.testUserCache = {
            userIds,
            lastFetchedTime: moment().valueOf(),
        };

        logger.info("Returning fresh user IDs");
        return userIds;

    } catch (err) {
        logger.error("Error fetching test user IDs:", err);
        return []; // Return empty array in case of error
    }
}; 