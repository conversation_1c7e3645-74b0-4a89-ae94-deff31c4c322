import express, { Router } from 'express';
import fs from 'fs';
import { UploadConstant } from '../../utils/upload_constant';
const router = Router();

/**
 * Stock xl
 */
router.get('/excel/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_EXCEL}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/stock/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_STOCK}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/download-stock/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.DOWNLOAD_DIR_STOCK}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/banner/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_BANNER}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/user/document/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_DOCUMENT}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/vendor/document/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_VENDOR_DOCUMENT}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/jewellery/video/thumbnail/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_JEWELLERY_VIDEO_THUMBNAIL}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/jewellery/video/:name', (req, res) => {
    const fileName = req.params.name;

    try {
        const videoPath = `${UploadConstant.UPLOAD_DIR_JEWELLERY_VIDEO}/${fileName}`;
        const stat = fs.statSync(videoPath);
        const fileSize = stat.size;

        // Check for the Range header
        const range = req.headers.range;
        if (range) {
            // Example Range header: "bytes=0-"
            // Remove "bytes=" and split into [start, end]
            const parts = range.replace(/bytes=/, '').split('-');
            const start = parseInt(parts[0], 10);
            // If the end is not specified, default to the last byte of the file
            const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;

            // Validate the range
            if (start >= fileSize) {
                res.status(416).send(`Requested range not satisfiable. Start (${start}) >= file size (${fileSize})`);
                return;
            }

            const chunkSize = end - start + 1;
            const fileStream = fs.createReadStream(videoPath, { start, end });

            // Set response headers for Partial Content
            res.writeHead(206, {
                'Content-Range': `bytes ${start}-${end}/${fileSize}`,
                'Accept-Ranges': 'bytes',
                'Content-Length': chunkSize,
                'Content-Type': 'video/mp4'
            });

            // Pipe the video chunk to the response
            fileStream.pipe(res);
        } else {
            // If no Range header is provided, send the entire video file
            res.writeHead(200, {
                'Content-Length': fileSize,
                'Content-Type': 'video/mp4'
            });
            fs.createReadStream(videoPath).pipe(res);
        }
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/invoice/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_INVOICE}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/vendor/order/invoice/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_VENDOR_ORDER_INVOICE}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

router.get('/user/signature/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_SIGNATURE}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

export default router;
