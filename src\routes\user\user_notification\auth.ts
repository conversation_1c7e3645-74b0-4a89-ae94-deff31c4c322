import { I<PERSON><PERSON>er, Router } from 'express';
import user_notification from '../../../controllers/user/user_notifications/user_notification';
import { fieldsValidator } from '../../../middlewares/validator';
import { createNotificationSchema } from '../../../utils/schema/user_notification_schema';

const routes: IRouter = Router();

routes.get('/notification', user_notification.getUserNotification);

routes.post(
    '/notification',
    (req, res, next) => fieldsValidator(req, res, next, createNotificationSchema),
    user_notification.sendUserInquiryNotification
);

routes.get('/notification-unread-count', user_notification.getUserNotificationUnreadCount);

routes.put('/notification-read', user_notification.markAsReadUserNotification);

export default routes;
