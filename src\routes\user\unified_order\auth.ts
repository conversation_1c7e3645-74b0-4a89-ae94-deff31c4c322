import { IRouter, Router, Request, Response, NextFunction } from 'express';
import unifiedOrder from '../../../controllers/user/unified_order';
import { createUnifiedOrderSchema } from '../../../utils/schema/unified_order_schema';
import { fieldsValidator } from '../../../middlewares/validator';

const routes: IRouter = Router();

// get order details
routes.post(
    '/unified-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createUnifiedOrderSchema),
    unifiedOrder.createUnifiedOrder
);

// list unified order
routes.get('/unified-order', unifiedOrder.listUnifiedOrder);

// list unified order
routes.get('/unified-order-details', unifiedOrder.unifiedOrderDetails);

export default routes;
