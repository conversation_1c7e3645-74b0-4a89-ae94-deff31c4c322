import { NextFunction, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import models, { sequelize } from '../../models';
import {
    NotificationType,
    PaymentMode,
    adminRole,
    buyRequestStatus,
    httpStatusCodes,
    isStocksAvailable
} from '../../utils/constants';
import { manageCanceledStatus, manageUpdatedAndPending } from './buy_request/update_buy_request';
import userNotification from './user_notifications/user_notification';
import { Op } from 'sequelize';
import stock from './stock';

class UserBuyRequest {
    /**
     * @api {put} /v1/auth/user/accept-buy-request
     * @apiName AcceptBuyRequest
     * @apiGroup BuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async acceptBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!! Calling accept buy request from user');
        const transaction = await sequelize.transaction();

        const newBody = {
            ...req.body,
            stock_ids: req.body.stock_ids?.map((stockItem: any) => ({
                ...stockItem,
                is_available: String(stockItem.is_available) === 'true',
                is_action_taken: String(stockItem.is_action_taken) === 'true'
            }))
        };
        const { card_number, card_holder_name, exp_date, apple_pay_response } = newBody;

        let stock_ids = newBody.stock_ids;

        try {
            const reqId = req[`id`];
            const role = req[`role`];

            /// buy request id
            const id = req.query.id;

            /// check user is blocked
            if (role === adminRole.user) {
                if (req[`user`].is_blocked) {
                    throw new Error('Please contact admin. User is blocked!!!');
                }
            }

            /// check if buy request exists
            const buyRequest = await models.buy_requests.findOne({ where: { id } });

            if (!buyRequest) {
                throw new Error(`No such buy request found.`);
            }

            if (role === adminRole.user) {
                if (reqId !== buyRequest?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            /// already accepted or cancelled
            if (
                [buyRequestStatus.accepted, buyRequestStatus.autoCanceled, buyRequestStatus.cancelled].includes(
                    buyRequest.status
                )
            ) {
                throw new Error('This buy request is already accepted or cancelled.');
            }

            /// update margin approval fields into request stock_ids from db if not provided in request
            stock_ids = stock_ids?.map((item: any) => {
                const stockId = buyRequest?.stock_ids?.find((stockData: any) => stockData?.stock_id === item?.stock_id);
                if (stockId) {
                    return {
                        ...stockId,
                        ...item
                    };
                }
                return item;
            });

            /// const stockIds = buyRequest.stock_ids.filter((item: any) => isStocksAvailable(item));

            buyRequest.card_number = card_number;
            buyRequest.card_holder_name = card_holder_name;
            buyRequest.exp_date = exp_date;
            buyRequest.stock_ids = stock_ids ? stock_ids : buyRequest?.stock_ids;

            logger.info('Calling update buy request function when accepted by user');
            const result = await manageUpdatedAndPending(
                buyRequest,
                id,
                role,
                reqId,
                buyRequestStatus.pending,
                buyRequest.stock_ids,
                apple_pay_response,
                buyRequest.reject_reason,
                transaction
            );

            /// fetch updated buy request payload
            const updatedBuyRequest = await models.buy_requests.findOne({ where: { id }, transaction });

            logger.info('Creating buy request trail when accepted by user');
            /// add to trail
            await models.buy_request_trails.create(
                {
                    user_id: updatedBuyRequest.user_id,
                    buy_request_id: updatedBuyRequest.id,
                    status: updatedBuyRequest.status,
                    updated_by_id: reqId,
                    payload: JSON.stringify(updatedBuyRequest)
                },
                { transaction }
            );

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: result === false ? `Buy request cancelled` : `Buy request accepted successfully`
            });

            //// notifications
            try {
                const stockStatusChangeIds: any[] = [];

                if (stock_ids?.length) {
                    for (const stockId of stock_ids) {
                        if (stockId?.stock_id) {
                            stockStatusChangeIds.push(stockId?.stock_id);
                        }
                    }
                }

                if (buyRequest?.stock_ids?.length) {
                    for (const stockId of buyRequest?.stock_ids) {
                        if (stockId?.stock_id) {
                            stockStatusChangeIds.push(stockId?.stock_id);
                        }
                    }
                }

                if (stockStatusChangeIds.length) {
                    const stockStatusChangeUniqueIds = [...new Set(stockStatusChangeIds)];

                    /// send notification for stock status change
                    userNotification.stockStatusChangeNotification(stockStatusChangeUniqueIds);
                }
            } catch (error: any) {
                logger.error(error);
            }

            try {
                /// send notification when auto cancelled and credit limit
                if (updatedBuyRequest.status === buyRequestStatus.autoCanceled) {
                    if (updatedBuyRequest.payment_mode === PaymentMode.creditLimit) {
                        /// send notification when credit added
                        userNotification.sendCreditLimitNotifications(
                            NotificationType.creditLimitAdded,
                            updatedBuyRequest.user_id
                        );
                    }
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/user/decline-buy-request
     * @apiName DeclineBuyRequest
     * @apiGroup DeclineBuyRequest
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async declineBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!! Calling decline buy request from user');
        const transaction = await sequelize.transaction();
        try {
            const reqId = req[`id`];

            /// buy request id
            const id = req.query.id;
            const role = req[`role`];

            /// check user is blocked
            if (role === adminRole.user) {
                if (req[`user`].is_blocked) {
                    throw new Error('Please contact admin. User is blocked!!!');
                }
            }

            /// check if buy request exists
            const buyRequest = await models.buy_requests.findOne({ where: { id } });

            if (!buyRequest) {
                throw new Error(`No such buy request found.`);
            }

            if (role === adminRole.user) {
                if (reqId !== buyRequest?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            /// already accepted or cancelled
            if (
                [buyRequestStatus.accepted, buyRequestStatus.autoCanceled, buyRequestStatus.cancelled].includes(
                    buyRequest.status
                )
            ) {
                throw new Error('This buy request is already accepted or cancelled.');
            }

            logger.info('Cancelling buy request by user');
            await manageCanceledStatus(buyRequest, buyRequest.stock_ids, transaction);

            logger.info('Cancelling update buy request with reason');
            /// update buy request status
            await models.buy_requests.update(
                { status: buyRequestStatus.cancelled, reject_reason: buyRequest.reject_reason },
                { where: { id }, transaction }
            );

            /// fetch updated buy request payload
            const updatedBuyRequest = await models.buy_requests.findOne({ where: { id }, transaction });

            logger.info('create buy request trails for cancelled');
            /// add to trail
            await models.buy_request_trails.create(
                {
                    user_id: updatedBuyRequest.user_id,
                    buy_request_id: updatedBuyRequest.id,
                    status: updatedBuyRequest.status,
                    updated_by_id: reqId,
                    payload: JSON.stringify(updatedBuyRequest)
                },
                { transaction }
            );

            /// when user declined buy request then create vendor rejected hold rejected entry for vendors stocks
            if (updatedBuyRequest.vendor_ids.length) {
                /// create vendor rejected hold rejected entry for vendors stocks
                const vendorRejectedHoldRequests = updatedBuyRequest.stock_ids
                    .filter((stockIds: any) => stockIds?.vendor_id)
                    .map((item: any) => {
                        return {
                            buy_request_id: updatedBuyRequest.id,
                            vendor_id: item?.vendor_id,
                            stock_id: item?.stock_id,
                            approved_margin: updatedBuyRequest?.stock_ids?.find(
                                (stockData: any) => stockData?.stock_id === item?.stock_id
                            ),
                            reject_reason: updatedBuyRequest?.reject_reason,
                            rejected_by: reqId
                        };
                    });

                /// bulk crate vendors declined stocks
                await models.vendor_rejected_hold_requests.bulkCreate(vendorRejectedHoldRequests, { transaction });
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Buy request declined successfully`
            });

            try {
                const stockStatusChangeIds: any[] = [];

                if (buyRequest?.stock_ids?.length) {
                    for (const stockId of buyRequest.stock_ids) {
                        if (stockId?.stock_id) {
                            stockStatusChangeIds.push(stockId?.stock_id);
                        }
                    }
                    /// send notification for stock status change
                    userNotification.stockStatusChangeNotification(stockStatusChangeIds);
                }
            } catch (error: any) {
                logger.error(error);
            }

            try {
                if ([buyRequestStatus.cancelled, buyRequestStatus.autoCanceled].includes(updatedBuyRequest.status)) {
                    if (updatedBuyRequest.payment_mode === PaymentMode.creditLimit) {
                        /// send notification when credit added
                        userNotification.sendCreditLimitNotifications(
                            NotificationType.creditLimitAdded,
                            updatedBuyRequest.user_id
                        );
                    }
                }
            } catch (error: any) {
                logger.error(error);
            }

            /// buy request cancelled notifications to vendor and admin
            try {
                if ([buyRequestStatus.cancelled, buyRequestStatus.autoCanceled].includes(updatedBuyRequest.status)) {
                    userNotification.buyRequestUpdatedNotificationToVendorAndAdmin(
                        NotificationType.buyRequestRejected,
                        updatedBuyRequest.id
                    );
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/updated-buy-request-count
     * @apiName UpdatedBuyRequestCount
     * @apiGroup UpdatedBuyRequestCount
     *
     *
     * @apiSuccess {Object} BuyRequest
     */
    async updatedBuyRequestCount(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!! updated buy request count');
        try {
            const reqId = req[`id`];

            /// count updated buy requests
            const count = await models.buy_requests.count({
                where: {
                    [Op.and]: [
                        { [Op.or]: [{ status: buyRequestStatus.updated }, { is_capture_failed: true }] },
                        { user_id: reqId }
                    ]
                }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Updated Buy request count listed successfully`,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new UserBuyRequest();
