import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface StockMarginAttributes {
    id: string; // id is an auto-generated UUID
    shape: string;
    weight: string;
    color: string;
    clarity: string;
    margin: number;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface StockMarginCreationAttributes extends Optional<StockMarginAttributes, 'id'> {}

interface StockMarginInstance
    extends Model<StockMarginAttributes, StockMarginCreationAttributes>,
        StockMarginAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type StockMarginStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => StockMarginInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const stock_margins = sequelize.define<StockMarginInstance>(
        'stock_margins',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            shape: {
                type: DataTypes.STRING,
                allowNull: false
            },
            weight: {
                type: DataTypes.STRING,
                allowNull: false
            },
            color: {
                type: DataTypes.STRING,
                allowNull: false
            },
            clarity: {
                type: DataTypes.STRING,
                allowNull: false
            },
            margin: {
                type: DataTypes.FLOAT,
                allowNull: false,
                defaultValue: 0
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            }
        },
        {
            freezeTableName: true,
            indexes: [
                {
                    name: 'unique_shape_weight_color_clarity_margin',
                    unique: true,
                    fields: ['shape', 'weight', 'color', 'clarity', 'margin']
                }
            ],
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as StockMarginStatic;

    //
    // await stock_margins.sync({ alter: true })

    return stock_margins;
};
