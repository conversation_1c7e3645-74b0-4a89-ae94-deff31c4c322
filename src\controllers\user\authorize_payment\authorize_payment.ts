import { NextFunction, Request, Response } from 'express';
import models from '../../../models';
import {
    applePayRefundStatus,
    AuthorizePaymentStatus,
    AuthorizeTransactionType,
    makeUniqueKey,
    PaymentMode,
    transactionStatus
} from '../../../utils/constants';
import { axiosPost } from './axios_services';
import { Op } from 'sequelize';
import dotenv from 'dotenv';
import { logger } from '../../../utils/logger';

dotenv.config({ path: '../.env ' });

class AuthorizePayment {
    /// create customer profile
    async createCustomerProfile(req: Request, transaction: any) {
        logger.info('Calling create authorized customer profile!!!!!!');
        const { card_holder_name, card_number, exp_date, cvv } = req.body;
        const userId = req[`id`];
        const user: any = req[`user`];
        try {
            const data = {
                createCustomerProfileRequest: {
                    merchantAuthentication: {
                        name: process.env.LOGIN_ID,
                        transactionKey: process.env.TRANSACTION_KEY
                    },
                    profile: {
                        // merchantCustomerId: user.email.toLowerCase(),
                        description: card_holder_name ?? `${user.first_name} ${user.last_name}`,
                        email: user.email,
                        paymentProfiles: {
                            customerType: 'individual',
                            payment: {
                                creditCard: {
                                    cardNumber: card_number,
                                    expirationDate: exp_date,
                                    cardCode: cvv
                                }
                            }
                        }
                    },
                    validationMode: 'testMode' // process.env.PAYMENT_VALIDATION_MODE // 'testMode' || 'liveMode
                }
            };

            /// post request
            const responseData: any = await axiosPost(data);

            if (!responseData?.customerProfileId) {
                throw new Error('Customer profile id not found!!');
            }

            logger.info('Updating customer profile id in users table!!!!!!!!!');

            if (responseData?.customerProfileId) {
                await models.users.update(
                    { customer_profile_id: responseData?.customerProfileId },
                    { where: { id: userId } } // transaction removed
                );
            }

            logger.info('Creating  authorize payment trails for success!!!!!');

            /// authorize payment trails
            await models.authorize_payment_trails.create(
                {
                    user_id: userId,
                    status: AuthorizePaymentStatus.createCustomer,
                    customer_profile_id: responseData.customerProfileId,
                    payload: JSON.stringify(responseData)
                },
                { transaction }
            );
        } catch (error: any) {
            logger.info('Creating  authorize payment trails for error!!!!!');
            /// authorize payment trails
            await models.authorize_payment_trails.create({
                user_id: userId,
                status: AuthorizePaymentStatus.createCustomerError,
                // customer_profile_id: responseData.customerProfileId,
                payload: JSON.stringify(error)
            });

            throw new Error(`create customer error ${JSON.stringify(error)}`);
        }

        ////
    }

    /// authorize credit card
    async authorizeCreditCard(
        reauthorize: boolean,
        card_number: string,
        card_holder_name: string,
        exp_date: string,
        cvv: string,
        userId: string,
        buyRequest: any,
        transaction: any,
        buyRequestStatus: any,
        order?: any
    ) {
        logger.info('Calling authorize credit card!!!!');
        // checking card details are provided or not
        if (!card_number && !card_holder_name && !exp_date) {
            throw new Error('Card details required');
        }

        /// fetch user for customer_profile_id
        const user: any = await models.users.findOne({
            where: { id: userId },
            attributes: ['customer_profile_id', 'first_name', 'last_name'],
            transaction
        });

        if (!user) {
            throw new Error('user not found!!!');
        }

        if (!user.customer_profile_id) {
            throw new Error('customer profile id not found!!');
        }

        try {
            /// line items
            let lineItems: any[] = [];

            /// get line items for buy request
            if (buyRequest) {
                /// fetch stocks for stock details
                // filtering all available stocks
                const stockIds = buyRequest.stock_ids
                    .filter((item: any) => item.is_available)
                    .map((stock: any) => stock.stock_id);

                if (!stockIds?.length) {
                    throw new Error('stock ids not found');
                }

                const stocks = await models.stocks.findAll({
                    where: {
                        id: { [Op.in]: stockIds }
                    },
                    attributes: [
                        'stock_id',
                        'shape',
                        'color',
                        'clarity',
                        'cut',
                        'polish',
                        'status',
                        'admin_id',
                        'vendor_id',
                        'final_price',
                        'diamond_type',
                        'is_lab_grown',
                        'growth_type',
                        'sku_number'
                    ]
                });

                if (!stocks.length) {
                    throw new Error('stocks not found');
                }

                lineItems = stocks.map((stock: any, index: number) => ({
                    itemId: index,
                    name: stock.stock_id,
                    description: `${stock.shape}, ${stock.color}, ${stock.clarity}, ${stock.cut}, ${stock.polish}`,
                    quantity: 1,
                    unitPrice: parseFloat(stock.final_price).toFixed(2)
                }));
            }
            /// get line items for order
            else if (order) {
                if (order.jewellery_array) {
                    lineItems = order.jewellery_array.map((jewellery: any, index: number) => ({
                        itemId: index,
                        name: 'jewellery',
                        description: jewellery?.variant_id ?? '',
                        quantity: jewellery.quantity,
                        unitPrice: parseFloat(jewellery.total_amount).toFixed(2)
                    }));
                } else if (order.melee_array) {
                    lineItems = order.melee_array.map((melee: any, index: number) => ({
                        itemId: index,
                        name: `price_per_carat: ${melee.price_per_carat}`,
                        description: '',
                        quantity: melee.quantity,
                        unitPrice: parseFloat(melee.total_amount).toFixed(2)
                    }));
                }
            }

            /// fetch shipment for shipment details
            const shipment = await models.shipments.findOne({
                where: { id: order ? order.shipment_id : buyRequest.shipment_id },
                transaction
            });

            if (!shipment) {
                throw new Error('shipment not found!!!');
            }

            /// unique ref id
            const uniqueRefId = makeUniqueKey(10);

            /// authorize credit card details
            const authorizeCreditCardRequestData: any = {
                createTransactionRequest: {
                    merchantAuthentication: {
                        name: process.env.LOGIN_ID,
                        transactionKey: process.env.TRANSACTION_KEY
                    },
                    refId: uniqueRefId,
                    transactionRequest: {
                        transactionType: AuthorizeTransactionType.authOnlyTransaction,
                        amount: order
                            ? parseFloat(order.grand_total).toFixed(2)
                            : parseFloat(buyRequest.grand_total).toFixed(2),
                        currencyCode: 'USD',
                        payment: {
                            creditCard: {
                                cardNumber: card_number,
                                expirationDate: exp_date,
                                cardCode: cvv
                            }
                        },
                        lineItems: {
                            lineItem: lineItems
                        },
                        shipping: {
                            amount: parseFloat(shipment.amount).toFixed(2),
                            name: shipment.title,
                            description: shipment.description
                        },
                        customer: {
                            id: user.customer_profile_id
                        },
                        // billTo: {
                        //     firstName: buyRequest.billing_address.first_name,
                        //     lastName: buyRequest.billing_address.last_name,
                        //     address: buyRequest.billing_address.street,
                        //     city: buyRequest.billing_address.city,
                        //     state: buyRequest.billing_address.state,
                        //     zip: buyRequest.billing_address.zip_code,
                        //     country: buyRequest.billing_address.country
                        // },
                        // shipTo: {
                        //     firstName: buyRequest.shipping_address.first_name,
                        //     lastName: buyRequest.shipping_address.last_name,
                        //     address: buyRequest.shipping_address.street,
                        //     city: buyRequest.shipping_address.city,
                        //     state: buyRequest.shipping_address.state,
                        //     zip: buyRequest.shipping_address.zip_code,
                        //     country: buyRequest.shipping_address.country
                        // },
                        userFields: {
                            userField: [
                                {
                                    name: 'userName',
                                    value: `${user?.first_name} ${user?.last_name}`
                                },
                                {
                                    name: 'orderCode',
                                    value: order ? order?.order_code : buyRequest?.order_code
                                }
                            ]
                        },
                        // processingOptions: {
                        //     isSubsequentAuth: 'true'
                        // },
                        // subsequentAuthInformation: {
                        //     originalNetworkTransId: '123456789NNNH',
                        //     originalAuthAmount: '45.00',
                        //     reason: 'resubmission'
                        // },
                        // poNumber: '456654',
                        authorizationIndicatorType: {
                            authorizationIndicator: 'pre'
                        }
                    }
                }
            };

            /// remove cvv when reauthorize
            if (reauthorize === true) {
                delete authorizeCreditCardRequestData.createTransactionRequest.transactionRequest.payment.creditCard
                    .cardCode;
            }

            /// post request to authorize credit card details
            const responseData: any = await axiosPost(authorizeCreditCardRequestData);

            const parsedResponse = JSON.parse(JSON.stringify(responseData));

            if (!parsedResponse.transactionResponse.transId) {
                throw new Error('transId not found');
            }

            // if (!responseData.transactionResponse.refTransID) {
            //     throw new Error('refTransID not found');
            // }

            /// SAVE CARD in buy request table
            /// to save full card number use > card_number
            /// to save last four digits use > parsedResponse?.transactionResponse?.accountNumber

            if (buyRequest) {
                logger.info('Updating authorize details into buy request');
                /// update buy requests
                await models.buy_requests.update(
                    {
                        trans_id: parsedResponse.transactionResponse.transId,
                        ref_id: parsedResponse?.refId,
                        // ref_trans_id: responseData.transactionResponse.refTransID,
                        authorize_transaction_payload: JSON.stringify(parsedResponse),
                        card_holder_name,
                        card_number: parsedResponse?.transactionResponse?.accountNumber,
                        exp_date,
                        card_type: parsedResponse.transactionResponse.accountType,
                        authorized_amount: parseFloat(parseFloat(buyRequest.grand_total).toFixed(2))
                    },
                    { where: { id: buyRequest.id }, transaction }
                );
            } else if (order) {
                logger.info('Updating authorize details into order');
                /// update trans id orders
                await models.orders.update(
                    {
                        authorize_payment_details: {
                            trans_id: parsedResponse.transactionResponse.transId,
                            ref_id: parsedResponse?.refId,
                            authorize_transaction_payload: JSON.stringify(parsedResponse),
                            card_holder_name,
                            card_number: parsedResponse?.transactionResponse?.accountNumber,
                            exp_date,
                            card_type: parsedResponse.transactionResponse.accountType,
                            authorized_amount: parseFloat(parseFloat(order.grand_total).toFixed(2))
                        }
                    },
                    { where: { id: order.id }, transaction }
                );
            }

            logger.info('Creating authorize payment trails for success');

            /// authorize payment trails
            await models.authorize_payment_trails.create(
                {
                    buy_request_id: buyRequest ? buyRequest.id : null,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequest ? buyRequestStatus : null,
                    order_id: order ? order.id : null,
                    amount: order
                        ? parseFloat(parseFloat(order.grand_total).toFixed(2))
                        : parseFloat(parseFloat(buyRequest.grand_total).toFixed(2)),
                    payment_details: {
                        card_holder_name,
                        card_number: parsedResponse?.transactionResponse?.accountNumber,
                        exp_date,
                        card_type: parsedResponse?.transactionResponse?.accountType
                    },
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    ref_id: parsedResponse?.refId,
                    user_id: userId,
                    status: reauthorize ? AuthorizePaymentStatus.reAuthorized : AuthorizePaymentStatus.authorized,
                    customer_profile_id: user.customerProfileId,
                    payload: JSON.stringify(parsedResponse)
                },
                { transaction }
            );
        } catch (error: any) {
            logger.info('Creating authorize payment trails for error');
            logger.error(`authorize error ${JSON.stringify(error, null, 2)}`);
            /// authorize payment trails
            await models.authorize_payment_trails.create({
                buy_request_id: buyRequest ? buyRequest.id : null,
                parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                buy_request_status: buyRequest ? buyRequestStatus : null,
                order_id: order ? order.id : null,
                amount: order
                    ? parseFloat(parseFloat(order.grand_total).toFixed(2))
                    : parseFloat(parseFloat(buyRequest.grand_total).toFixed(2)),
                // payment_details: {
                //     card_holder_name,
                //     card_number: parsedResponse?.transactionResponse?.accountNumber,
                //     exp_date,
                //     card_type: parsedResponse?.transactionResponse?.accountType
                // },
                // trans_id: parsedResponse?.transactionResponse?.transId,
                // ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                // ref_id: parsedResponse?.refId,
                user_id: userId,
                status: reauthorize ? AuthorizePaymentStatus.reAuthorizedError : AuthorizePaymentStatus.authorizedError,
                customer_profile_id: user?.customerProfileId,
                payload: JSON.stringify(error)
            });

            throw new Error(`authorize error ${JSON.stringify(error)}`);
        }

        /// print result
    }

    /// capture previously authorized amount
    async capturePreviouslyAuthorizedAmount(
        buyRequestId: any,
        transaction: any,
        buyRequestStatus: any,
        orderId?: string
    ) {
        logger.info('Calling capture previously authorized amount!!!!!');
        let buyRequest: any;
        let order: any;

        if (buyRequestId) {
            buyRequest = await models.buy_requests.findOne({ where: { id: buyRequestId }, transaction });

            if (!buyRequest) {
                throw new Error('Buy request not found');
            }
        } else if (orderId) {
            order = await models.orders.findOne({ where: { id: orderId }, transaction });
            if (!order) {
                throw new Error('order not found');
            }
        }

        try {
            /// capture amount data
            const requestData = {
                createTransactionRequest: {
                    merchantAuthentication: {
                        name: process.env.LOGIN_ID,
                        transactionKey: process.env.TRANSACTION_KEY
                    },
                    refId: order ? order.order_code : buyRequest.order_code,
                    transactionRequest: {
                        transactionType: AuthorizeTransactionType.priorAuthCaptureTransaction,
                        amount: order
                            ? parseFloat(order.grand_total).toFixed(2)
                            : parseFloat(buyRequest.grand_total).toFixed(2),
                        currencyCode: 'USD',
                        refTransId: order ? order?.authorize_payment_details?.trans_id : buyRequest.trans_id
                    }
                }
            };

            /// post request
            const responseData: any = await axiosPost(requestData);

            const parsedResponse = JSON.parse(JSON.stringify(responseData));

            if (!parsedResponse.transactionResponse.transId) {
                throw new Error('transId not found');
            }

            if (!parsedResponse.transactionResponse.refTransID) {
                throw new Error('refTransID not found');
            }

            if (buyRequest) {
                logger.info('Updating authorize details into buy request');
                /// update buy requests
                await models.buy_requests.update(
                    {
                        trans_id: parsedResponse.transactionResponse.transId,
                        ref_trans_id: parsedResponse.transactionResponse.refTransID,
                        capture_void_transaction_payload: JSON.stringify(parsedResponse)
                    },
                    { where: { id: buyRequest.id }, transaction }
                );
            } else if (order) {
                logger.info('Updating authorize details into order');
                /// update trans id orders
                await models.orders.update(
                    {
                        authorize_payment_details: {
                            ...order?.authorize_payment_details,
                            trans_id: parsedResponse.transactionResponse.transId,
                            ref_trans_id: parsedResponse.transactionResponse.refTransID,
                            capture_void_transaction_payload: JSON.stringify(parsedResponse)
                        }
                    },
                    { where: { id: order.id }, transaction }
                );
            }

            logger.info('Creating authorize payment trails for success!!!');
            /// authorize payment trails
            await models.authorize_payment_trails.create(
                {
                    buy_request_id: buyRequest ? buyRequest.id : null,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequest ? buyRequestStatus : null,
                    order_id: order?.id,
                    amount: order ? order.grand_total : buyRequest.grand_total,
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    ref_id: parsedResponse?.refId,
                    user_id: order ? order.user_id : buyRequest.user_id,
                    status: AuthorizePaymentStatus.capture,
                    payload: JSON.stringify(parsedResponse)
                },
                { transaction }
            );
        } catch (error: any) {
            logger.info('Creating authorize payment trails for error!!!');
            logger.error(`capture transaction error ${JSON.stringify(error, null, 2)}`);
            /// authorize payment trails
            await models.authorize_payment_trails.create({
                buy_request_id: buyRequest ? buyRequest.id : null,
                parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                buy_request_status: buyRequest ? buyRequestStatus : null,
                amount: order ? order.grand_total : buyRequest.grand_total,
                // trans_id: parsedResponse?.transactionResponse?.transId,
                // ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                //  ref_id: parsedResponse?.refId,
                user_id: order ? order.user_id : buyRequest.user_id,
                status: AuthorizePaymentStatus.captureError,
                payload: JSON.stringify(error)
            });

            if (buyRequest) {
                /// update is_capture_failed in buyRequest
                await models.buy_requests.update({ is_capture_failed: true }, { where: { id: buyRequest.id } });
            }

            throw new Error(`capture error ${JSON.stringify(error)}`);
        }
    }

    /// void transaction
    async voidTransaction(buy_request_id: string, transaction: any, buyRequestStatus: any) {
        logger.info('Calling void transactions!!!!!');
        const buyRequest = await models.buy_requests.findOne({ where: { id: buy_request_id }, transaction });

        if (!buyRequest) {
            throw new Error('Buy request not found');
        }

        /// capture amount data
        const requestData = {
            createTransactionRequest: {
                merchantAuthentication: {
                    name: process.env.LOGIN_ID,
                    transactionKey: process.env.TRANSACTION_KEY
                },
                refId: buyRequest.order_code,
                transactionRequest: {
                    transactionType: AuthorizeTransactionType.voidTransaction,
                    amount: parseFloat(buyRequest.authorized_amount).toFixed(2),
                    currencyCode: 'USD',
                    refTransId: buyRequest.trans_id
                }
            }
        };

        try {
            /// post request
            const responseData: any = await axiosPost(requestData);

            const parsedResponse = JSON.parse(JSON.stringify(responseData));

            if (!parsedResponse.transactionResponse.transId) {
                throw new Error('transId not found');
            }

            if (!parsedResponse.transactionResponse.refTransID) {
                throw new Error('refTransID not found');
            }

            logger.info('Updating authorize details into buy request!!!');

            /// update buy requests
            await models.buy_requests.update(
                {
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    capture_void_transaction_payload: JSON.stringify(parsedResponse)
                },
                { where: { id: buyRequest.id }, transaction }
            );

            logger.info('Creating authorize payment trails for success!!!');
            /// authorize payment trails
            await models.authorize_payment_trails.create(
                {
                    buy_request_id: buyRequest.id,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequestStatus,
                    amount: buyRequest.authorized_amount,
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    ref_id: parsedResponse?.refId,
                    user_id: buyRequest.user_id,
                    status: AuthorizePaymentStatus.void,
                    payload: JSON.stringify(parsedResponse)
                },
                { transaction }
            );
        } catch (error: any) {
            logger.info('Creating authorize payment trails for error!!!');
            logger.error(`void transaction error ${JSON.stringify(error, null, 2)}`);
            let shouldThrowError = true;
            if (error.messages && error.messages.message.length) {
                error.messages.message.forEach((message: any) => {
                    if (message.code === 'E00027') {
                        shouldThrowError = false;
                        // The transaction cannot be found
                        /// authorize payment trails
                        models.authorize_payment_trails.create({
                            buy_request_id: buyRequest.id,
                            parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                            buy_request_status: buyRequestStatus,
                            amount: buyRequest.authorized_amount,
                            // trans_id: parsedResponse?.transactionResponse?.transId,
                            // ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                            //  ref_id: parsedResponse?.refId,
                            user_id: buyRequest.user_id,
                            status: AuthorizePaymentStatus.voidError,
                            payload: JSON.stringify(error)
                        });
                    }
                });
            }
            if (shouldThrowError) {
                await models.authorize_payment_trails.create({
                    buy_request_id: buyRequest.id,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequestStatus,
                    amount: buyRequest.authorized_amount,
                    // trans_id: parsedResponse?.transactionResponse?.transId,
                    // ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    //  ref_id: parsedResponse?.refId,
                    user_id: buyRequest.user_id,
                    status: AuthorizePaymentStatus.voidError,
                    payload: JSON.stringify(error)
                });

                throw new Error(`void transaction error ${JSON.stringify(error)}`);
            }
        }
    }

    /// refund transaction
    async refundTransaction(
        refundAmount: any,
        buyRequest: any,
        transaction: any,
        order?: any,
        fromApplePayCron?: boolean
    ) {
        logger.info('Calling refund transactions!!!');
        try {
            /// unique ref id
            const uniqueRefId = makeUniqueKey(10);

            /// 1 : using full credit card
            // creditCard: {
            //       cardNumber: returnOrder?.order?.buy_request?.card_number,
            //       expirationDate: returnOrder?.order?.buy_request?.exp_date,
            // }

            /// 2 : only last four digits of credit card and trans_id
            // payment: {
            //      creditCard: {
            //           cardNumber: returnOrder?.order?.buy_request?.card_number.slice(-4), // str.slice(-4)
            //           expirationDate: 'XXXX'
            //       }
            // },
            // transId: returnOrder?.order?.buy_request?.trans_id

            /// card number
            const cardNumber = order ? order?.authorize_payment_details?.card_number : buyRequest?.card_number;

            /// transId
            const transId = order ? order?.authorize_payment_details?.trans_id : buyRequest?.trans_id;

            logger.info(`Payment Mode ${buyRequest.payment_mode}`);
            if (buyRequest.payment_mode === PaymentMode.applePay && !fromApplePayCron) {
                const transactionDetails: any = await this.getTransactionDetails(transId);

                if (transactionDetails?.transaction?.transactionStatus !== transactionStatus.settled) {
                    logger.info('Creating apple pay refund table entry!!!');
                    // entry in refund table
                    await models.apple_pay_refunds.create(
                        {
                            user_id: order ? order.user_id : buyRequest.user_id,
                            buy_request_id: buyRequest?.id,
                            parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                            refund_status: applePayRefundStatus.pending,
                            transaction_details: JSON.stringify(transactionDetails),
                            refund_amount: parseFloat(String(refundAmount)).toFixed(2),
                            payment_details: order
                                ? order?.payment_details
                                : {
                                    trans_id: buyRequest?.trans_id,
                                    ref_id: buyRequest?.ref_id,
                                    order_id: buyRequest?.order_id,
                                    ref_trans_id: buyRequest?.ref_trans_id
                                },
                            trans_id: transId,
                            ref_id: order ? order?.payment_details?.ref_id : buyRequest?.ref_id,
                            order_id: order?.id,
                            ref_trans_id: order ? order?.payment_details?.ref_trans_id : buyRequest?.ref_trans_id
                        },
                        { transaction }
                    );

                    return true;
                }
            }

            /// capture amount data
            const requestData = {
                createTransactionRequest: {
                    merchantAuthentication: {
                        name: process.env.LOGIN_ID,
                        transactionKey: process.env.TRANSACTION_KEY
                    },
                    refId: uniqueRefId,
                    transactionRequest: {
                        transactionType: AuthorizeTransactionType.refundTransaction,
                        amount: parseFloat(refundAmount).toFixed(2),
                        currencyCode: 'USD',
                        payment: {
                            creditCard: {
                                cardNumber: cardNumber.toString().slice(-4),
                                expirationDate: 'XXXX'
                            }
                        },
                        refTransId: transId
                        // transId: returnOrder?.order?.buy_request?.trans_id
                    }
                }
            };

            /// post request
            const responseData: any = await axiosPost(requestData);

            const parsedResponse = JSON.parse(JSON.stringify(responseData));

            logger.info('Creating authorize payment trails for success!!!');

            /// authorize payment trails
            await models.authorize_payment_trails.create(
                {
                    buy_request_id: buyRequest?.id,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequest?.status,
                    order_id: order?.id,
                    amount: parseFloat(parseFloat(refundAmount).toFixed(2)),
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    ref_id: parsedResponse?.refId,
                    user_id: order ? order?.user_id : buyRequest?.user_id,
                    status: AuthorizePaymentStatus.refund,
                    payload: JSON.stringify(parsedResponse)
                },
                { transaction }
            );
        } catch (error: any) {
            logger.info('Creating authorize payment trails for error!!!');
            logger.error(`refund transaction ${JSON.stringify(error, null, 2)}`);

            /// authorize payment trails
            await models.authorize_payment_trails.create({
                buy_request_id: buyRequest?.id,
                parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                buy_request_status: buyRequest?.status,
                order_id: order?.id,
                amount: parseFloat(parseFloat(refundAmount).toFixed(2)),
                // trans_id: parsedResponse?.transactionResponse?.transId,
                // ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                // ref_id: parsedResponse?.refId,
                user_id: order ? order?.user_id : buyRequest?.user_id,
                status: AuthorizePaymentStatus.refundError,
                payload: JSON.stringify(error)
            });

            /// Apple pay refund error
            /// apple payment trails
            if ((order ? order.payment_mode : buyRequest?.payment_mode) === PaymentMode.applePay) {
                logger.info(
                    `trail object ${JSON.stringify(
                        {
                            token: '',
                            apple_pay_response: JSON.stringify({}),
                            buy_request_id: buyRequest?.id,
                            buy_request_status: buyRequest?.status,
                            order_id: order?.id,
                            amount: parseFloat(parseFloat(refundAmount).toFixed(2)),
                            /// trans_id: parsedResponse?.transactionResponse?.transId,
                            /// ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                            /// ref_id: parsedResponse?.refId,
                            user_id: order ? order?.user_id : buyRequest?.user_id,
                            status: AuthorizePaymentStatus.refundError,
                            authorize_payload: JSON.stringify(error)
                        },
                        null,
                        2
                    )}`
                );
                await models.apple_pay_trails.create({
                    token: '',
                    apple_pay_response: JSON.stringify({}),
                    buy_request_id: buyRequest?.id,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequest?.status,
                    order_id: order?.id,
                    amount: parseFloat(parseFloat(refundAmount).toFixed(2)),
                    /// trans_id: parsedResponse?.transactionResponse?.transId,
                    /// ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    /// ref_id: parsedResponse?.refId,
                    user_id: order ? order?.user_id : buyRequest?.user_id,
                    status: AuthorizePaymentStatus.refundError,
                    authorize_payload: JSON.stringify(error)
                });
            }

            throw new Error(`refund transaction error ${JSON.stringify(error)}`);
        }
    }

    /// get transaction details
    async getTransactionDetails(transactionId: string) {
        logger.info('Calling get transaction details!!!!');
        try {
            const requestData = {
                getTransactionDetailsRequest: {
                    merchantAuthentication: {
                        name: process.env.LOGIN_ID,
                        transactionKey: process.env.TRANSACTION_KEY
                    },
                    transId: transactionId
                }
            };

            /// post request
            const responseData: any = await axiosPost(requestData);

            return JSON.parse(JSON.stringify(responseData));
        } catch (error: any) {
            logger.error(`get transaction details error ${error}`);
            return {};
        }
    }

    /// create Apple pay transaction
    async createApplePayTransaction(
        buyRequestId: any,
        applePayResponse: any,
        userId: any,
        transaction: any,
        orderId?: any
    ) {
        logger.info('Calling create apple pay!!!');
        /// fetch user for customer_profile_id
        const user: any = await models.users.findOne({
            where: { id: userId },
            attributes: ['customer_profile_id'],
            transaction
        });

        if (!user) {
            throw new Error('user not found!!!');
        }

        if (!user.customer_profile_id) {
            throw new Error('customer profile id not found!!');
        }

        let buyRequest: any;
        let order: any;

        if (buyRequestId) {
            /// fetch buy request for stocks and addresses
            buyRequest = await models.buy_requests.findOne({ where: { id: buyRequestId }, transaction });

            if (!buyRequest) {
                throw new Error('Buy request not found');
            }
        } else if (orderId) {
            /// fetch order for line items
            order = await models.orders.findOne({ where: { id: orderId }, transaction });

            if (!order) {
                throw new Error('order not found');
            }
        }

        /// apple pay response
        const parsedApplePayResponse = JSON.parse(JSON.stringify(applePayResponse));
        const token = parsedApplePayResponse?.token;
        // const dataValue: any = parsedToken?.data;
        // const dataValue: any = btoa(JSON.stringify(parsedApplePayResponse.token));

        if (!token) {
            throw new Error(`token not found!!!`);
        }

        // convert this token into base64
        const dataValue = Buffer.from(token).toString('base64');

        if (!dataValue) {
            throw new Error(`dataValue not found!!!`);
        }

        try {
            /// line items
            let lineItems: any[] = [];

            if (buyRequest) {
                /// fetch stocks for stock details
                const stockIds = buyRequest.stock_ids
                    .filter((item: any) => item.is_available)
                    .map((stock: any) => stock.stock_id);

                const stocks = await models.stocks.findAll({
                    where: {
                        id: { [Op.in]: stockIds }
                    },
                    attributes: [
                        'stock_id',
                        'shape',
                        'color',
                        'clarity',
                        'cut',
                        'polish',
                        'status',
                        'admin_id',
                        'vendor_id',
                        'final_price',
                        'diamond_type',
                        'is_lab_grown',
                        'growth_type',
                        'sku_number'
                    ],
                    transaction
                });

                if (!stocks.length) {
                    throw new Error('stocks not found');
                }

                lineItems = stocks.map((stock: any, index: number) => ({
                    itemId: index,
                    name: stock.stock_id,
                    description: `${stock.shape}, ${stock.color}, ${stock.clarity}, ${stock.cut}, ${stock.polish}`,
                    quantity: 1,
                    unitPrice: parseFloat(stock.final_price).toFixed(2)
                }));
            }
            /// get line items for order
            else if (order) {
                if (order.jewellery_array) {
                    lineItems = order.jewellery_array.map((jewellery: any, index: number) => ({
                        itemId: index,
                        name: `variant_id:${jewellery.variant_id}`,
                        description: `product_id:${jewellery.product_id}`,
                        quantity: jewellery.quantity,
                        unitPrice: parseFloat(jewellery.total_amount).toFixed(2)
                    }));
                } else if (order.melee_array) {
                    lineItems = order.melee_array.map((melee: any, index: number) => ({
                        itemId: index,
                        name: `price_per_carat: ${melee.price_per_carat}`,
                        description: '',
                        quantity: melee.quantity,
                        unitPrice: parseFloat(melee.total_amount).toFixed(2)
                    }));
                }
            }

            /// fetch shipment for shipment details
            const shipment = await models.shipments.findOne({
                where: { id: order ? order.shipment_id : buyRequest.shipment_id },
                transaction
            });

            if (!shipment) {
                throw new Error('shipment not found!!!');
            }

            /// unique ref id
            const uniqueRefId = makeUniqueKey(10);

            /// inApp payment request
            const inAppPaymentRequestData = {
                createTransactionRequest: {
                    merchantAuthentication: {
                        name: process.env.LOGIN_ID,
                        transactionKey: process.env.TRANSACTION_KEY
                    },
                    refId: uniqueRefId, /// buy request code
                    transactionRequest: {
                        transactionType: AuthorizeTransactionType.authCaptureTransaction,
                        amount: order
                            ? parseFloat(order.grand_total).toFixed(2)
                            : parseFloat(buyRequest.grand_total).toFixed(2),
                        payment: {
                            opaqueData: {
                                dataDescriptor: 'COMMON.APPLE.INAPP.PAYMENT',
                                dataValue
                            }
                        },
                        lineItems: {
                            lineItem: lineItems
                        },
                        shipping: {
                            amount: parseFloat(shipment.amount).toFixed(2),
                            name: shipment.title,
                            description: shipment.description
                        },
                        customer: {
                            id: user.customer_profile_id
                        },
                        retail: {
                            marketType: '0'
                        },
                        // billTo: {
                        //     firstName: buyRequest.billing_address.first_name,
                        //     lastName: buyRequest.billing_address.last_name,
                        //     address: buyRequest.billing_address.street,
                        //     city: buyRequest.billing_address.city,
                        //     state: buyRequest.billing_address.state,
                        //     zip: buyRequest.billing_address.zip_code,
                        //     country: buyRequest.billing_address.country
                        // },
                        // shipTo: {
                        //     firstName: buyRequest.shipping_address.first_name,
                        //     lastName: buyRequest.shipping_address.last_name,
                        //     address: buyRequest.shipping_address.street,
                        //     city: buyRequest.shipping_address.city,
                        //     state: buyRequest.shipping_address.state,
                        //     zip: buyRequest.shipping_address.zip_code,
                        //     country: buyRequest.shipping_address.country
                        // },
                        userFields: {
                            userField: [
                                {
                                    name: 'MerchantDefinedFieldName1',
                                    value: 'MerchantDefinedFieldValue1'
                                },
                                {
                                    name: 'favorite_color',
                                    value: 'blue'
                                }
                            ]
                        }
                    }
                }
            };

            /// post request to authorize credit card details
            const responseData: any = await axiosPost(inAppPaymentRequestData);

            const parsedResponse = JSON.parse(JSON.stringify(responseData));

            // if (!responseData.transactionResponse.transId) {
            //     throw new Error('transId not found');
            // }

            // if (!responseData.transactionResponse.refTransID) {
            //     throw new Error('refTransID not found');
            // }

            if (buyRequest) {
                logger.info('Updating create apple pay details in buy request!!!');
                /// update buy requests
                await models.buy_requests.update(
                    {
                        trans_id: parsedResponse?.transactionResponse?.transId,
                        ref_id: parsedResponse?.refId,
                        ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                        authorize_transaction_payload: JSON.stringify(parsedResponse),
                        card_number: parsedResponse?.transactionResponse?.accountNumber,
                        card_type: parsedResponse.transactionResponse?.accountType,
                        authorized_amount: parseFloat(parseFloat(buyRequest.grand_total).toFixed(2))
                    },
                    { where: { id: buyRequest.id }, transaction }
                );
            } else if (order) {
                logger.info('Updating create apple pay details in orders!!!');
                /// update trans id orders
                await models.orders.update(
                    {
                        authorize_payment_details: {
                            trans_id: parsedResponse?.transactionResponse?.transId,
                            ref_id: parsedResponse?.refId,
                            ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                            authorize_transaction_payload: JSON.stringify(parsedResponse),
                            card_number: parsedResponse?.transactionResponse?.accountNumber,
                            card_type: parsedResponse.transactionResponse?.accountType,
                            authorized_amount: parseFloat(parseFloat(order?.grand_total).toFixed(2))
                        }
                    },
                    { where: { id: order.id }, transaction }
                );
            }

            logger.info('Create authorize payment trails for success');
            /// authorize payment trails
            await models.authorize_payment_trails.create(
                {
                    buy_request_id: buyRequest ? buyRequest?.id : null,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequest ? buyRequest?.status : null,
                    order_id: order ? order?.id : null,
                    amount: order
                        ? parseFloat(parseFloat(order?.grand_total).toFixed(2))
                        : parseFloat(parseFloat(buyRequest?.grand_total).toFixed(2)),
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    ref_id: parsedResponse?.refId,
                    user_id: order ? order.user_id : buyRequest?.user_id,
                    status: AuthorizePaymentStatus.createApplePay,
                    payload: JSON.stringify(parsedResponse)
                },
                { transaction }
            );

            logger.info('Create apple payment trails for success');
            /// apple payment trails
            await models.apple_pay_trails.create(
                {
                    token: JSON.stringify(dataValue),
                    apple_pay_response: JSON.stringify(applePayResponse),
                    buy_request_id: buyRequest?.id,
                    parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                    buy_request_status: buyRequest?.status,
                    order_id: order?.id,
                    amount: order
                        ? parseFloat(parseFloat(order?.grand_total).toFixed(2))
                        : parseFloat(parseFloat(buyRequest?.grand_total).toFixed(2)),
                    trans_id: parsedResponse?.transactionResponse?.transId,
                    ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                    ref_id: parsedResponse?.refId,
                    user_id: order ? order?.user_id : buyRequest?.user_id,
                    status: AuthorizePaymentStatus.createApplePay,
                    authorize_payload: JSON.stringify(parsedResponse)
                },
                { transaction }
            );

            ///
        } catch (error: any) {
            logger.info('Create authorize payment trails for error');
            logger.error(`create apple pay error ${JSON.stringify(error, null, 2)}`);

            /// authorize payment trails
            await models.authorize_payment_trails.create({
                buy_request_id: buyRequest ? buyRequest?.id : null,
                parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                buy_request_status: buyRequest ? buyRequest?.status : null,
                order_id: order ? order?.id : null,
                amount: order
                    ? parseFloat(parseFloat(order?.grand_total).toFixed(2))
                    : parseFloat(parseFloat(buyRequest?.grand_total).toFixed(2)),
                /// trans_id: parsedResponse?.transactionResponse?.transId,
                /// ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                /// ref_id: parsedResponse?.refId,
                user_id: order ? order?.user_id : buyRequest?.user_id,
                status: AuthorizePaymentStatus.createApplePayError,
                payload: JSON.stringify(error)
            });

            logger.info('Create apple payment trails for error');
            /// apple payment trails
            await models.apple_pay_trails.create({
                token: JSON.stringify(dataValue),
                apple_pay_response: JSON.stringify(applePayResponse),
                buy_request_id: buyRequest?.id,
                parent_buyrequest_id: buyRequest?.parent_buyrequest_id,
                buy_request_status: buyRequest?.status,
                order_id: order?.id,
                amount: order
                    ? parseFloat(parseFloat(order?.grand_total).toFixed(2))
                    : parseFloat(parseFloat(buyRequest?.grand_total).toFixed(2)),
                /// trans_id: parsedResponse?.transactionResponse?.transId,
                /// ref_trans_id: parsedResponse?.transactionResponse?.refTransID,
                /// ref_id: parsedResponse?.refId,
                user_id: buyRequest?.user_id,
                status: AuthorizePaymentStatus.createApplePayError,
                authorize_payload: JSON.stringify(error)
            });

            throw new Error(`create apple pay error ${JSON.stringify(error)}`);
        }
    }
}

export default new AuthorizePayment();
