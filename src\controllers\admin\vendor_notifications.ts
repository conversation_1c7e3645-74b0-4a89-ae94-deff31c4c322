import { NextFunction, Request, Response } from 'express';
import models from '../../models';
import { adminRole, httpStatusCodes } from '../../utils/constants';
import { logger } from '../../utils/logger';

class VendorNotification {
    /**
     * @api {get} /v1/auth/vendor/notification
     * @apiName VendorNotificationList
     * @apiGroup VendorNotification
     *
     *
     * @apiSuccess {Object} VendorNotification.
     */
    async getVendorNotification(req: Request, res: Response, next: NextFunction) {
        try {
            const vendor_id = req[`id`];
            const skip = req.query.skip;
            const limit = req.query.limit;
            const role = req[`role`];

            if (role !== adminRole.vendor) {
                throw new Error('unauthorized access!!!!');
            }

            const { rows, count } = await models.user_notifications.findAndCountAll({
                where: { vendor_id },
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/vendor/notification-unread-count
     * @apiName VendorNotificationUnreadCount
     * @apiGroup VendorNotification
     *
     *
     * @apiSuccess {Object} VendorNotification.
     */
    async getVendorNotificationUnreadCount(req: Request, res: Response, next: NextFunction) {
        try {
            const vendor_id = req[`id`];

            const count = await models.user_notifications.count({ where: { vendor_id, is_read: false } });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/vendor/notification-read
     * @apiName VendorNotificationUpdateRead
     * @apiGroup VendorNotification
     *
     *
     * @apiSuccess {Object} VendorNotification.
     */
    async markAsReadVendorNotification(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;
            const readAll = String(req.query.read_all).toLowerCase() === 'true';
            const reqId = req[`id`];

            const role = req[`role`];

            if (role !== adminRole.vendor) {
                throw new Error('unauthorized access!!!!');
            }

            if (readAll) {
                /// mark all notifications as read
                await models.user_notifications.update({ is_read: true }, { where: { vendor_id: reqId } });
            } else if (id) {
                /// mark as read
                const notification = await models.user_notifications.findOne({ where: { id } });

                if (!notification) {
                    throw new Error(`Notification not found!!`);
                }

                /// update is_read
                await models.user_notifications.update({ is_read: true }, { where: { id } });
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: 'Notification read successfully'
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new VendorNotification();
