import { IRouter, Router, Request, Response, NextFunction } from 'express';
import returnOrder from '../../../controllers/admin/return_order';
import {
    createdReturnOrderSchema,
    returnOrderActionSchema,
    returnOrderStatusSchema
} from '../../../utils/schema/return_order_schema';
import { fieldsValidator } from '../../../middlewares/validator';

const routes: IRouter = Router();

// return order status
routes.get('/return-order', returnOrder.listReturnOrder);

// return order status
routes.put(
    '/return-order-status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, returnOrderStatusSchema),
    returnOrder.returnOrderStatus
);

// create return order
routes.post(
    '/return-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createdReturnOrderSchema),
    returnOrder.createReturnOrder
);

// return order action
routes.put(
    '/return-order-action',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, returnOrderActionSchema),
    (req: Request, res: Response, next: NextFunction) => returnOrder.returnOrderAction(req, res, next)
);

export default routes;
