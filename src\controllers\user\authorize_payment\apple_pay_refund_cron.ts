import models, { sequelize } from '../../../models';
import { applePayRefundStatus, transactionStatus } from '../../../utils/constants';
import authorizePayment from './authorize_payment';
import { Op } from 'sequelize';
import { logger } from '../../../utils/logger';

class ApplePayRefundCron {
    /// apple pay refund cron
    async applePayRefundCron() {
        logger.info('Calling apple pay refund cron!!!');
        try {
            /// fetch pending transactions
            const { rows, count } = await models.apple_pay_refunds.findAndCountAll({
                where: {
                    refund_status: { [Op.in]: [applePayRefundStatus.pending, applePayRefundStatus.error] }
                }
            });

            for (const pendingTransaction of rows) {
                /// get transaction details
                const transactionDetails = await authorizePayment.getTransactionDetails(pendingTransaction?.trans_id);

                if (transactionDetails?.transaction?.transactionStatus === transactionStatus.settled) {
                    /// transaction
                    const transaction = await sequelize.transaction();
                    try {
                        let buyRequest: any;
                        let order: any;
                        /// fetch buy requests
                        if (pendingTransaction?.buy_request_id) {
                            buyRequest = await models.buy_requests.findOne({
                                where: { id: pendingTransaction?.buy_request_id }
                            });
                        }

                        /// fetch orders
                        if (pendingTransaction?.order_id) {
                            order = await models.orders.findOne({
                                where: { id: pendingTransaction?.order_id }
                            });
                        }

                        /// refund transaction
                        await authorizePayment.refundTransaction(
                            pendingTransaction?.refund_amount,
                            buyRequest,
                            transaction,
                            order,
                            true
                        );

                        /// update apple pay refund entry
                        await models.apple_pay_refunds.update(
                            {
                                refund_status: applePayRefundStatus.success,
                                transaction_details: JSON.stringify(transactionDetails)
                            },
                            { where: { id: pendingTransaction.id }, transaction }
                        );

                        /// commit
                        await transaction.commit();
                    } catch (error: any) {
                        logger.error(`apple pay cron loop item error ${error}`);

                        /// rollback
                        await transaction.rollback();
                        /// update apple pay refund entry
                        await models.apple_pay_refunds.update(
                            {
                                refund_status: applePayRefundStatus.error,
                                transaction_details: JSON.stringify(transactionDetails)
                            },
                            { where: { id: pendingTransaction.id } }
                        );
                    }
                }
            }
        } catch (error: any) {
            logger.error(`apple pay cron error ${error}`);
        }
    }
}

export default new ApplePayRefundCron();
