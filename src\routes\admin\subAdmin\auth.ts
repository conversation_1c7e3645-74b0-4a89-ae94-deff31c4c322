import { Router, IRouter, Request, Response, NextFunction } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    createSubAdminSchema,
    updateSubAdminSchema,
    activeInActiveSubAdminSchema
} from '../../../utils/schema/subAdmin_schema';

const router = Router();
import subAdmin from '../../../controllers/admin/subAdmin';

router.post(
    '/subAdmin',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createSubAdminSchema),
    subAdmin.subAdminCreate
);

router.put(
    '/subAdmin',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateSubAdminSchema),
    subAdmin.updateSubAdmin
);

router.delete('/subAdmin', subAdmin.deleteSubAdmin);

router.put(
    '/subAdmin/updateStatus',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, activeInActiveSubAdminSchema),
    subAdmin.activeInActiveSubAdmin
);

router.get('/subAdmin', subAdmin.getAllSubAdmin);

export default router;
