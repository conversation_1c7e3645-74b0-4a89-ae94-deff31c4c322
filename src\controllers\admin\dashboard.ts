import { NextFunction, Request, Response } from 'express';
import models, { sequelize } from '../../models';

import {
    adminRole,
    buyRequestStatus,
    httpStatusCodes,
    OfferStatus,
    orderStatus,
    ReturnOrderStatus,
    unifiedOrderType,
    vendorOrderStatus
} from '../../utils/constants';
import { Op, Sequelize } from 'sequelize';
import { logger } from '../../utils/logger';

class Dashboard {
    // constructor() { }

    /*
        --------------------------------------------------------------------------------
        Dashboard functions
    */

    /**
     *  @api {post} /v1/auth/admin/kpi
     *  @apiName admin kpi
     *  @apiGroup Admin
     *
     *  @apiSuccess {Object} Admin
     */

    async listCounts(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req[`role`];
            const id = req[`id`];

            const resultData: any = {};

            /// for admins
            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                const admin_stock_count = await models.stocks.count({
                    where: {
                        admin_id:
                            role === adminRole.superAdmin
                                ? id
                                : {
                                    [Op.not]: null
                                }
                    }
                });

                /// admins stock count
                resultData.admin_stock_count = admin_stock_count;

                /// stock status wise count
                const stock_status_count = await models.stocks.count({
                    where: {
                        admin_id:
                            role === adminRole.superAdmin
                                ? id
                                : {
                                    [Op.not]: null
                                }
                    },
                    group: [
                        sequelize.literal(
                            `CASE WHEN status IN ('HOLD', 'ON HOLD') THEN 'HOLD' WHEN status IN ('MEMO', 'ON MEMO') THEN 'MEMO' ELSE status END`
                        )
                    ],
                    raw: true,
                    attributes: [
                        [
                            sequelize.literal(
                                `CASE WHEN status IN ('HOLD', 'ON HOLD') THEN 'HOLD' WHEN status IN ('MEMO', 'ON MEMO') THEN 'MEMO' ELSE status END`
                            ),
                            'status'
                        ],
                        [sequelize.fn('COUNT', sequelize.col('status')), 'count']
                    ]
                });

                /// admins stock status count
                resultData.stock_status_count = stock_status_count;

                /// buy request count
                const buy_request_count = await models.buy_requests.count({ include: [{ model: models.users }] });

                resultData.buy_request_count = buy_request_count;

                /// order count
                const order_count = await models.orders.count({ include: [{ model: models.users }] });

                resultData.order_count = order_count;

                /// jewellery order count
                const jewellery_order_count = await models.orders.count({
                    where: { unified_order_type: unifiedOrderType.JEWELLERY },
                    include: [{ model: models.users }]
                });

                resultData.jewellery_order_count = jewellery_order_count;

                /// melle order count
                const melle_order_count = await models.orders.count({
                    where: { unified_order_type: unifiedOrderType.MELEE },
                    include: [{ model: models.users }]
                });

                resultData.melle_order_count = melle_order_count;

                /// vendor order count
                const vendor_orders_count = await models.vendor_orders.count({
                    include: [
                        { model: models.orders, include: [{ model: models.users }] },
                        { model: models.vendors, where: { is_blacklisted: false } }
                    ]
                });

                resultData.vendor_orders_count = vendor_orders_count;

                /// return order count
                const return_orders_count = await models.return_orders.count({ include: [{ model: models.users }] });

                resultData.return_orders_count = return_orders_count;

                /// stock offer count
                const stock_offers_count = await models.stock_offers.count({
                    where: {
                        admin_id: {
                            [Op.not]: null
                        }
                    },
                    include: [{ model: models.users }]
                });

                resultData.stock_offers_count = stock_offers_count;

                /// stock offer status wise count
                const stock_offer_status_count = await models.stock_offers.count({
                    where: {
                        admin_id: {
                            [Op.not]: null
                        }
                    },
                    include: [{ model: models.users }],
                    group: 'status',
                    attributes: ['status']
                });

                resultData.stock_offer_status_count = stock_offer_status_count;

                /// return order status wise count
                const return_order_status_count = await models.return_orders.count({
                    group: 'status',
                    attributes: ['status'],
                    include: [{ model: models.users }]
                });

                resultData.return_order_status_count = return_order_status_count;

                /// buy request status wise count
                const buy_request_status_count = await models.buy_requests.count({
                    group: 'status',
                    attributes: ['status'],
                    include: [{ model: models.users }]
                });

                resultData.buy_request = buy_request_status_count;

                /// buy request margin counts
                const buy_request_margin_count = await models.buy_requests.findAll({
                    where: { status: buyRequestStatus.pending },
                    attributes: [
                        [Sequelize.literal(`CASE WHEN is_margin_approved = true THEN 'Margin Approved' ELSE 'Margin Pending' END`), 'status'],
                        [Sequelize.fn('COUNT', Sequelize.col('*')), 'count'],
                    ],
                    group: [Sequelize.literal(`CASE WHEN is_margin_approved = true THEN 'Margin Approved' ELSE 'Margin Pending' END`)],
                    raw: true,
                });

                resultData.buy_request_margin_count = buy_request_margin_count;

                /// order status wise count
                const order_status_count = await models.orders.count({
                    group: 'order_status',
                    attributes: ['order_status'],
                    include: [{ model: models.users }]
                });

                resultData.order = order_status_count;

                /// jewellery order status wise count
                const jewellery_order_status_count = await models.orders.count({
                    where: { unified_order_type: unifiedOrderType.JEWELLERY },
                    group: 'order_status',
                    attributes: ['order_status'],
                    include: [{ model: models.users }]
                });

                resultData.jewellery_order = jewellery_order_status_count;

                /// melle order status wise count
                const melle_order_status_count = await models.orders.count({
                    where: { unified_order_type: unifiedOrderType.MELEE },
                    group: 'order_status',
                    attributes: ['order_status'],
                    include: [{ model: models.users }]
                });

                resultData.melle_order = melle_order_status_count;

                /// vendor order status wise count
                const vendor_order_status_count = await models.vendor_orders.count({
                    group: 'status',
                    attributes: ['status'],
                    include: [
                        { model: models.orders, include: [{ model: models.users }] },
                        { model: models.vendors, where: { is_blacklisted: false } }
                    ]
                });

                resultData.vendor_order = vendor_order_status_count;
            }

            /// for vendors
            if (adminRole.vendor === role) {
                /// buy request count
                const buy_request_count = await models.buy_requests.count({
                    where: { vendor_ids: { [Op.contains]: [id] } },
                    include: [{ model: models.users }]
                });

                resultData.buy_request_count = buy_request_count;

                /// vendor orders count
                const vendor_orders_count = await models.vendor_orders.count({
                    where: { vendor_id: id },
                    include: [
                        { model: models.orders, include: [{ model: models.users }] },
                        { model: models.vendors, where: { is_blacklisted: false } }
                    ]
                });

                resultData.vendor_orders_count = vendor_orders_count;

                /// return order counts
                const return_orders_count = await models.return_orders.count({
                    where: { vendor_id: id },
                    include: [{ model: models.users }]
                });

                resultData.return_orders_count = return_orders_count;

                /// stock offers count
                const stock_offers_count = await models.stock_offers.count({
                    where: { vendor_id: id },
                    include: [{ model: models.users }]
                });

                resultData.stock_offers_count = stock_offers_count;

                /// stock offer status wise count
                const stock_offer_status_count = await models.stock_offers.count({
                    where: { vendor_id: id },
                    include: [{ model: models.users }],
                    group: 'status',
                    attributes: ['status']
                });

                resultData.stock_offer_status_count = stock_offer_status_count;

                /// return order status wise count
                const return_order_status_count = await models.return_orders.count({
                    where: { vendor_id: id },
                    group: 'status',
                    attributes: ['status'],
                    include: [{ model: models.users }]
                });

                resultData.return_order_status_count = return_order_status_count;

                /// buy requests count
                const buy_request_status_count = await models.buy_requests.count({
                    where: { vendor_ids: { [Op.contains]: [id] } },
                    include: [{ model: models.users }],
                    group: 'status',
                    attributes: ['status']
                });

                resultData.buy_request = buy_request_status_count;

                /// vendor order status wise count
                const vendor_orders_status_count = await models.vendor_orders.count({
                    where: { vendor_id: id },
                    group: 'status',
                    attributes: ['status'],
                    include: [
                        { model: models.orders, include: [{ model: models.users }] },
                        { model: models.vendors, where: { is_blacklisted: false } }
                    ]
                });

                resultData.vendor_orders = vendor_orders_status_count;
            }

            /// vendors or all vendors stock count
            const vendors_stock_count = await models.stocks.count({
                where: {
                    vendor_id: role === adminRole.vendor ? id : { [Op.ne]: null }
                }
            });

            /// vendors stock status wise count
            const vendors_stock_status_count = await models.stocks.count({
                attributes: [
                    [
                        sequelize.literal(
                            `CASE WHEN status IN ('MEMO', 'ON MEMO') THEN 'MEMO' ELSE status END`
                        ),
                        'status'
                    ],
                    [sequelize.fn('COUNT', sequelize.col('status')), 'count']
                ],
                where: {
                    vendor_id: role === adminRole.vendor ? id : { [Op.ne]: null }
                },
                group: [
                    sequelize.literal(
                        `CASE WHEN status IN ('MEMO', 'ON MEMO') THEN 'MEMO' ELSE status END`
                    )
                ],
                raw: true
            });

            /// vendors stock status count
            resultData.vendors_stock_status_count = vendors_stock_status_count;

            /// users count
            const users_count = await models.users.count();

            /// vendors count
            const vendors_count = await models.vendors.count();

            /// sub admins count
            let subAdmin_count = await models.admins.count();

            subAdmin_count = subAdmin_count - 1;

            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                resultData.users_count = users_count;
                resultData.vendors_count = vendors_count;
                resultData.subAdmin_count = subAdmin_count;
                resultData.vendors_stock_count = vendors_stock_count;
                resultData.vendors_stock_status_count = vendors_stock_status_count;
            } else if (role === adminRole.vendor) {
                resultData.vendors_stock_count = vendors_stock_count;
                resultData.vendors_stock_status_count = vendors_stock_status_count;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'successfully listed counts',
                data: resultData
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/pending-sections
     *  @apiName admin kpi
     *  @apiGroup Admin
     *
     *  @apiSuccess {Object} Admin
     */

    async listPendingSections(req: Request, res: Response, next: NextFunction) {
        try {
            const role = req[`role`];
            const id = req[`id`];

            const resultData: any = {};

            /// for admins
            if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                /// buy request count
                const buy_request_count = await models.buy_requests.count({
                    where: { status: buyRequestStatus.pending },
                    include: [{ model: models.users }]
                });

                resultData.buy_requests = buy_request_count > 0;

                /// order count
                const order_count = await models.orders.count({
                    where: { order_status: orderStatus.pending },
                    include: [{ model: models.users }]
                });

                resultData.orders = order_count > 0;

                /// jewellery order count
                const jewellery_order_count = await models.orders.count({
                    where: { order_status: orderStatus.pending, unified_order_type: unifiedOrderType.JEWELLERY },
                    include: [{ model: models.users }]
                });

                resultData.jewellery_orders = jewellery_order_count > 0;

                /// melle order count
                const melle_order_count = await models.orders.count({
                    where: { order_status: orderStatus.pending, unified_order_type: unifiedOrderType.MELEE },
                    include: [{ model: models.users }]
                });

                resultData.melle_orders = melle_order_count > 0;

                /// vendor order count
                const vendor_orders_count = await models.vendor_orders.count({
                    where: { status: vendorOrderStatus.pending },
                    include: [
                        { model: models.orders, include: [{ model: models.users }] },
                        { model: models.vendors, where: { is_blacklisted: false } }
                    ]
                });

                resultData.vendor_orders = vendor_orders_count > 0;

                /// return order count
                const return_orders_count = await models.return_orders.count({
                    where: { status: ReturnOrderStatus.pending },
                    include: [{ model: models.users }]
                });

                resultData.return_orders = return_orders_count > 0;

                /// stock offer count
                const stock_offers_count = await models.stock_offers.count({
                    where: {
                        status: OfferStatus.pending,
                        admin_id: {
                            [Op.not]: null
                        }
                    },
                    include: [{ model: models.users }]
                });

                resultData.stock_offers = stock_offers_count > 0;
            }

            /// for vendors
            if (adminRole.vendor === role) {
                /// buy request count
                const buy_request_count = await models.buy_requests.count({
                    where: { status: buyRequestStatus.pending, vendor_ids: { [Op.contains]: [id] } },
                    include: [{ model: models.users }]
                });

                resultData.buy_requests = buy_request_count > 0;

                /// vendor orders count
                const vendor_orders_count = await models.vendor_orders.count({
                    where: { status: vendorOrderStatus.pending, vendor_id: id },
                    include: [
                        { model: models.orders, include: [{ model: models.users }] },
                        { model: models.vendors, where: { is_blacklisted: false } }
                    ]
                });

                resultData.vendor_orders = vendor_orders_count > 0;

                /// return order counts
                const return_orders_count = await models.return_orders.count({
                    where: { status: ReturnOrderStatus.pending, vendor_id: id },
                    include: [{ model: models.users }]
                });

                resultData.return_orders = return_orders_count > 0;

                /// stock offers count
                const stock_offers_count = await models.stock_offers.count({
                    where: { status: OfferStatus.pending, vendor_id: id },
                    include: [{ model: models.users }]
                });

                resultData.stock_offers = stock_offers_count > 0;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'successfully listed pending sections',
                data: resultData
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Dashboard();
