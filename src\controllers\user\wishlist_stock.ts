import { NextFunction, Request, Response } from 'express';
import models from '../../models';
import { httpStatusCodes } from '../../utils/constants';
import { WishlistStockAttributes } from '../../models/wishlist_stocks';
import { Op } from 'sequelize';
import { logger } from '../../utils/logger';

class WishlistStock {
    /**
     * @api {get} /v1/auth/user/wishlist
     * @apiName WishlistStocksList
     * @apiGroup WishlistStock
     *
     *
     * @apiSuccess {Object} UserNotification.
     */
    async getWishlistStocks(req: Request, res: Response, next: NextFunction) {
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const user_id = req[`id`];
            const { rows, count } = await models.wishlist_stocks.findAndCountAll({
                where: { user_id },
                order: [['createdAt', 'DESC']],
                // offset: skip,
                // limit
            });
            const wishlistStocks: any = rows.map((stock: any) => ({
                stock_id: stock.stock_id,
                vendor_id: stock.vendor_id,
                admin_id: stock.admin_id
            }));

            const stocksList: any[] = [];
            let countOfStocks = 0;
            let addedStocks = 0;

            const allStocks = await models.stocks.findAll({
                where: { stock_id: { [Op.in]: wishlistStocks.map((stock: any) => stock.stock_id) } },
            })

            for (const wishlistStock of wishlistStocks) {
                // check for skip and limit
                if (limit && addedStocks >= parseInt(String(limit), 10)) {
                    break;
                }
            
                const stock = allStocks.find((stockItem: any) => stockItem.stock_id === wishlistStock.stock_id);
                
                if (stock) {
                    if (skip && countOfStocks < parseInt(String(skip), 10)) {
                        countOfStocks++;
                        continue;
                    }

                    countOfStocks++;
                    addedStocks++;
                    stocksList.push(stock);
                }
            }

            stocksList.forEach((stock) => {
                stock.is_wishlist = true;
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: stocksList,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/wishlist
     * @apiName WishlistStock
     * @apiGroup WishlistStock
     *
     *
     * @apiSuccess {Object} WishlistStock
     */
    async addToWishlistStocks(req: Request, res: Response, next: NextFunction) {
        try {
            const user_id = req[`id`];
            const { stock_id } = req.body;

            if (stock_id) {
                const stock: any = await models.stocks.findOne({
                    where: { id: stock_id },
                    attributes: ['stock_id', 'status', 'final_price', 'vendor_id', 'admin_id']
                });

                const isSaved: WishlistStockAttributes = await models.wishlist_stocks.findOne({
                    where: {
                        [Op.and]: [
                            { user_id },
                            { stock_id: stock.stock_id },
                            stock.vendor_id ? { vendor_id: stock.vendor_id } : {},
                            stock.admin_id ? { admin_id: stock.admin_id } : {}
                        ]
                    }
                });

                if (!isSaved) {
                    await models.wishlist_stocks.create({
                        user_id,
                        stock_id: stock.stock_id,
                        status: stock.status,
                        final_price: stock.final_price,
                        vendor_id: stock.vendor_id,
                        admin_id: stock.admin_id
                    });
                    res.send({
                        status: httpStatusCodes.SUCCESS_CODE,
                        message: 'Stock added to wishlist'
                    });
                } else {
                    await models.wishlist_stocks.destroy({
                        where: {
                            [Op.and]: [
                                { user_id },
                                { stock_id: stock.stock_id },
                                stock.vendor_id ? { vendor_id: stock.vendor_id } : {},
                                stock.admin_id ? { admin_id: stock.admin_id } : {}
                            ]
                        }
                    });

                    res.send({
                        status: httpStatusCodes.SUCCESS_CODE,
                        message: 'Stock removed from wishlist'
                    });
                }
            }
            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new WishlistStock();
