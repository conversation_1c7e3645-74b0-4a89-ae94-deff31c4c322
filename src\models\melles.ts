import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface MelleAttributes {
    id?: string;
    shape: string;
    growth_type: string;
    color: string;
    clarity: string;
    sieve_size: string;
    milimeter: string;
    pointer: string;
    price_per_caret: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface MelleCreationAttributes extends Optional<MelleAttributes, 'id'> {}

interface MelleInstance extends Model<MelleAttributes, MelleCreationAttributes>, MelleAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type MelleStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => MelleInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const melles = sequelize.define<MelleInstance>(
        'melles',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            shape: {
                type: DataTypes.STRING,
                allowNull: true
            },
            growth_type: {
                type: DataTypes.STRING,
                allowNull: true
            },
            color: {
                type: DataTypes.STRING,
                allowNull: true
            },
            clarity: {
                type: DataTypes.STRING,
                allowNull: true
            },
            sieve_size: {
                type: DataTypes.STRING,
                allowNull: true
            },
            milimeter: {
                type: DataTypes.STRING,
                allowNull: true
            },
            pointer: {
                type: DataTypes.STRING,
                allowNull: true
            },
            price_per_caret: {
                type: DataTypes.DOUBLE,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as MelleStatic;

    // melles.associate = (models) => {
    //     melle.belongsTo(models.users, {
    //         foreignKey: 'user_id',
    //         onDelete: 'CASCADE',
    //         onUpdate: 'CASCADE'
    //     });

    // };

    // TODO: make common function to sync
    // await melles.sync({ alter: true });

    return melles;
};
