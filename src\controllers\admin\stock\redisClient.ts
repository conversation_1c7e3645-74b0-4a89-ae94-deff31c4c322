import { createClient, RedisClientType } from 'redis';
import { logger } from '../../../utils/logger';

export class RedisClient {
    private url: string;
    private password: string;
    private dbIndex: number;
    private client: RedisClientType | null;

    constructor() {
        this.url = 'redis://95.111.243.36:6379';
        this.password = 'KXA4i38jp4YbXdlE6giLPaLmVckIucQH13XTW2TAcTC1a1wGPilzWqxxMf1nzPJtjW+cq6yGbL2D39m4';
        this.dbIndex = 1;
        this.client = null;
    }

    // Initialize the connection
    async connect(): Promise<RedisClientType> {
        if (this.client) {
            return this.client;
        } // Avoid reconnecting if already connected

        this.client = createClient({
            url: this.url,
            password: this.password,
            database: this.dbIndex
        });

        // Handle connection events
        this.client.on('error', (err) => {
            logger.error('Redis Client Error:', err);
        });

        await this.client.connect();
        logger.info('Connected to Redis');

        return this.client;
    }

    // Setter method to store a key-value pair
    async set(key: string, value: string, expiration: number | null = null): Promise<void> {
        try {
            const client = await this.connect();
            await client.set(key, value);
            if (expiration) {
                await client.expire(key, expiration);
            }
            logger.info(`Key "${key}" set successfully.`);
        } catch (err) {
            logger.error('Error setting key:', err);
        }
    }

    // Getter method to retrieve a value by key
    async get(key: string): Promise<string | null> {
        try {
            const client = await this.connect();
            const value = await client.get(key);
            return value;
        } catch (err) {
            logger.error('Error getting key:', err);
            return null;
        }
    }

    // Close the connection when done
    async disconnect(): Promise<void> {
        if (this.client) {
            await this.client.quit();
            logger.info('Disconnected from Redis');
            this.client = null;
        }
    }
}
