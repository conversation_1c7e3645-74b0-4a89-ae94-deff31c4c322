import { NextFunction, Response, Request } from 'express';
import { logger } from '../../utils/logger';
import models, { sequelize } from '../../models';
import {
    adminRole,
    buyRequestStatus,
    buyRequestType,
    calculateFinancialYear,
    getInvoiceNumber,
    httpStatusCodes,
    makeUniqueKey,
    NotificationType,
    OfferActionType,
    OfferStatus,
    PaymentMode,
    paymentType,
    stockStatus,
    unifiedOrderType,
    vendorOrderStatus
} from '../../utils/constants';
import { Op, Sequelize } from 'sequelize';
import authorizePayment from '../user/authorize_payment/authorize_payment';
import userNotification from '../user/user_notifications/user_notification';

class StockOffers {
    /**
     * @api {post} /v1/auth/user/stock-offer
     * @apiName CreateStockOffer
     * @apiGroup StockOffer
     *
     *
     * @apiSuccess {Object} StockOffer.
     */
    async createStockOffer(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling create stock offer');
        const transaction = await sequelize.transaction();
        try {
            const userId = req[`id`];

            const { stock_id, offer_price } = req.body;

            if (offer_price <= 0) {
                throw new Error(`Offer price cannot be zero!!!`);
            }

            const offer: any = await models.stock_offers.findOne({ where: { stock_id, user_id: userId }, transaction });

            if (offer && [OfferStatus.pending, OfferStatus.revised].includes(offer.status)) {
                throw new Error('Offer already created for this stock');
            }

            const user: any = await models.users.findOne({ where: { id: userId }, transaction });

            if (!user) {
                throw new Error(`User not found`);
            }

            if (user.is_blocked || !user.is_active) {
                throw new Error(`User is blocked, please contact administrator.!!!`);
            }

            if (!user.is_verified) {
                throw new Error(`User is not verified!!!`);
            }

            const stock: any = await models.stocks.findOne({ where: { id: stock_id }, transaction });

            if (!stock) {
                throw new Error('Stock not found!!');
            }

            if (stock.status !== 'AVAILABLE') {
                throw new Error('Stock not available!!');
            }

            const minOfferPrice = parseFloat(stock.final_price) - parseFloat(stock.final_price) * 0.1; // 10% of stock price

            if (
                parseFloat(parseFloat(offer_price).toFixed(2)) >= parseFloat(parseFloat(stock.final_price).toFixed(2))
            ) {
                throw new Error(`Offer price must be less than stock price!`);
            } else if (parseFloat(parseFloat(offer_price).toFixed(2)) < parseFloat(minOfferPrice.toFixed(2))) {
                throw new Error(`Offer price must be greater than minimum offer price \$${minOfferPrice.toFixed(2)}!`);
            }

            const createOfferObject: any = {
                user_id: userId,
                stock_id,
                admin_id: stock.admin_id,
                vendor_id: stock.vendor_id,
                offer_price: parseFloat(parseFloat(offer_price).toFixed(2)),
                updated_offer_price: parseFloat(parseFloat(offer_price).toFixed(2)),
                last_offer_price: parseFloat(parseFloat(offer_price).toFixed(2)),
                updated_last_offer_price: parseFloat(parseFloat(offer_price).toFixed(2)),
                // buy_request_id: string;
                // order_id: string;
                base_price: stock.final_price,
                base_price_vendor: stock.final_price_ori,
                // vendor_margin: stock.vendor_margin,
                status: OfferStatus.pending, // PENDING | ACCEPTED | REJECTED
                last_offer_id: userId,
                last_action_id: userId,
                last_action_type: OfferActionType.user // USER | VENDOR | ADMIN
            };

            /// calculate updated offer price for vendor
            if (stock.vendor_id) {
                logger.info('calculating price for vendor stock including margin!!!');
                /// fetch vendor margin
                // const vendor: any = await models.vendors.findOne({
                //     where: { id: stock.vendor_id },
                //     attributes: ['margin_percentage']
                // });

                /// apply margin to offer price
                const marginedPrice: number =
                    parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(stock?.stock_margin) / 100);

                /// subtract margin from offer price
                createOfferObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2)) - marginedPrice;
                createOfferObject.vendor_margin = stock?.stock_margin;
                createOfferObject.updated_last_offer_price =
                    parseFloat(parseFloat(offer_price).toFixed(2)) - marginedPrice;
            }

            logger.info('creating stock offer entry');

            const resultOffer: any = await models.stock_offers.create(createOfferObject, { transaction });

            logger.info('creating stock offer trails');

            await models.stock_offer_trails.create(
                {
                    ...(({ id, createdAt, updatedAt, ...rest }) => rest)(JSON.parse(JSON.stringify(resultOffer))),
                    offer_id: resultOffer.id,
                    payload: JSON.stringify(resultOffer)
                },
                { transaction }
            );

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `offer created successfully`,
                data: resultOffer
            });

            try {
                /// notify user for offer created
                userNotification.sendOfferAcceptedRejectedNotification(NotificationType.offerCreated, resultOffer.id);
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/user/update-offer
     * @apiName GetStockOffers
     * @apiGroup StockOffer
     *
     *
     * @apiSuccess {Object} StockOffer.
     */
    async updateOfferOld(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update stock offer');

        const transaction = await sequelize.transaction();

        try {
            const id = req[`id`];
            const role = req[`role`];
            const conditions: any[] = [];

            const { offer_price, offer_id, status } = req.body;

            const updateObject: any = {};

            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                conditions.push({ admin_id: id });
            } else if (adminRole.vendor === role) {
                conditions.push({ vendor_id: id });
            }

            if (offer_price <= 0) {
                throw new Error(`Offer price cannot be zero!!!`);
            }

            conditions.push({ id: offer_id });

            const offer: any = await models.stock_offers.findOne({ where: { [Op.and]: conditions } });

            if (!offer) {
                throw new Error('Offer not found!!');
            }

            if (role === adminRole.user) {
                if (id !== offer?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            if (offer?.last_action_id === id) {
                throw new Error('Offer already submitted!!');
            }

            const stock: any = await models.stocks.findOne({ where: { id: offer?.stock_id }, transaction });

            if (!stock) {
                throw new Error('Stock not found!!');
            }

            if (stock.status !== 'AVAILABLE') {
                throw new Error('Stock sold out!!');
            }

            /// counter offer limit
            if ([OfferStatus.accepted, OfferStatus.rejected].includes(offer.status)) {
                throw new Error('Offer already accepted or rejected!!');
                // if (role === adminRole.user && [OfferStatus.rejected].includes(offer.status)) {
                //     logger.info('Checking rejected offer limit');
                //     /// check three rejected offers
                //     const rejectedOffersCount = await models.stock_offer_trails.count({
                //         where: {
                //             offer_id: offer?.id,
                //             status: OfferStatus.rejected
                //         },
                //         transaction
                //     });

                //     if (rejectedOffersCount >= 3) {
                //         throw new Error('Offer limit exceeded!!');
                //     }
                // } else {
                //     throw new Error('Offer already accepted or rejected!!');
                // }
            }

            /// update offer pending
            if ([OfferStatus.pending, OfferStatus.revised].includes(status)) {
                // offer_price = user side value
                // updated_offer_price = vendor side value
                // base_price = user side value
                // base_price_vendor = vendor side value
                // last_offer_price = user side value
                // updated_last_offer_price = vendor side value

                /// check base_price_vendor for vendor and base_price for  use and admin
                if (role === adminRole.vendor) {
                    if (
                        parseFloat(parseFloat(offer_price).toFixed(2)) >=
                        parseFloat(parseFloat(offer.base_price_vendor).toFixed(2))
                    ) {
                        throw new Error('Offer price must be less than diamond price!!');
                    }
                } else {
                    if (
                        parseFloat(parseFloat(offer_price).toFixed(2)) >=
                        parseFloat(parseFloat(offer.base_price).toFixed(2))
                    ) {
                        throw new Error('Offer price must be less than diamond price!!');
                    }
                }

                if (role === adminRole.user) {
                    const countOfPreviousOffers = await models.stock_offer_trails.count({
                        where: { user_id: offer.user_id, offer_id: offer.id, last_offer_id: id },
                        transaction
                    });

                    if (countOfPreviousOffers >= 3) {
                        throw new Error('!!!!!!!!!!Offer limit exceeded!!!!!!!!!!!!!!');
                    }
                }

                /// offer price
                logger.info('updating offer price when status is pending or revised!!');
                logger.info('Checking previous offer price!!!');
                /// offer can not be less than previous offer
                const previousOffer = await models.stock_offer_trails.findOne({
                    where: { user_id: offer?.user_id, offer_id: offer?.id, last_offer_id: id },
                    order: [['createdAt', 'DESC']],
                    transaction
                });

                logger.info(`!!!!!role!!!!!!! ${role}`);
                logger.info(`!!!!!offer!!!!!!! ${JSON.stringify(offer, null, 2)}`);
                logger.info(`!!!!offer_price!!! ${offer_price}`);
                logger.info(`!!!!!Previous Offer ${JSON.stringify(previousOffer, null, 2)}`);

                let minPrice;
                let maxPrice;

                if (role === adminRole.vendor) {
                    if (previousOffer) {
                        minPrice = offer?.updated_offer_price;
                        maxPrice = previousOffer?.updated_offer_price;
                    } else {
                        minPrice = offer?.updated_offer_price;
                        maxPrice = offer?.base_price_vendor;
                    }

                    // this case check if vendor offer the price but after margin if offer is still less than the user offer it should not be allowed
                    const marginedPrice: number =
                        parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(offer.vendor_margin) / 100);
                    const user_offer_price = parseFloat(parseFloat(offer_price).toFixed(2)) + marginedPrice;

                    if (user_offer_price < offer?.offer_price) {
                        throw new Error(`Offer price must be greater than previous offer!!`);
                    }
                } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    if (previousOffer) {
                        minPrice = offer?.offer_price;
                        maxPrice = previousOffer?.offer_price;
                    } else {
                        minPrice = offer?.offer_price;
                        maxPrice = offer?.base_price;
                    }
                } else if (role === adminRole.user) {
                    if (previousOffer) {
                        minPrice = previousOffer?.offer_price;
                        maxPrice = offer?.offer_price;
                    } else {
                        minPrice = offer?.offer_price;
                        maxPrice = offer?.base_price;
                    }
                }

                logger.info(`!!!!minPrice!!! ${minPrice}`);
                logger.info(`!!!!maxPrice!!! ${maxPrice}`);

                if (minPrice > maxPrice) {
                    logger.info('!!!!!!!minPrice is greater than maxPrice!!!!!!!!!');
                    if (role === adminRole.user) {
                        const previousVendorAdminOffer = await models.stock_offer_trails.findAll({
                            where: { user_id: offer?.user_id, offer_id: offer?.id, last_offer_id: { [Op.ne]: id } },
                            order: [['createdAt', 'DESC']],
                            transaction
                        });

                        // calculate maxPrice from this list of offers
                        maxPrice = previousVendorAdminOffer.reduce((acc: number, item: any) => {
                            return acc < parseFloat(item.offer_price) ? parseFloat(item.offer_price) : acc;
                        }, 0);
                    }
                }

                logger.info(`!!!!minPrice!!! ${minPrice}`);
                logger.info(`!!!!maxPrice!!! ${maxPrice}`);

                // check min Price and Max Price
                if (
                    parseFloat(parseFloat(offer_price).toFixed(2)) <= parseFloat(parseFloat(minPrice).toFixed(2)) ||
                    parseFloat(parseFloat(offer_price).toFixed(2)) >= parseFloat(parseFloat(maxPrice).toFixed(2))
                ) {
                    throw new Error(`Offer price must be between ${minPrice} and ${maxPrice}!!`);
                }

                // if (role === adminRole.vendor) {
                //     /// vendors offer price should be greater than his own previous offer
                //     if (previousOffer) {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //             parseFloat(parseFloat(previousOffer?.updated_offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be less than previous offer!!`);
                //         }
                //     }
                //     /// first offer must be greater than buyer offer
                //     else {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) <=
                //             parseFloat(parseFloat(offer?.updated_offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be greater than buyer offer!!`);
                //         }
                //     }
                // } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                //     /// user and admin offer price should be greater than his own previous offer
                //     if (previousOffer) {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //             parseFloat(parseFloat(previousOffer?.offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be less than previous offer!!`);
                //         }
                //     }
                //     /// first offer must be greater than buyer offer
                //     else {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) <=
                //             parseFloat(parseFloat(offer?.offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be greater than buyer offer!!`);
                //         }
                //     }
                // } else if (role === adminRole.user) {
                //     /// users offer will greater than his previous offer
                //     if (previousOffer) {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) <=
                //             parseFloat(parseFloat(previousOffer?.offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be greater than previous offer!!`);
                //         }
                //     }

                //     /// users offer price should be less vendors last offer price
                //     /// and skip checking vendors last offer on counter offer

                //     // Commented as we are not allowing counter offer once the offer is rejected
                //     // if (
                //     //     parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //     //         parseFloat(parseFloat(offer?.offer_price).toFixed(2)) &&
                //     //     offer.status !== OfferStatus.rejected
                //     // ) {
                //     //     throw new Error(`Offer price must be less than previous offer!!`);
                //     // }
                //     if (
                //         parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //             parseFloat(parseFloat(offer?.offer_price).toFixed(2))
                //     ) {
                //         throw new Error(`Offer price must be less than previous offer!!`);
                //     }
                // }

                updateObject.last_offer_id = id;
                updateObject.last_offer_price = offer.offer_price;
                updateObject.updated_last_offer_price = offer.updated_offer_price;

                logger.info('Calculating prices for offer');
                if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    /// calculate updated offer price for vendor
                    updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    /// subtract margin from offer price
                    updateObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    /// response from user
                    updateObject.is_response_required = true;
                } else if (adminRole.user === role) {
                    /// response from user
                    updateObject.is_response_required = false;
                    if (offer.admin_id) {
                        /// calculate updated offer price for vendor
                        updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                        /// subtract margin from offer price
                        updateObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    } else if (offer.vendor_id) {
                        /// apply margin to offer price
                        const marginedPrice: number =
                            parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(offer.vendor_margin) / 100);

                        updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                        /// subtract margin from offer price
                        updateObject.updated_offer_price =
                            parseFloat(parseFloat(offer_price).toFixed(2)) - marginedPrice;
                    }
                } else if (adminRole.vendor === role) {
                    if (!offer.vendor_id) {
                        throw new Error('Vendor id not found!!!');
                    }

                    /// apply margin to offer price
                    const marginedPrice: number =
                        parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(offer.vendor_margin) / 100);

                    updateObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    /// add margin to offer price
                    updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2)) + marginedPrice;

                    /// response from user
                    updateObject.is_response_required = true;
                }

                if (role === adminRole.vendor) {
                    if (
                        parseFloat(parseFloat(updateObject.updated_offer_price).toFixed(2)) >
                        parseFloat(parseFloat(offer.base_price_vendor).toFixed(2))
                    ) {
                        throw new Error('Offer price can not be greater than diamond price!!!');
                    }
                } else {
                    if (
                        parseFloat(parseFloat(updateObject.offer_price).toFixed(2)) >
                        parseFloat(parseFloat(offer.base_price).toFixed(2))
                    ) {
                        throw new Error('Offer price can not be greater than diamond price!!!');
                    }
                }
            }
            /// accept offer
            else if (status === OfferStatus.accepted) {
                logger.info('!!!!!!! Accepting offer');

                /// create buy request

                const stockIds: any = {
                    stock_id: offer?.stock_id,
                    is_available: true,
                    is_action_taken: offer?.vendor_id ? false : true
                };

                if (offer?.vendor_id) {
                    stockIds.vendor_id = offer.vendor_id;
                }

                if (offer?.admin_id) {
                    stockIds.admin_id = offer.admin_id;
                }

                logger.info('creating buy request when offer accepted');
                /// create buy request with PENDING status
                /// is_available true
                const buyRequest: any = await models.buy_requests.create(
                    {
                        ...req.body,
                        user_id: offer.user_id,
                        stock_ids: [stockIds],
                        vendor_ids: offer?.vendor_id ? [offer?.vendor_id] : [],
                        status: buyRequestStatus.pending,
                        updated_by_id: id,
                        order_code: makeUniqueKey(10),
                        amount: offer?.offer_price,
                        grand_total: offer?.offer_price,
                        updated_amount: offer?.updated_offer_price,
                        updated_grand_total: offer?.updated_offer_price,
                        type: buyRequestType.offer
                    },
                    { transaction }
                );

                logger.info('putting stocks on HOLD');
                /// put stock on hold
                await models.stocks.update({ status: 'HOLD' }, { where: { id: offer.stock_id }, transaction });

                /// update user buy_request_count
                await models.users.update(
                    { buy_request_count: sequelize.literal('buy_request_count + 1') },
                    { where: { id: offer.user_id } }
                );

                /// update vendor buy_request_count
                if (offer.vendor_id) {
                    await models.vendors.update(
                        { buy_request_count: sequelize.literal('buy_request_count + 1') },
                        { where: { id: offer.vendor_id } }
                    );
                }

                updateObject.buy_request_id = buyRequest?.id;
                /// response from user
                updateObject.is_response_required = true;
            }
            /// reject offer
            else if (status === OfferStatus.rejected) {
                logger.info('rejecting offer');
                updateObject.is_completed = true;
                /// response from user
                updateObject.is_response_required = false;
            }

            updateObject.status = status === OfferStatus.pending ? OfferStatus.revised : status;
            updateObject.last_action_id = id;

            /// update last action type
            if (role === adminRole.user) {
                updateObject.last_action_type = OfferActionType.user;
            } else if (role === adminRole.vendor) {
                updateObject.last_action_type = OfferActionType.vendor;
            } else if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                updateObject.last_action_type = OfferActionType.admin;
            }

            logger.info('Updating offer into table');
            /// update offer
            await models.stock_offers.update(updateObject, { where: { id: offer_id }, transaction });

            /// fetch updated offer for offer trail
            const updatedOffer: any = await models.stock_offers.findOne({
                where: { id: offer_id },
                attributes: { exclude: ['createdAt', 'updatedAt'] },
                transaction
            });

            const trailsObject: any = JSON.parse(JSON.stringify(updatedOffer));

            delete trailsObject.id;

            /// set old trails response required to false
            await models.stock_offer_trails.update(
                { is_response_required: false },
                { where: { offer_id }, transaction }
            );

            logger.info('creating offer trails');
            /// create new trial from updated offer
            const updatedOfferTrail: any = await models.stock_offer_trails.create(
                {
                    ...trailsObject,
                    offer_id: updatedOffer.id,
                    payload: JSON.stringify(updatedOffer)
                },
                { transaction }
            );

            /// for other users
            /// reject other users offer when accepted
            if (status === OfferStatus.accepted) {
                logger.info('Rejecting other offers for same stocks');
                /// reject other offers for same stock
                await models.stock_offers.update(
                    { status: OfferStatus.rejected, is_completed: true, is_response_required: false },
                    {
                        where: {
                            [Op.and]: [
                                { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } },
                                { stock_id: offer.stock_id },
                                { user_id: { [Op.ne]: offer.user_id } }
                            ]
                        },
                        transaction
                    }
                );

                /// create rejected trail for details page in app
                const otherUsersOffers = await models.stock_offers.findAll({
                    where: {
                        [Op.and]: [
                            { status: OfferStatus.rejected },
                            { stock_id: offer.stock_id },
                            { user_id: { [Op.ne]: offer.user_id } }
                        ]
                    },
                    attributes: { exclude: ['createdAt', 'updatedAt'] },
                    transaction
                });

                /// create rejected trail for details page in app
                if (otherUsersOffers.length) {
                    logger.info('Creating rejected trail for other users');
                    const otherUserOfferTrailObject = otherUsersOffers.map((item: any) => {
                        const otherTrailsObject: any = JSON.parse(JSON.stringify(item));
                        delete otherTrailsObject.id;
                        return {
                            ...otherTrailsObject,
                            offer_id: item.id,
                            payload: JSON.stringify(item)
                        };
                    });

                    const existsTrails = await models.stock_offer_trails.findAll({
                        where: {
                            [Op.and]: [
                                { user_id: { [Op.in]: otherUserOfferTrailObject.map((item: any) => item.user_id) } },
                                { status: OfferStatus.rejected },
                                { stock_id: offer.stock_id }
                            ]
                        },
                        transaction
                    });

                    const existsTrailsUsers = existsTrails.map((data: any) => data.user_id);

                    const filteredObject = otherUserOfferTrailObject.filter((item: any) => {
                        return !existsTrailsUsers.includes(item.user_id);
                    });

                    if (filteredObject.length) {
                        await models.stock_offer_trails.bulkCreate(filteredObject, { transaction });
                    }
                }
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Offers updated successfully`,
                data: updatedOfferTrail
            });

            try {
                /// notify user for offer accepted
                if (updatedOffer.status === OfferStatus.accepted) {
                    if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                        userNotification.sendOfferAcceptedRejectedNotification(
                            NotificationType.offerAccepted,
                            updatedOffer.id
                        );
                    }
                } else if (updatedOffer.status === OfferStatus.rejected) {
                    if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                        userNotification.sendOfferAcceptedRejectedNotification(
                            NotificationType.offerRejected,
                            updatedOffer.id
                        );
                    }
                } else if ([OfferStatus.revised, OfferStatus.pending].includes(updatedOffer.status)) {
                    if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                        userNotification.counterOfferNotification(NotificationType.offerRevised, updatedOffer.id, role);
                    }
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/user/update-offer
     * @apiName GetStockOffers
     * @apiGroup StockOffer
     *
     *
     * @apiSuccess {Object} StockOffer.
     */
    async updateOffer(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update stock offer');

        const transaction = await sequelize.transaction();

        try {
            const id = req[`id`];
            let loginId = id;
            const conditions: any[] = [];
            let role: any = req[`role`];

            const { offer_price, offer_id, status } = req.body;

            const updateObject: any = {};

            if (offer_price <= 0) {
                throw new Error(`Offer price cannot be zero!!!`);
            }

            conditions.push({ id: offer_id });

            const offer: any = await models.stock_offers.findOne({ where: { [Op.and]: conditions } });

            if (!offer) {
                throw new Error('Offer not found!!');
            }

            /// offer already accepted or rejected
            if ([OfferStatus.accepted, OfferStatus.rejected].includes(offer.status)) {
                throw new Error('Offer already accepted or rejected!!');
            }

            /// admin request + vendor offer role will be vendor
            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role) && offer?.vendor_id) {
                role = adminRole.vendor;
                loginId = offer.vendor_id;
            } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role) && offer?.admin_id) {
                if (id !== offer?.admin_id) {
                    throw new Error(`Unauthorized access.`);
                }
            } else if ([adminRole.vendor].includes(role)) {
                if (id !== offer?.vendor_id) {
                    throw new Error(`Unauthorized access.`);
                }
            } else if ([adminRole.user].includes(role)) {
                if (id !== offer?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            if (offer?.last_action_id === loginId) {
                throw new Error('Offer already submitted!!');
            }

            const stock: any = await models.stocks.findOne({ where: { id: offer?.stock_id }, transaction });

            if (!stock) {
                throw new Error('Stock not found!!');
            }

            if (stock.status !== 'AVAILABLE') {
                throw new Error('Stock sold out!!');
            }

            /// counter offer limit
            if ([OfferStatus.accepted, OfferStatus.rejected].includes(offer.status)) {
                throw new Error('Offer already accepted or rejected!!');
                // if (role === adminRole.user && [OfferStatus.rejected].includes(offer.status)) {
                //     logger.info('Checking rejected offer limit');
                //     /// check three rejected offers
                //     const rejectedOffersCount = await models.stock_offer_trails.count({
                //         where: {
                //             offer_id: offer?.id,
                //             status: OfferStatus.rejected
                //         },
                //         transaction
                //     });

                //     if (rejectedOffersCount >= 3) {
                //         throw new Error('Offer limit exceeded!!');
                //     }
                // } else {
                //     throw new Error('Offer already accepted or rejected!!');
                // }
            }

            /// update offer pending
            if ([OfferStatus.pending, OfferStatus.revised].includes(status)) {
                // offer_price = user side value
                // updated_offer_price = vendor side value
                // base_price = user side value
                // base_price_vendor = vendor side value
                // last_offer_price = user side value
                // updated_last_offer_price = vendor side value

                /// check base_price_vendor for vendor and base_price for  use and admin
                if (role === adminRole.vendor) {
                    if (
                        parseFloat(parseFloat(offer_price).toFixed(2)) >=
                        parseFloat(parseFloat(offer.base_price_vendor).toFixed(2))
                    ) {
                        throw new Error('Offer price must be less than diamond price!!');
                    }
                } else {
                    if (
                        parseFloat(parseFloat(offer_price).toFixed(2)) >=
                        parseFloat(parseFloat(offer.base_price).toFixed(2))
                    ) {
                        throw new Error('Offer price must be less than diamond price!!');
                    }
                }

                if (role === adminRole.user) {
                    const countOfPreviousOffers = await models.stock_offer_trails.count({
                        where: { user_id: offer.user_id, offer_id: offer.id, last_offer_id: loginId },
                        transaction
                    });

                    if (countOfPreviousOffers >= 3) {
                        throw new Error('!!!!!!!!!!Offer limit exceeded!!!!!!!!!!!!!!');
                    }
                }

                /// offer price
                logger.info('updating offer price when status is pending or revised!!');
                logger.info('Checking previous offer price!!!');
                /// offer can not be less than previous offer
                const previousOffer = await models.stock_offer_trails.findOne({
                    where: { user_id: offer?.user_id, offer_id: offer?.id, last_offer_id: loginId },
                    order: [['createdAt', 'DESC']],
                    transaction
                });

                logger.info(`!!!!!role!!!!!!! ${role}`);
                logger.info(`!!!!!offer!!!!!!! ${JSON.stringify(offer, null, 2)}`);
                logger.info(`!!!!offer_price!!! ${offer_price}`);
                logger.info(`!!!!!Previous Offer ${JSON.stringify(previousOffer, null, 2)}`);

                let minPrice;
                let maxPrice;

                if (role === adminRole.vendor) {
                    if (previousOffer) {
                        minPrice = offer?.updated_offer_price;
                        maxPrice = previousOffer?.updated_offer_price;
                    } else {
                        minPrice = offer?.updated_offer_price;
                        maxPrice = offer?.base_price_vendor;
                    }

                    // this case check if vendor offer the price but after margin if offer is still less than the user offer it should not be allowed
                    const marginedPrice: number =
                        parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(offer.vendor_margin) / 100);
                    const user_offer_price = parseFloat(parseFloat(offer_price).toFixed(2)) + marginedPrice;

                    if (user_offer_price < offer?.offer_price) {
                        throw new Error(`Offer price must be greater than previous offer!!`);
                    }
                } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    if (previousOffer) {
                        minPrice = offer?.offer_price;
                        maxPrice = previousOffer?.offer_price;
                    } else {
                        minPrice = offer?.offer_price;
                        maxPrice = offer?.base_price;
                    }
                } else if (role === adminRole.user) {
                    if (previousOffer) {
                        minPrice = previousOffer?.offer_price;
                        maxPrice = offer?.offer_price;
                    } else {
                        minPrice = offer?.offer_price;
                        maxPrice = offer?.base_price;
                    }
                }

                logger.info(`!!!!minPrice!!! ${minPrice}`);
                logger.info(`!!!!maxPrice!!! ${maxPrice}`);

                if (minPrice > maxPrice) {
                    logger.info('!!!!!!!minPrice is greater than maxPrice!!!!!!!!!');
                    if (role === adminRole.user) {
                        const previousVendorAdminOffer = await models.stock_offer_trails.findAll({
                            where: {
                                user_id: offer?.user_id,
                                offer_id: offer?.id,
                                last_offer_id: { [Op.ne]: loginId }
                            },
                            order: [['createdAt', 'DESC']],
                            transaction
                        });

                        // calculate maxPrice from this list of offers
                        maxPrice = previousVendorAdminOffer.reduce((acc: number, item: any) => {
                            return acc < parseFloat(item.offer_price) ? parseFloat(item.offer_price) : acc;
                        }, 0);
                    }
                }

                logger.info(`!!!!minPrice!!! ${minPrice}`);
                logger.info(`!!!!maxPrice!!! ${maxPrice}`);

                // check min Price and Max Price
                if (
                    parseFloat(parseFloat(offer_price).toFixed(2)) <= parseFloat(parseFloat(minPrice).toFixed(2)) ||
                    parseFloat(parseFloat(offer_price).toFixed(2)) >= parseFloat(parseFloat(maxPrice).toFixed(2))
                ) {
                    throw new Error(`Offer price must be between ${minPrice} and ${maxPrice}!!`);
                }

                // if (role === adminRole.vendor) {
                //     /// vendors offer price should be greater than his own previous offer
                //     if (previousOffer) {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //             parseFloat(parseFloat(previousOffer?.updated_offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be less than previous offer!!`);
                //         }
                //     }
                //     /// first offer must be greater than buyer offer
                //     else {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) <=
                //             parseFloat(parseFloat(offer?.updated_offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be greater than buyer offer!!`);
                //         }
                //     }
                // } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                //     /// user and admin offer price should be greater than his own previous offer
                //     if (previousOffer) {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //             parseFloat(parseFloat(previousOffer?.offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be less than previous offer!!`);
                //         }
                //     }
                //     /// first offer must be greater than buyer offer
                //     else {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) <=
                //             parseFloat(parseFloat(offer?.offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be greater than buyer offer!!`);
                //         }
                //     }
                // } else if (role === adminRole.user) {
                //     /// users offer will greater than his previous offer
                //     if (previousOffer) {
                //         if (
                //             parseFloat(parseFloat(offer_price).toFixed(2)) <=
                //             parseFloat(parseFloat(previousOffer?.offer_price).toFixed(2))
                //         ) {
                //             throw new Error(`Offer price must be greater than previous offer!!`);
                //         }
                //     }

                //     /// users offer price should be less vendors last offer price
                //     /// and skip checking vendors last offer on counter offer

                //     // Commented as we are not allowing counter offer once the offer is rejected
                //     // if (
                //     //     parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //     //         parseFloat(parseFloat(offer?.offer_price).toFixed(2)) &&
                //     //     offer.status !== OfferStatus.rejected
                //     // ) {
                //     //     throw new Error(`Offer price must be less than previous offer!!`);
                //     // }
                //     if (
                //         parseFloat(parseFloat(offer_price).toFixed(2)) >=
                //             parseFloat(parseFloat(offer?.offer_price).toFixed(2))
                //     ) {
                //         throw new Error(`Offer price must be less than previous offer!!`);
                //     }
                // }

                updateObject.last_offer_id = loginId;
                updateObject.last_offer_price = offer.offer_price;
                updateObject.updated_last_offer_price = offer.updated_offer_price;

                logger.info('Calculating prices for offer');
                if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                    /// calculate updated offer price for vendor
                    updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    /// subtract margin from offer price
                    updateObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    /// response from user
                    updateObject.is_response_required = true;
                } else if (adminRole.user === role) {
                    /// response from user
                    updateObject.is_response_required = false;
                    if (offer.admin_id) {
                        /// calculate updated offer price for vendor
                        updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                        /// subtract margin from offer price
                        updateObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    } else if (offer.vendor_id) {
                        /// apply margin to offer price
                        const marginedPrice: number =
                            parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(offer.vendor_margin) / 100);

                        updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                        /// subtract margin from offer price
                        updateObject.updated_offer_price =
                            parseFloat(parseFloat(offer_price).toFixed(2)) - marginedPrice;
                    }
                } else if (adminRole.vendor === role) {
                    if (!offer.vendor_id) {
                        throw new Error('Vendor id not found!!!');
                    }

                    /// apply margin to offer price
                    const marginedPrice: number =
                        parseFloat(parseFloat(offer_price).toFixed(2)) * (parseFloat(offer.vendor_margin) / 100);

                    updateObject.updated_offer_price = parseFloat(parseFloat(offer_price).toFixed(2));
                    /// add margin to offer price
                    updateObject.offer_price = parseFloat(parseFloat(offer_price).toFixed(2)) + marginedPrice;

                    /// response from user
                    updateObject.is_response_required = true;
                }

                if (role === adminRole.vendor) {
                    if (
                        parseFloat(parseFloat(updateObject.updated_offer_price).toFixed(2)) >
                        parseFloat(parseFloat(offer.base_price_vendor).toFixed(2))
                    ) {
                        throw new Error('Offer price can not be greater than diamond price!!!');
                    }
                } else {
                    if (
                        parseFloat(parseFloat(updateObject.offer_price).toFixed(2)) >
                        parseFloat(parseFloat(offer.base_price).toFixed(2))
                    ) {
                        throw new Error('Offer price can not be greater than diamond price!!!');
                    }
                }
            }
            /// accept offer
            else if (status === OfferStatus.accepted) {
                logger.info('!!!!!!! Accepting offer');

                /// create buy request

                const stockIds: any = {
                    stock_id: offer?.stock_id,
                    is_available: true,
                    is_action_taken: offer?.vendor_id ? false : true
                };

                if (offer?.vendor_id) {
                    stockIds.vendor_id = offer.vendor_id;
                }

                if (offer?.admin_id) {
                    stockIds.admin_id = offer.admin_id;
                }

                logger.info('creating buy request when offer accepted');
                /// create buy request with PENDING status
                /// is_available true
                const buyRequest: any = await models.buy_requests.create(
                    {
                        ...req.body,
                        user_id: offer.user_id,
                        stock_ids: [stockIds],
                        vendor_ids: offer?.vendor_id ? [offer?.vendor_id] : [],
                        status: buyRequestStatus.pending,
                        updated_by_id: loginId,
                        order_code: makeUniqueKey(10),
                        amount: offer?.offer_price,
                        grand_total: offer?.offer_price,
                        updated_amount: offer?.updated_offer_price,
                        updated_grand_total: offer?.updated_offer_price,
                        type: buyRequestType.offer
                    },
                    { transaction }
                );

                logger.info('putting stocks on HOLD');
                /// put stock on hold
                await models.stocks.update({ status: 'HOLD' }, { where: { id: offer.stock_id }, transaction });

                /// update user buy_request_count
                await models.users.update(
                    { buy_request_count: sequelize.literal('buy_request_count + 1') },
                    { where: { id: offer.user_id } }
                );

                /// update vendor buy_request_count
                if (offer.vendor_id) {
                    await models.vendors.update(
                        { buy_request_count: sequelize.literal('buy_request_count + 1') },
                        { where: { id: offer.vendor_id } }
                    );
                }

                updateObject.buy_request_id = buyRequest?.id;
                /// response from user
                updateObject.is_response_required = true;
            }
            /// reject offer
            else if (status === OfferStatus.rejected) {
                logger.info('rejecting offer');
                updateObject.is_completed = true;
                /// response from user
                updateObject.is_response_required = false;
            }

            updateObject.status = status === OfferStatus.pending ? OfferStatus.revised : status;
            updateObject.last_action_id = loginId;

            /// update last action type
            if (role === adminRole.user) {
                updateObject.last_action_type = OfferActionType.user;
            } else if (role === adminRole.vendor) {
                // if loggedIn admin id and loginId (Which will be changed in case of admin make the request for the vendor offer) is different that means admin has made the request for vendor offer
                updateObject.last_action_type = loginId !== id ? OfferActionType.adminVendor : OfferActionType.vendor;
            } else if ([adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                updateObject.last_action_type = OfferActionType.admin;
            }

            logger.info('Updating offer into table');
            /// update offer
            await models.stock_offers.update(updateObject, { where: { id: offer_id }, transaction });

            /// fetch updated offer for offer trail
            const updatedOffer: any = await models.stock_offers.findOne({
                where: { id: offer_id },
                attributes: { exclude: ['createdAt', 'updatedAt'] },
                transaction
            });

            const trailsObject: any = JSON.parse(JSON.stringify(updatedOffer));

            delete trailsObject.id;

            /// set old trails response required to false
            await models.stock_offer_trails.update(
                { is_response_required: false },
                { where: { offer_id }, transaction }
            );

            logger.info('creating offer trails');
            /// create new trial from updated offer
            const updatedOfferTrail: any = await models.stock_offer_trails.create(
                {
                    ...trailsObject,
                    offer_id: updatedOffer.id,
                    payload: JSON.stringify(updatedOffer)
                },
                { transaction }
            );

            /// for other users
            /// reject other users offer when accepted
            if (status === OfferStatus.accepted) {
                logger.info('Rejecting other offers for same stocks');
                /// reject other offers for same stock
                await models.stock_offers.update(
                    { status: OfferStatus.rejected, is_completed: true, is_response_required: false },
                    {
                        where: {
                            [Op.and]: [
                                { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } },
                                { stock_id: offer.stock_id },
                                { user_id: { [Op.ne]: offer.user_id } }
                            ]
                        },
                        transaction
                    }
                );

                /// create rejected trail for details page in app
                const otherUsersOffers = await models.stock_offers.findAll({
                    where: {
                        [Op.and]: [
                            { status: OfferStatus.rejected },
                            { stock_id: offer.stock_id },
                            { user_id: { [Op.ne]: offer.user_id } }
                        ]
                    },
                    attributes: { exclude: ['createdAt', 'updatedAt'] },
                    transaction
                });

                /// create rejected trail for details page in app
                if (otherUsersOffers.length) {
                    logger.info('Creating rejected trail for other users');
                    const otherUserOfferTrailObject = otherUsersOffers.map((item: any) => {
                        const otherTrailsObject: any = JSON.parse(JSON.stringify(item));
                        delete otherTrailsObject.id;
                        return {
                            ...otherTrailsObject,
                            offer_id: item.id,
                            payload: JSON.stringify(item)
                        };
                    });

                    const existsTrails = await models.stock_offer_trails.findAll({
                        where: {
                            [Op.and]: [
                                { user_id: { [Op.in]: otherUserOfferTrailObject.map((item: any) => item.user_id) } },
                                { status: OfferStatus.rejected },
                                { stock_id: offer.stock_id }
                            ]
                        },
                        transaction
                    });

                    const existsTrailsUsers = existsTrails.map((data: any) => data.user_id);

                    const filteredObject = otherUserOfferTrailObject.filter((item: any) => {
                        return !existsTrailsUsers.includes(item.user_id);
                    });

                    if (filteredObject.length) {
                        await models.stock_offer_trails.bulkCreate(filteredObject, { transaction });
                    }
                }
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Offers updated successfully`,
                data: updatedOfferTrail
            });

            try {
                /// notify user for offer accepted
                if (updatedOffer.status === OfferStatus.accepted) {
                    if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                        userNotification.sendOfferAcceptedRejectedNotification(
                            NotificationType.offerAccepted,
                            updatedOffer.id
                        );
                    }
                } else if (updatedOffer.status === OfferStatus.rejected) {
                    if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                        userNotification.sendOfferAcceptedRejectedNotification(
                            NotificationType.offerRejected,
                            updatedOffer.id
                        );
                    }
                } else if ([OfferStatus.revised, OfferStatus.pending].includes(updatedOffer.status)) {
                    if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                        userNotification.counterOfferNotification(NotificationType.offerRevised, updatedOffer.id, role);
                    }
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/stock-offer
     * @apiName GetStockOffers
     * @apiGroup StockOffer
     *
     *
     * @apiSuccess {Object} StockOffer.
     */
    async listStockOffer(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling get stock offer');
        try {
            const id = req[`id`];
            const role = req[`role`];
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const isCompleted = req.query.is_completed;
            const isResponseRequired = req.query.is_response_required;
            const isVendorsOffers = String(req.query.is_vendors_offers).toLowerCase() === 'true';
            const vendorId = req.query.vendor_id;
            const userId = req.query.user_id;
            const conditions: any[] = [];

            /// admins request
            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                /// all vendors offers
                if (isVendorsOffers) {
                    if (vendorId) {
                        /// group by only vendors offers
                        conditions.push({ vendor_id: vendorId });
                    } else if (userId) {
                        /// group by only user offers
                        conditions.push({ user_id: userId });
                    } else {
                        /// all vendors offers
                        conditions.push({ vendor_id: { [Op.ne]: null } });
                    }
                }
                /// only vendors offers
                else {
                    conditions.push({ admin_id: id });
                }
            }
            /// vendors offer only
            else if (adminRole.vendor === role) {
                conditions.push({ vendor_id: id });
            }
            /// users offer only
            else if (adminRole.user === role) {
                conditions.push({ user_id: id });
            }

            /// pending status
            if (status?.toString().toUpperCase() === OfferStatus.pending) {
                /// for admin adn vendor list only updated from user
                if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                    if (isVendorsOffers) {
                        if (vendorId) {
                            /// not offered by vendor
                            conditions.push({ last_offer_id: { [Op.ne]: vendorId } });
                        } else {
                            /// not offered by all vendors
                            conditions.push({ last_offer_id: { [Op.ne]: Sequelize.col('stock_offers.vendor_id') } });
                        }
                    } else {
                        conditions.push({ last_offer_id: { [Op.ne]: id } });
                    }
                }

                /// if status is pending consider both revised and pending
                conditions.push({
                    status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                });
            }

            /// accepted or rejected
            if (status && status.toString().toUpperCase() !== OfferStatus.pending) {
                conditions.push({ status: status.toString().toUpperCase() });
            }

            /// rejected offers and placed order if accepted
            if (isCompleted) {
                conditions.push({ is_completed: isCompleted });
            }

            /// when users response required
            if (isResponseRequired) {
                if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                    if (status) {
                        if (
                            status.toString().toUpperCase() === OfferStatus.pending ||
                            status.toString().toUpperCase() === OfferStatus.revised
                        ) {
                            conditions.push({ is_response_required: isResponseRequired });
                        }
                    }
                } else if (adminRole.user === role) {
                    conditions.push({ is_response_required: isResponseRequired });
                }
            }

            const { rows, count } = await models.stock_offers.findAndCountAll({
                where: { [Op.and]: conditions },
                include: [
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'discounts_ori',
                            'price_per_caret',
                            'price_per_caret_ori',
                            'final_price',
                            'final_price_ori',
                            'sku_number',
                            'sku_prefix',
                            'lab',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'stock_margin',
                            'diamond_type',
                            'is_lab_grown',
                            'growth_type',
                            'stock_margin'
                        ]
                    },
                    { model: models.users, attributes: ['id', 'first_name', 'last_name', 'email'] },
                    { model: models.vendors, attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'], required: false }
                ],
                order: [['updatedAt', 'DESC']],
                offset: skip,
                limit
            });

            // Add `updated_offer_price_per_caret` calculation
            const updatedRows = rows.map((row) => {
                const stock = row.stock || {};
                return {
                    ...row.toJSON(), // Convert Sequelize model to plain object
                    offer_price_per_caret: ((row?.offer_price ?? 0) / (stock?.weight ?? 0)).toFixed(2),
                    updated_offer_price_per_caret: ((row?.updated_offer_price ?? 0) / (stock?.weight ?? 0)).toFixed(2),
                    last_offer_price_per_caret: ((row?.last_offer_price ?? 0) / (stock?.weight ?? 0)).toFixed(2),
                    updated_last_offer_price_per_caret: (
                        (row?.updated_last_offer_price ?? 0) / (stock?.weight ?? 0)
                    ).toFixed(2)
                };
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Offers listed successfully`,
                data: updatedRows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/offer-vendors-group
     * @apiName GetVendorsOffersGroup
     * @apiGroup StockOfferGroup
     *
     *
     * @apiSuccess {Object} StockOfferGroup.
     */
    async listStockOfferGroup(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling get offer vendors group by');
        try {
            const id = req[`id`];
            const role = req[`role`];
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const isGroupByVendor = String(req.query.is_group_by_vendor).toLowerCase() === 'true';
            const isGroupByUser = String(req.query.is_group_by_user) === 'true';
            const conditions: any[] = [];

            /// admins request
            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                /// all vendors offers
                conditions.push({ vendor_id: { [Op.ne]: null } });
            }

            /// pending status
            if (status?.toString().toUpperCase() === OfferStatus.pending) {
                /// for admin adn vendor list only updated from user
                if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
                    conditions.push({
                        last_offer_id: {
                            [Op.ne]: isGroupByVendor
                                ? Sequelize.col('stock_offers.vendor_id')
                                : Sequelize.col('stock_offers.user_id')
                        }
                    });
                }

                /// if status is pending consider both revised and pending
                conditions.push({
                    status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] }
                });
            }

            /// accepted or rejected
            if (status && status.toString().toUpperCase() !== OfferStatus.pending) {
                conditions.push({ status: status.toString().toUpperCase() });
            }

            /// fetch all offers
            const offers: any = await models.stock_offers.findAll({
                where: {
                    [Op.and]: [...conditions]
                },
                order: [['updatedAt', 'DESC']]
            });

            /// vendors ids
            const vendorIds: any = [...new Set([...offers.map((offer: any) => offer?.vendor_id)])];

            /// user ids
            const userIds: any = [...new Set([...offers.map((offer: any) => offer?.user_id)])];

            /// stock ids
            /// const stockIds: any = offers.map((offer: any) => offer.stock_id);

            /// group by result
            const groupByResult: any[] = [];
            let resultCount = 0;

            /// group by vendors
            if (isGroupByVendor) {
                logger.info('fetching vendors list');

                /// vendors
                const { rows, count } = await models.vendors.findAndCountAll({
                    where: { id: { [Op.in]: vendorIds } },
                    order: [['createdAt', 'DESC']],
                    attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'],
                    offset: skip,
                    limit
                });

                /// add offers count
                const updatedVendors: any = rows.map((item: any) => ({
                    ...JSON.parse(JSON.stringify(item)),
                    offers_count: offers?.filter((offer: any) => offer?.vendor_id === item?.id)?.length ?? 0
                }));

                // Sort by offers_count in descending order
                updatedVendors.sort((a, b) => b.offers_count - a.offers_count);

                groupByResult.push(...updatedVendors);
                resultCount = count;
            }
            /// group by users
            else if (isGroupByUser) {
                logger.info('fetching users list');
                /// users
                const { rows, count } = await models.users.findAndCountAll({
                    where: { id: { [Op.in]: userIds } },
                    order: [['createdAt', 'DESC']],
                    attributes: ['id', 'first_name', 'last_name', 'email'],
                    offset: skip,
                    limit
                });

                /// add offers count
                const updatedUsers = rows.map((item: any) => ({
                    ...JSON.parse(JSON.stringify(item)),
                    offers_count: offers?.filter((offer: any) => offer?.user_id === item?.id)?.length ?? 0
                }));

                // Sort by offers_count in descending order
                updatedUsers.sort((a, b) => b.offers_count - a.offers_count);

                groupByResult.push(...updatedUsers);
                resultCount = count;
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Group by offers listed successfully`,
                data: groupByResult,
                count: resultCount
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/offer-buy-request
     * @apiName GetOfferBuyRequest
     * @apiGroup OfferBuyRequest
     *
     *
     * @apiSuccess {Object} OfferBuyRequest.
     */
    async getOfferBuyRequest(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling get stock offer');
        try {
            const id = req[`id`];
            const role = req[`role`];
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const conditions: any[] = [];

            if (role === adminRole.user) {
                conditions.push({ user_id: id });
            }

            if (status) {
                conditions.push({ status: status.toString().toUpperCase() });
            }

            conditions.push({ type: 'OFFER' });

            /// fetch buy requests
            const { rows, count } = await models.buy_requests.findAndCountAll({
                where: { [Op.and]: conditions },
                order: [['updatedAt', 'DESC']],
                offset: skip,
                limit
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Offers listed successfully`,
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/place-order
     * @apiName PlaceOrder
     * @apiGroup OfferBuyRequest
     *
     *
     * @apiSuccess {Object} PlaceOrder.
     */
    async placeOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling place order for accepted offer');

        const transaction = await sequelize.transaction();

        try {
            const userId = req[`id`];
            const role = req[`role`];
            const userData: any = req[`user`];

            const {
                card_number,
                card_holder_name,
                exp_date,
                cvv,
                payment_mode,
                offer_id,
                shipment_id,
                shipment_price,
                apple_pay_response
            } = req.body;

            /// create buy req by user ony
            if (role !== adminRole.user) {
                throw new Error('Unauthorized access');
            }

            /// check user is blocked
            if (userData.is_blocked) {
                throw new Error('Please contact admin. User is blocked!!!');
            }

            /// fetch offer
            const offer: any = await models.stock_offers.findOne({ where: { id: offer_id }, transaction });

            if (!offer) {
                throw new Error('Offer not found!!');
            }

            if (role === adminRole.user) {
                if (userId !== offer?.user_id) {
                    throw new Error(`Unauthorized access.`);
                }
            }

            if (offer.status !== OfferStatus.accepted) {
                throw new Error('Offer is not accepted!!');
            }

            if (offer.order_id) {
                throw new Error('Order already exists!!');
            }

            /// fetch buy request for offer
            const buyRequest: any = await models.buy_requests.findOne({
                where: { id: offer?.buy_request_id },
                transaction
            });

            if (!buyRequest) {
                throw new Error('Buy request not found!!');
            }

            /// add shipment price
            const grandTotal: number =
                parseFloat(parseFloat(buyRequest.grand_total).toFixed(2)) + parseFloat(shipment_price);

            /// add shipment price
            const updatedGrandTotal: number =
                parseFloat(parseFloat(buyRequest.updated_grand_total).toFixed(2)) + parseFloat(shipment_price);

            /// check credit available
            if (payment_mode === PaymentMode.creditLimit) {
                logger.info('!!!!!!! Calculating available credit limit!!!');

                /// fetch sum of credit
                const totalCredit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'CREDIT',
                        user_id: userId
                    }
                });

                /// fetch sum of debit
                const totalDebit = await models.credit_histories.sum('credit', {
                    where: {
                        transaction_type: 'DEBIT',
                        user_id: userId
                    }
                });

                const availableCreditLimit =
                    parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                    parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

                /// check amount exceed available credit
                if (grandTotal > availableCreditLimit) {
                    throw new Error('Not enough credit');
                }
            }

            /// check vendor black listed
            if (offer.vendor_id) {
                const vendor = await models.vendors.findOne({
                    where: { id: offer.vendor_id, is_blacklisted: true },
                    transaction
                });

                /// check vendor blacklisted
                if (vendor) {
                    throw new Error('Please refresh your stock list');
                }
            }

            logger.info('Updating buy request for accepted offer');
            /// updated buy request
            await models.buy_requests.update(
                {
                    ...req.body,
                    status: buyRequestStatus.accepted,
                    grand_total: grandTotal,
                    updated_grand_total: updatedGrandTotal
                },
                {
                    where: { id: buyRequest.id },
                    returning: true,
                    plain: true,
                    transaction
                }
            );

            const updatedBuyRequest: any = await models.buy_requests.findOne({
                where: { id: buyRequest.id },
                transaction
            });

            /// fetch latest generate invoice number
            const lastInvoice: any = await models.orders.findOne({
                order: [['createdAt', 'DESC']],
                attribute: ['invoice_number', 'financial_year']
            });

            /// generate invoice number
            const invoiceNumber: string = `${process.env.INVOICE_PREFIX}${getInvoiceNumber(
                lastInvoice
            )}${calculateFinancialYear().replace('-', '')}`;

            logger.info('Create order for accepted offer');
            /// create order
            const order = await models.orders.create(
                {
                    user_id: updatedBuyRequest.user_id,
                    buy_request_id: updatedBuyRequest.id,
                    amount: updatedBuyRequest.amount,
                    updated_amount: updatedBuyRequest.updated_amount,
                    shipment_id: updatedBuyRequest.shipment_id,
                    shipment_price: updatedBuyRequest.shipment_price,
                    grand_total: updatedBuyRequest.grand_total,
                    updated_grand_total: updatedBuyRequest.updated_grand_total,
                    payment_status: 'PAID',
                    order_code: updatedBuyRequest.order_code,
                    order_status: 'PENDING',
                    billing_address: updatedBuyRequest.billing_address,
                    shipping_address: updatedBuyRequest.shipping_address,
                    payment_mode: updatedBuyRequest.payment_mode,
                    user_details: userData,
                    invoice_number: invoiceNumber,
                    financial_year: calculateFinancialYear(),
                    type: updatedBuyRequest.type,
                    unified_order_type: unifiedOrderType.DIAMONDS
                },
                { transaction }
            );

            if (!order) {
                throw new Error('Something went wrong!!');
            }

            logger.info('Updating order details in offer and offer trails');
            /// update order_id in offer
            /// is_completed: offer has order placed or offer is rejected
            /// is_response_required: flag true when response required from user
            await models.stock_offers.update(
                { order_id: order.id, is_completed: true, is_response_required: false },
                { where: { id: offer_id }, transaction }
            );

            /// make all entries is_response_required false
            await models.stock_offer_trails.update(
                { is_response_required: false },
                { where: { offer_id }, transaction }
            );

            /// and make is_completed true
            await models.stock_offer_trails.update(
                { is_completed: true, order_id: order.id },
                { where: { [Op.and]: [{ offer_id }, { status: OfferStatus.accepted }] }, transaction }
            );

            const updatedOffer: any = await models.stock_offers.findOne({ where: { id: offer_id }, transaction });

            logger.info('Initiate payment for offer order!!!');
            ////////////////// MAKE PAYMENT //////////////////////////
            if (payment_mode === PaymentMode.creditCard) {
                /// check for customer profile id exists
                if (!userData?.customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }

                /// authorize credit card
                await authorizePayment.authorizeCreditCard(
                    false,
                    card_number,
                    card_holder_name,
                    exp_date,
                    cvv,
                    userId,
                    updatedBuyRequest,
                    transaction,
                    buyRequestStatus.pending
                );

                /// capture amount
                await authorizePayment.capturePreviouslyAuthorizedAmount(
                    updatedBuyRequest.id,
                    transaction,
                    buyRequestStatus.accepted
                );

                /////
            } else if (payment_mode === PaymentMode.applePay) {
                /// ApplePay
                /// check for customer profile id exists
                if (!req[`user`].customer_profile_id) {
                    /// create customer profile
                    await authorizePayment.createCustomerProfile(req, transaction);
                }
                /// ApplePay
                await authorizePayment.createApplePayTransaction(
                    updatedBuyRequest.id,
                    apple_pay_response,
                    userId,
                    transaction
                );
            } else if (payment_mode === PaymentMode.creditLimit) {
                logger.info('debiting credit limit for offer order');
                /// debit credit limit
                await models.credit_histories.create(
                    {
                        user_id: updatedBuyRequest.user_id,
                        credit: updatedBuyRequest.grand_total,
                        type: 'CREATE-BUY-REQUEST',
                        transaction_type: 'DEBIT',
                        buy_request_id: updatedBuyRequest.id
                    },
                    { transaction }
                );
            }

            /// create vendor order
            if (updatedOffer?.vendor_id) {
                logger.info('creating vendor order for offer order');
                /// create vendor orders
                const vendorOrder: any = await models.vendor_orders.create(
                    {
                        stock_id: updatedOffer.stock_id,
                        vendor_id: updatedOffer.vendor_id,
                        order_id: order.id,
                        buy_request_id: order.buy_request_id,
                        status: vendorOrderStatus.pending
                    },
                    { transaction }
                );

                /// create vendor order trail
                await models.vendor_order_trails.create(
                    {
                        vendor_order_id: vendorOrder.id,
                        order_id: vendorOrder.order_id,
                        buy_request_id: vendorOrder.buy_request_id,
                        vendor_id: vendorOrder.vendor_id,
                        updated_by_id: userId,
                        payload: JSON.stringify(vendorOrder),
                        status: vendorOrder.status // pending, shipped, paid
                    },
                    { transaction }
                );

                // update vendor order count
                await models.vendors.update(
                    { order_count: sequelize.literal('order_count + 1') },
                    { where: { id: updatedOffer.vendor_id }, transaction }
                );
            }

            logger.info('Updating stock status to SOLD');
            /// update stock status to SOLD
            await models.stocks.update({ status: 'SOLD' }, { where: { id: updatedOffer.stock_id }, transaction });

            /// update user order count
            await models.users.update(
                { order_count: sequelize.literal('order_count + 1') },
                { where: { id: updatedOffer.user_id }, transaction }
            );

            logger.info('creating buy request trail');
            /// add to trail
            await models.buy_request_trails.create(
                {
                    user_id: userId,
                    buy_request_id: updatedBuyRequest.id,
                    status: updatedBuyRequest.status,
                    updated_by_id: userId,
                    payload: JSON.stringify(updatedBuyRequest)
                },
                { transaction }
            );

            logger.info('creating order trail');
            /// create order trail
            await models.order_trails.create(
                {
                    order_id: order.id,
                    user_id: order.user_id,
                    buy_request_id: order.buy_request_id,
                    updated_by_id: userId,
                    payload: JSON.stringify(order),
                    payment_status: order.payment_status, // pending, paid, failed, canceled
                    order_status: order.order_status // pending, processing, shipped, delivered, canceled
                },
                { transaction }
            );

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Order placed successfully`,
                data: order
            });

            try {
                if (updatedBuyRequest?.stock_ids?.length) {
                    /// send notification for stock status change
                    userNotification.stockStatusChangeNotification(
                        updatedBuyRequest.stock_ids.map((item: any) => item.stock_id)
                    );
                }
            } catch (error: any) {
                logger.error(`stock status change notification error ${JSON.stringify(error, null, 2)}`);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/offer-trails
     * @apiName OfferTrails
     * @apiGroup OfferBuyRequest
     *
     *
     * @apiSuccess {Object} OfferTrails.
     */
    async listOfferTrails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list user offer trails');

        try {
            const id = req[`id`];
            const role = req[`role`];
            const offerId = req.query.offer_id;
            const conditions: any[] = [];

            if (!offerId) {
                throw new Error('Offer id required!!');
            }

            const offer: any = await models.stock_offers.findOne({ where: { id: offerId } });

            if (!offer) {
                throw new Error('Offer not found!!');
            }

            conditions.push({ offer_id: offerId });

            if (role === adminRole.user) {
                /// only users offer trails
                conditions.push({ user_id: id });
            } else if (role === adminRole.vendor) {
                /// only vendors offer trails
                conditions.push({ vendor_id: id });
            } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                /// admin can fetch trails for all users and vendors based on offer id only
                // conditions.push({ admin_id: id });
            }

            /// fetch offer trails
            const offerTrails: any = await models.stock_offer_trails.findAll({
                where: { [Op.and]: conditions },
                attributes: { exclude: ['payload'] },
                order: [['createdAt', 'DESC']]
            });

            if (!offerTrails.length) {
                throw new Error('Offers not found!!');
            }

            const stock = await models.stocks.findOne({ where: { id: offer?.stock_id } });

            // Add `updated_offer_price_per_caret` calculation
            const updatedRows = offerTrails.map((row) => {
                return {
                    ...row.toJSON(), // Convert Sequelize model to plain object
                    offer_price_per_caret: ((row?.offer_price ?? 0) / (stock?.weight ?? 0)).toFixed(2),
                    updated_offer_price_per_caret: ((row?.updated_offer_price ?? 0) / (stock?.weight ?? 0)).toFixed(2),
                    last_offer_price_per_caret: ((row?.last_offer_price ?? 0) / (stock?.weight ?? 0)).toFixed(2),
                    updated_last_offer_price_per_caret: (
                        (row?.updated_last_offer_price ?? 0) / (stock?.weight ?? 0)
                    ).toFixed(2)
                };
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User offer trails listed successfully`,
                data: updatedRows
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/offer-details
     * @apiName OfferDetails
     * @apiGroup OfferBuyRequest
     *
     *
     * @apiSuccess {Object} OfferTrails.
     */
    async listOfferDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list user offer details');

        try {
            const id = req[`id`];
            const role = req[`role`];
            const offerId = req.query.offer_id;
            const conditions: any[] = [];

            if (!offerId) {
                throw new Error('Offer id required!!');
            }

            conditions.push({ id: offerId });

            if (role === adminRole.user) {
                conditions.push({ user_id: id });
            } else if (role === adminRole.vendor) {
                conditions.push({ vendor_id: id });
            } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                // conditions.push({ admin_id: id });
            }

            /// fetch offer
            const offer: any = await models.stock_offers.findOne({
                where: { [Op.and]: conditions },
                include: [
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'price_per_caret',
                            'final_price',
                            'sku_number',
                            'sku_prefix',
                            'lab',
                            'diamond_type',
                            'growth_type',
                            'is_lab_grown',
                            'admin_id',
                            'vendor_id',
                            'certificate_number',
                            'stock_margin'
                        ]
                    }
                ]
            });

            if (!offer) {
                throw new Error('Offer not found!!');
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User offer details listed successfully`,
                data: offer
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /// reject offers cron
    async rejectOffers() {
        logger.info('!!!Calling get reject offers cron');

        const transaction = await sequelize.transaction();

        try {
            logger.info('Fetching outdated offers');
            const outDatedOffers: any = await models.stock_offers.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [
                                {
                                    [Op.and]: [{ status: OfferStatus.accepted }, { order_id: { [Op.eq]: null } }]
                                },
                                { status: { [Op.in]: [OfferStatus.pending, OfferStatus.revised] } }
                            ]
                        },
                        {
                            updatedAt: {
                                [Op.lte]: sequelize.literal(
                                    `NOW() - INTERVAL '${process.env.OFFER_VALIDITY || 10} days'`
                                )
                            }
                        }
                    ]
                }
            });

            if (!outDatedOffers.length) {
                return;
            }

            /// outdated offer buy requests
            const outdatedOfferBuyRequestIds = outDatedOffers
                .filter((offer: any) => offer?.status === buyRequestStatus.accepted)
                .filter((item: any) => item?.buy_request_id)
                .map((data: any) => data.buy_request_id);

            if (outdatedOfferBuyRequestIds.length) {
                logger.info('Updating offer, offer trails in buy requests from reject cron');
                /// update buy request cancelled
                await models.buy_requests.update(
                    { status: buyRequestStatus.autoCanceled },
                    { where: { id: { [Op.in]: outdatedOfferBuyRequestIds } }, transaction }
                );

                const outdatedOfferBuyRequests = await models.buy_requests.findAll({
                    where: { id: { [Op.in]: outdatedOfferBuyRequestIds } },
                    transaction
                });

                /// extract stock ids
                const outDatedStockIds = outdatedOfferBuyRequests
                    .filter((item: any) => item?.stock_ids?.length)
                    .map((data: any) => data?.stock_ids[0]?.stock_id);

                /// make stocks available
                await models.stocks.update(
                    { status: stockStatus.available },
                    {
                        where: { id: { [Op.in]: outDatedStockIds } },
                        transaction
                    }
                );

                const outDatedBuyRequestTrail = outdatedOfferBuyRequests.map((outDatedBuyRequest: any) => ({
                    user_id: outDatedBuyRequest?.user_id,
                    buy_request_id: outDatedBuyRequest?.id,
                    status: outDatedBuyRequest?.status,
                    updated_by_id: outDatedBuyRequest?.updated_by_id,
                    payload: JSON.stringify(outDatedBuyRequest)
                }));

                /// create buy request trails
                await models.buy_request_trails.bulkCreate(outDatedBuyRequestTrail, { transaction });
            }

            /// update stock offer
            await models.stock_offers.update(
                { status: OfferStatus.rejected, is_completed: true, is_response_required: false },
                {
                    where: {
                        id: { [Op.in]: outDatedOffers.map((offer: any) => offer.id) }
                    },
                    transaction
                }
            );

            /// set old trails response required to false
            await models.stock_offer_trails.update(
                { is_response_required: false },
                { where: { offer_id: { [Op.in]: outDatedOffers.map((offer: any) => offer.id) } }, transaction }
            );

            const updatedOutDatedOffers: any = await models.stock_offers.findAll({
                where: {
                    id: { [Op.in]: outDatedOffers.map((offer: any) => offer.id) }
                },
                attributes: { exclude: ['createdAt', 'updatedAt'] },
                transaction
            });

            const outDatedOfferTrailsObject = updatedOutDatedOffers.map((outDatedOffer: any) => {
                const outDatedOfferObject: any = JSON.parse(JSON.stringify(outDatedOffer));
                delete outDatedOfferObject.id;
                return {
                    ...outDatedOfferObject,
                    offer_id: outDatedOffer.id,
                    payload: JSON.stringify(outDatedOffer)
                };
            });

            /// bulk create offer trails
            await models.stock_offer_trails.bulkCreate(outDatedOfferTrailsObject, { transaction });

            await transaction.commit();

            try {
                /// notify users for offer rejected
                for (const offer of updatedOutDatedOffers) {
                    userNotification.sendOfferAcceptedRejectedNotification(NotificationType.offerRejected, offer.id);
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
        }
    }
}

export default new StockOffers();
