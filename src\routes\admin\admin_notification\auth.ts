import { IRouter, Router } from 'express';
import admin_notifications from '../../../controllers/admin/admin_notifications';

const routes: IRouter = Router();

routes.get('/notification', admin_notifications.getAdminNotification);

routes.get('/notification-unread-count', admin_notifications.getAdminNotificationUnreadCount);

routes.put('/notification-read', admin_notifications.markAsReadAdminNotification);

export default routes;
