import { Request, Response, NextFunction } from 'express';
import _ from 'lodash';
import { logger } from '../utils/logger';
import jwt from 'jsonwebtoken';
import { TOKEN, adminRole, httpStatusCodes } from '../utils/constants';
import models from '../models';
import * as console from 'console';
import { decryptBody } from '../utils/encrypt';

type DecodedType = {
    role: string;
    id: string;
};

export async function tokenHandler(req: Request, res: Response, next: NextFunction) {
    try {
        // matching null if user dont have saved the token in browser in that case it will be string type of null value
        const authorizedTokenString =
            (req.body && req.body.access_token) || (req.query && req.query.access_token) || req.headers.authorization;
        if (!authorizedTokenString) {
            res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                status: httpStatusCodes.UNAUTHORIZED_CODE,
                message: httpStatusCodes.UNAUTHORIZED_URL
            });

            return;
        }

        if (!(authorizedTokenString && authorizedTokenString.split(' ')[1])) {
            res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                status: httpStatusCodes.UNAUTHORIZED_CODE,
                message: httpStatusCodes.UNAUTHORIZED_URL
            });

            return;
        }

        const accessToken = authorizedTokenString.split(' ')[1];

        const decodedData: DecodedType = await new Promise((resolve: any, reject: any) => {
            jwt.verify(accessToken, TOKEN, (err, decoded) => {
                if (err) {
                    logger.info(accessToken);
                    reject(err);
                }
                logger.info(accessToken);
                resolve(decoded);
            });
        });

        const entityId = decodedData.id;
        const role = decodedData.role;
        req[`role`] = role;
        req[`id`] = entityId;

        if ([adminRole.subAdmin, adminRole.superAdmin].includes(role as adminRole)) {
            const adminData = await models.admins.findOne({
                where: {
                    id: decodedData.id
                },
                attributes: { exclude: ['password', 'otp'] }
            });

            if (!adminData) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'Admin not found'
                });

                return;
            } else if (!adminData.isActive) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'Admin is not active'
                });

                return;
            }

            req[`admin`] = adminData;
            next();
        } else if (role === adminRole.vendor) {
            const vendorData = await models.vendors.findOne({
                where: {
                    id: decodedData.id
                },
                attributes: { exclude: ['password', 'otp'] }
            });

            if (!vendorData) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'Vendor not found'
                });

                return;
            } else if (vendorData.is_blacklisted) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'Vendor black listed'
                });

                return;
            } else if (!vendorData.is_active) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'Vendor is not active'
                });

                return;
            }

            if (vendorData?.ftp_password) {
                const decryptedPassword = await decryptBody(vendorData?.ftp_password);
                vendorData.ftp_password = decryptedPassword;
            }

            req[`vendor`] = vendorData;
            next();
        } else if (role === adminRole.user) {
            const userData = await models.users.findOne({
                where: {
                    id: decodedData.id
                },
                attributes: { exclude: ['password', 'otp', 'fcm_token'] }
            });

            if (!userData) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'User not found'
                });

                return;
            } else if (!userData.is_active) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'User is not active'
                });

                return;
            } else if (userData.is_blocked) {
                res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                    status: httpStatusCodes.UNAUTHORIZED_CODE,
                    message: 'User is blocked'
                });

                return;
            }

            req[`user`] = userData;
            next();
        } else {
            res.status(httpStatusCodes.UNAUTHORIZED_CODE).json({
                status: httpStatusCodes.UNAUTHORIZED_CODE,
                message: httpStatusCodes.UNAUTHORIZED_URL
            });

            return;
        }
    } catch (error: any) {
        res.json({
            status: httpStatusCodes.TOKEN_EXPIRED_CODE,
            message: httpStatusCodes.TOKEN_EXPIRED,
            error
        });
        return;
    }
}

export default tokenHandler;
