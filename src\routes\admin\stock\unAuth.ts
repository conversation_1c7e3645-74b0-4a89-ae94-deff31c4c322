import { Router, IRouter, Request, Response, NextFunction } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import stock from '../../../controllers/admin/stock/stock';
import { createStockFromApiSchema } from '../../../utils/schema/stock_schema';

const router: IRouter = Router();

// add stock from api
// router.post(
//     '/stock-from-api',
//     (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createStockFromApiSchema),
//     stock.addStockFromApi
// );
router.post(
    '/stock-from-api',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createStockFromApiSchema),
    stock.addStockFromFilePath
);


router.get(
    '/all-stocks-csv',
    stock.getAllStocksCsv
);

export default router;
