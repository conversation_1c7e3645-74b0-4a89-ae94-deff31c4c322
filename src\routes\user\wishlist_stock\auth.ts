import { IRouter, Router } from 'express';
import wishlist_stock from '../../../controllers/user/wishlist_stock';
import { fieldsValidator } from '../../../middlewares/validator';
import { addWishlistStockSchema } from '../../../utils/schema/wishlist_schema';
const routes: IRouter = Router();

routes.get('/wishlist', wishlist_stock.getWishlistStocks);

routes.post(
    '/wishlist',
    (req, res, next) => fieldsValidator(req, res, next, addWishlistStockSchema),
    wishlist_stock.addToWishlistStocks
);

export default routes;
