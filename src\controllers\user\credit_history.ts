import { NextFunction, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import models from '../../models';
import { httpStatusCodes } from '../../utils/constants';

class CreditHistory {
    /**
     * @api {post} /v1/auth/user/credit-history
     * @apiName CreditHistory
     * @apiGroup UserCreditHistory
     *
     *
     * @apiSuccess {Object} CreditHistory.
     */
    async addCreditHistory(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling add credit history');
        try {
            const creditLimitData = req.body;

            await models.credit_histories.create(creditLimitData);

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Credit history created successfully`
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/user-credit-history
     * @apiName CreditHistory
     * @apiGroup UserCreditHistory
     *
     *
     * @apiSuccess {Object} CreditHistory.
     */
    async getUserCreditHistory(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling get user credit history');
        try {
            const user_id = req.query.user_id;

            if (!user_id) {
                throw new Error(`user_id required`);
            }

            const { rows, count } = await models.credit_histories.findAndCountAll({ where: { user_id } });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Vendor Order updated successfully`,
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/available-credit
     * @apiName CreditHistory
     * @apiGroup UserCreditHistory
     *
     *
     * @apiSuccess {Object} CreditHistory.
     */
    async userAvailableCredit(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling get user available credit');
        try {
            const user_id = req.query.user_id;

            if (!user_id) {
                throw new Error(`user_id required`);
            }

            const totalCredit = await models.credit_histories.sum('credit', {
                where: {
                    transaction_type: 'CREDIT',
                    user_id
                }
            });

            const totalDebit = await models.credit_histories.sum('credit', {
                where: {
                    transaction_type: 'DEBIT',
                    user_id
                }
            });

            const availableCredit =
                parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
                parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Available user credit`,
                data: availableCredit
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new CreditHistory();
