import { NextFunction, Request, Response } from 'express';
import models from '../../models';
import { Op } from 'sequelize';
import { TOKEN, adminRole, httpStatusCodes } from '../../utils/constants';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { isOTPExpired } from '../../utils/validators';
import { logger } from '../../utils/logger';
import userNotifications from '../user/user_notifications/user_notification';
import mailServices from '../user/user_notifications/mail_services';
import smsServices from '../user/user_notifications/sms_services';

class User {
    /**
     * @api {post} /v1/user/register
     * @apiName registerUser
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async userRegister(req: Request, res: Response, next: NextFunction) {
        try {
            const dataObject = req.body;
            const { email, phone, password, is_address_same, billing_address, document_name, signature_image_name } =
                req.body;

            if (!email && !phone) {
                throw new Error('Email or Phone is required');
            }

            if ((is_address_same ?? false).toString().toLowerCase() === 'false' && !billing_address) {
                throw Error('billing_address required');
            }

            dataObject.is_address_same = (req.body?.is_address_same ?? false).toString().toLowerCase() === 'true';
            dataObject.has_AMLprogram = (req.body?.has_AMLprogram ?? false).toString().toLowerCase() === 'true';

            const userExists = await models.users.findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false }
            });

            if (userExists) {
                throw new Error('User already registered  with this Email or phone');
            }

            if (password) {
                const hashedPassword = await bcrypt.hash(password, 12);

                dataObject.password = hashedPassword;
            }

            if (document_name) {
                dataObject.document_url = `${process.env.BASE_URL}/user/document/${document_name}`;
            }
            if (signature_image_name) {
                dataObject.signature_image_url = `${process.env.BASE_URL}/user/signature/${signature_image_name}`;
            }

            if (email) {
                dataObject.email = email.toString().toLowerCase().trim();
            }

            const user = await models.users.create(dataObject);

            const userData = JSON.parse(JSON.stringify(user));

            delete userData.password;
            delete userData.otp;

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User registered successfully`,
                data: userData
            });

            try {
                /// send welcome email
                userNotifications.accountCreatedNotification(userData.id);
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/user/update
     * @apiName registerUser
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async updateUser(req: Request, res: Response, next: NextFunction) {
        try {
            const dataObject: any = {};
            const id = req[`id`];

            const { email, phone, is_address_same, billing_address, document_name, signature_image_name } = req.body;

            if (is_address_same === false && !billing_address) {
                throw Error('billing_address required');
            }

            const userExists = await models.users.findOne({
                where: { id, _deleted: false }
            });

            if (!userExists) {
                throw new Error('User not found');
            }

            const emailOrPhoneExists = await models.users.findOne({
                where: {
                    [Op.or]: [email ? { email } : {}, phone ? { phone } : {}],
                    id: { [Op.ne]: id },
                    _deleted: false
                }
            });

            if (emailOrPhoneExists) {
                throw new Error('User already registered  with this Email or phone');
            }

            for (const key in req.body) {
                if (req.body[key] || req.body[key] === '') {
                    dataObject[key] = req.body[key];
                }
            }

            if (document_name) {
                if (userExists?.is_verified) {
                    throw new Error(`Couldn't resubmit document, User already verified!!!`);
                }
                dataObject.document_url = `${process.env.BASE_URL}/user/document/${document_name}`;
            }
            if (signature_image_name) {
                dataObject.signature_image_url = `${process.env.BASE_URL}/user/signature/${signature_image_name}`;
            }

            if (email) {
                dataObject.email = email.toString().toLowerCase().trim();
            }

            const [affectedRows, user] = await models.users.update(dataObject, {
                where: { id },
                returning: true,
                plain: true
            });

            const userData = JSON.parse(JSON.stringify(user));

            delete userData.password;
            delete userData.otp;

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User updated successfully`,
                data: userData
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/user/login
     * @apiName loginUser
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async loginUser(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, phone, password, fcm_token } = req.body;

            if (!email && !phone) {
                throw new Error('Email or Phone is required');
            }

            const user = await models.users.findOne({
                where: {
                    [Op.or]: [email ? { email: email.toString().toLowerCase().trim() } : {}, phone ? { phone } : {}]
                }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (user.is_blocked || !user.is_active) {
                throw new Error(`User is blocked, please contact administrator.!!!`);
            }

            const isPasswordMatched = await bcrypt.compare(password, user.password);

            if (!isPasswordMatched) {
                throw new Error('Incorrect password');
            }

            await models.users.update({ fcm_token }, { where: { id: user.id } });

            const authToken = await jwt.sign({ id: user.id, role: adminRole.user }, TOKEN);

            delete user.dataValues.password;

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User loggedIn successfully`,
                data: { ...JSON.parse(JSON.stringify(user)), token: authToken }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/user/registration-status
     * @apiName loginUser
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async registrationStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { email } = req.body;

            if (!email) {
                throw new Error('Email or Phone is required');
            }

            const user: any = await models.users.findOne({
                where: { email: email.toString().toLowerCase().trim() }
            });

            if (!user) {
                throw new Error('User not found');
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User found!!`,
                data: {
                    is_password_created: user.password ? true : false,
                    email: user.email,
                    phone: user.phone,
                    is_phone_verified: user.is_phone_verified,
                    is_email_verified: user.is_email_verified
                }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/user/change-password
     *  @apiName changeUserPassword
     *  @apiGroup Users
     *
     *  @apiSuccess {Object} Password
     */
    async changeUserPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { old_password, new_password } = req.body;

            const id = req[`id`];

            const user = await models.users.findOne({
                where: { id }
            });

            if (!user) {
                throw new Error('User not found');
            }

            const isPasswordMatched = await bcrypt.compare(old_password, user.password);

            if (!isPasswordMatched) {
                throw new Error("Old password doesn't matched");
            }

            const newPassword = await bcrypt.hash(new_password, 12);

            await models.users.update(
                {
                    password: newPassword
                },
                {
                    where: { id }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Password Updated Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/user/reset-password
     *  @apiName resetPassword
     *  @apiGroup Users
     *
     *  @apiSuccess {Object} ResetPassword
     */
    async resetUserPassword(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, phone, otp, new_password } = req.body;

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            const user = await models.users.findOne({
                where: {
                    [Op.or]: [email ? { email: email.toString().toLowerCase().trim() } : {}, phone ? { phone } : {}],
                    _deleted: false
                }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (isOTPExpired(user.updatedAt)) {
                throw new Error('OTP Expired');
            }

            if (otp !== user.otp) {
                throw new Error('Incorrect OTP');
            }

            const hashPassword = await bcrypt.hash(new_password, 12);

            await models.users.update(
                {
                    password: hashPassword
                },
                {
                    where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                }
            );

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Password Updated Successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {post} /v1/user/send-verification-otp
     *  @apiName sendVerificationOtp
     *  @apiGroup Users
     *
     *  @apiSuccess {Object} VerificationOtp
     */
    async sendUserVerificationOtp(req: Request, res: Response, next: NextFunction) {
        try {
            const { phone } = req.body;
            let email = req.body.email;

            if (!email && !phone) {
                throw new Error('email or phone is required');
            }

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            const user = await models.users.findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}], _deleted: false }
            });

            if (!user) {
                throw new Error('We could not find your account');
            }

            const otp = Math.floor(100000 + Math.random() * 900000);

            await models.users.update(
                {
                    otp
                },
                {
                    where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
                }
            );

            /// is phone and email verified
            /// send OTP on email and phone both if verified
            const isPhoneEmailVerified = user?.is_email_verified && user?.is_phone_verified;

            /// isSendEmail
            /// use user.mail for forgot password
            /// use req.email for email verification
            const userEmail = isPhoneEmailVerified ? user?.email : email;

            /// isOTPSent
            let isOTPSent = false;

            if (userEmail) {
                try {
                    const emailMessage =
                        'We received a request to verify your identity. To proceed, please use the One-Time Password (OTP) code below:' +
                        `<br><br><b>OTP Code: ${otp}<b><br>`;

                    /// send mail
                    await mailServices.send({
                        to: userEmail,
                        subject: 'Your One-Time Password (OTP) Code',
                        data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
                    });
                    isOTPSent = true;
                } catch (error) {
                    logger.error(`send otp mail error ${JSON.stringify(error)}`);
                }
            }

            /// isSendPhone
            /// use user.phone for forgot password
            /// use req.phone for phone verification
            const userPhone = isPhoneEmailVerified ? user?.phone : phone;

            if (userPhone) {
                try {
                    /// add mobile and otp to req.body
                    req.body.mobile = userPhone;
                    req.body.OTP = otp;

                    /// call sendOTP function
                    await smsServices.sendOTP(req, res, next, true);

                    /// set otp sent flag
                    isOTPSent = true;
                } catch (error) {
                    logger.error(`send otp SMS error ${JSON.stringify(error)}`);
                }
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: isOTPSent ? `Verification OTP sent successfully` : 'Failed to send OTP'
                // otp
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {PUT} /v1/auth/user/change-email
     *  @apiName changeUserEmail
     *  @apiGroup Users
     *
     *  @apiSuccess {Object} ChangeEmail
     */
    async changeUserEmail(req: Request, res: Response, next: NextFunction) {
        try {
            const email = req.body.email;

            const user = req[`user`];
            const oldEmail = user.email;
            const reqId = user.id;

            const userData = await models.users.findOne({ where: { email: oldEmail } });

            if (reqId !== userData.id) {
                throw new Error('Unauthorized access');
            }

            const emailExists = await models.users.findOne({
                where: { email: email.toString().toLowerCase().trim(), id: { [Op.ne]: reqId } }
            });

            if (emailExists) {
                throw new Error('Email already exists');
            }

            await models.users.update(
                { email: email.toString().toLowerCase().trim() },
                { where: { email: oldEmail, id: reqId } }
            );

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Email has been changed successfully'
            });

            return;
        } catch (error) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/user/verify-phone
     *  @apiName changeUserVerifyPhoneStatus
     *  @apiGroup Users
     *
     *  @apiSuccess {Object} Users
     */
    async changeUserPhoneVerificationStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { phone, otp } = req.body;

            const user = await models.users.findOne({
                where: {
                    phone,
                    _deleted: false
                }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (isOTPExpired(user.updatedAt)) {
                throw new Error('OTP Expired');
            }

            if (otp !== user.otp) {
                throw new Error('Incorrect OTP');
            }

            await models.users.update({ is_phone_verified: true }, { where: { id: user.id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'phone successfully verified'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     *  @api {put} /v1/user/verify-email
     *  @apiName changeUserVerifyEmailStatus
     *  @apiGroup Users
     *
     *  @apiSuccess {Object} Users
     */
    async changeUserEmailVerificationStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, otp } = req.body;

            const user = await models.users.findOne({
                where: {
                    email: email.toString().toLowerCase().trim(),
                    _deleted: false
                }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (isOTPExpired(user.updatedAt)) {
                throw new Error('OTP Expired');
            }

            if (otp !== user.otp) {
                throw new Error('Incorrect OTP');
            }

            await models.users.update({ is_email_verified: true }, { where: { id: user.id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'email successfully verified'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/details
     * @apiName userDetails
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async userDetails(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!User details function start!!!!!');
        try {
            const id: any = req[`id`];

            const user = await models.users.findOne({
                where: { id, _deleted: false },
                attributes: { exclude: ['password', 'otp'] }
            });

            if (!user) {
                throw new Error(`User not found`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User details successfully listed',
                data: user
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/user/delete
     * @apiName deleteUser
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async deleteUser(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!User delete function start!!!!!');
        try {
            const id: any = req[`id`];

            const user = await models.users.findOne({ where: { id } });

            if (!user) {
                throw new Error(`User not found`);
            }

            await models.users.update({ _deleted: true }, { where: { id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'User deleted successfully'
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/user/email-phone-exists
     * @apiName checkEmailPhoneExists
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async checkEmailPhoneExists(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!Check email phone exists!!!!!');
        try {
            let email = req.body.email;
            const phone = req.body.phone;

            if (!email && !phone) {
                throw new Error('Email or phone required!!');
            }

            if (email) {
                email = email.toString().toLowerCase().trim();
            }

            const user = await models.users.unscoped().findOne({
                where: { [Op.or]: [email ? { email } : {}, phone ? { phone } : {}] }
            });

            const data: any = {};

            data.is_available = true;

            if (user) {
                data.is_available = false;
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Email or phone status listed`,
                data
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new User();
