import { logger } from '../../../utils/logger';
import { FirebaseController } from './firebase_controller';
import smsServices from './sms_services';
import mailServices from './mail_services';

interface CallNotificationsParams {
    token?: any;
    payload?: any;
    mobile?: string;
    to?: string;
    subject?: string;
    data?: any;
    body?: any;
    isPushNotifications?: boolean;
    isTextSMS?: boolean;
    isWhatsAppMessage?: boolean;
    isEmail?: boolean;
}

class CommonNotifications {
    // private static instance: CommonNotifications | null = null;

    // constructor() {
    //     if (!CommonNotifications.instance) {
    //         CommonNotifications.instance = this;
    //     }

    //     return CommonNotifications.instance;
    // }

    // Method to call notifications based on member variables
    public callNotifications(params: CallNotificationsParams): void {
        if (params.isPushNotifications ?? false) {
            this.sendPushNotification(params.token, params.payload);
        }

        if (params.isTextSMS ?? false) {
            // this.sendTextSMS(params.mobile, params.data);
        }

        if (params.isWhatsAppMessage ?? false) {
            // this.sendWhatsAppMessage(params.mobile, params.data);
        }

        if (params.isEmail ?? false) {
            // this.sendEmail(params.to, params.subject, params.body);
        }
    }

    // Dummy methods to simulate sending notifications
    private sendPushNotification(token: any, payload: any): void {
        logger.info('Sending push notification...');
        FirebaseController.sendToDevice(token, payload);
    }

    private sendTextSMS(mobile?: string, data?: any): void {
        logger.info('Sending SMS...');
        // smsServices.sendSMS(mobile ?? '', data);
    }

    private sendWhatsAppMessage(mobile?: string, data?: any): void {
        logger.info('Sending WhatsApp message...');
        // smsServices.sendWhatsAppMessage(mobile ?? '', data);
    }

    private sendEmail(to?: string, subject?: string, body?: any): void {
        logger.info('Sending email...');
        // mailServices.send({ to, subject, data: body });
    }
}

export default new CommonNotifications();
