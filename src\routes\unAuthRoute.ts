import adminRoute from './admin/admin/unAuth';
import subAdminRoute from './admin/subAdmin/unAuth';
import userRoutes from './user/user/unAuth';
import settingsUnAuthRoutes from './admin/settings/unAuth';
import stockRoutes from './admin/stock/unAuth';
import userBanner from './user/banner/unAuth';
import userStock from './user/stock/unAuth';
import userStaticPagesRoutes from './user/static_pages/unAuth';
import userNotificationsRoutes from './user/user_notification/unAuth';
import crmRoutes from './user/customer_relation_manager/unAuth';
import vendorRoute from './admin/vendor/unAuth';
import melleRoute from './admin/melle/unAuth';
import stockMarginRoute from './admin/stock_margin/unAuth';
import userJewelleryInquiryAuthRoutes from './user/jewellery_inquiry/unAuth';

import { Router } from 'express';
const router = Router();

/**
 * Total UnAuth Routes
 */
router.use('/admin', adminRoute);

router.use('/admin', subAdminRoute);

router.use('/admin', settingsUnAuthRoutes);

router.use('/admin', stockRoutes);

router.use('/admin', vendorRoute);

router.use('/admin', melleRoute);

router.use('/admin', stockMarginRoute);

router.use('/user', userRoutes);

router.use('/user', userBanner);

router.use('/user', userStock);

router.use('/user', userStaticPagesRoutes);

router.use('/user', crmRoutes);

router.use('/user', userJewelleryInquiryAuthRoutes);

router.use('/user', userNotificationsRoutes);

router.use('/vendor', vendorRoute);

export default router;
