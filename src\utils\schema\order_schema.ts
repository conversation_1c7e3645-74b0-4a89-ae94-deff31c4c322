import Joi from 'joi';

export const updateOrderStatusSchema = Joi.object({
    ids: Joi.array().items(Joi.string().uuid().required()).required(),
    status: Joi.string().valid('PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELED').required(),
    courier_company: Joi.string().when('status', {
        is: 'SHIPPED',
        then: Joi.required(),
        otherwise: Joi.optional()
    }),
    awb_number: Joi.string().when('status', {
        is: 'SHIPPED',
        then: Joi.required(),
        otherwise: Joi.optional()
    })
});

export const updateOrderInvoiceSchema = Joi.object({
    id: Joi.string().uuid().required(),
    file_name: Joi.string().required()
});

export const acceptVendorOrderInvoiceSchema = Joi.object({
    vendor_order_ids: Joi.array().items(Joi.string().uuid().required()).required(),
    is_accepted: Joi.boolean().required(),
    reject_reason: Joi.string().when('is_accepted', {
        is: false,
        then: Joi.string().required(),
        otherwise: Joi.string().optional()
    })
});

export const cancelOrderStatusSchema = Joi.object({
    id: Joi.string().uuid().required(),
    cancel_reason: Joi.string().required()
});
