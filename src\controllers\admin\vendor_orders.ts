import { NextFunction, Request, Response } from 'express';
import models, { sequelize } from '../../models';
import { Op, Sequelize } from 'sequelize';
import { adminRole, buyRequestType, httpStatusCodes, NotificationType, vendorOrderStatus } from '../../utils/constants';
import { logger } from '../../utils/logger';
import userNotification from '../../controllers/user/user_notifications/user_notification';
import policyViolationsService from './policy_violations';

class VendorOrders {
    /*
        --------------------------------------------------------------------------------
        Vendor Order functions
    */

    /**
     * @api {get} /v1/auth/admin/vendor-order
     * @apiName listVendorOrders
     * @apiGroup AdminVendorOrders
     *
     *
     * @apiSuccess {Object} VendorOrders.
     */
    async listVendorOrders(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list vendor order');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const sortByName = req.query.sort_by_name?.toString().toLowerCase() === 'true';
            const orderId = req.query.order_id;
            const conditions: any = [];
            const role = req[`role`];
            const reqId = req[`id`];

            if (role === adminRole.vendor) {
                conditions.push({ vendor_id: reqId });
            }

            if (orderId) {
                conditions.push({ order_id: orderId });
            }

            if (status) {
                conditions.push({ status: status.toString().toUpperCase() });
            }

            const { rows, count } = await models.vendor_orders.findAndCountAll({
                where: { [Op.and]: conditions },
                include: [
                    { model: models.orders },
                    { model: models.vendors, attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'] },
                    { model: models.stock_offers, required: false },
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'discounts_ori',
                            'price_per_caret',
                            'price_per_caret_ori',
                            'final_price',
                            'final_price_ori',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'certificate_number',
                            'stock_margin'
                        ]
                    }
                ],
                offset: skip,
                limit,
                order: [['updatedAt', 'DESC']]
            });

            /// replace final price with offer price
            for (const vendorOder of rows) {
                /// update approved margin for vendor order
                if (vendorOder?.approved_margin?.stock?.stock_margin) {
                    vendorOder.stock.stock_margin = vendorOder?.approved_margin?.stock?.stock_margin;
                }
                if (vendorOder?.approved_margin?.stock?.final_price_ori) {
                    vendorOder.stock.final_price_ori = vendorOder?.approved_margin?.stock?.final_price_ori;
                }
                if (vendorOder?.approved_margin?.stock?.price_per_caret_ori) {
                    vendorOder.stock.price_per_caret_ori = vendorOder?.approved_margin?.stock?.price_per_caret_ori;
                }

                if (vendorOder?.order?.type === buyRequestType.offer) {
                    /// fetch offer
                    const offer: any = await models.stock_offers.findOne({
                        where: { stock_id: vendorOder?.stock?.id }
                    });
                    if (offer) {
                        if (role === adminRole.vendor) {
                            vendorOder.stock.final_price = offer.updated_offer_price;
                            vendorOder.stock.final_price_ori = offer.updated_offer_price;
                            vendorOder.stock.price_per_caret = offer.updated_offer_price / vendorOder?.stock?.weight;
                            vendorOder.stock.price_per_caret_ori =
                                offer.updated_offer_price / vendorOder?.stock?.weight;
                        } else if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                            vendorOder.stock.final_price = offer.offer_price;
                            vendorOder.stock.price_per_caret = offer.offer_price / vendorOder?.stock?.weight;
                        }
                    }
                }
            }

            const resultData = sortByName
                ? rows.sort((a: any, b: any) => {
                      const firstNameComparison = a.vendor.first_name.localeCompare(b.vendor.first_name);
                      if (firstNameComparison === 0) {
                          return a.vendor.last_name.localeCompare(b.vendor.last_name);
                      }
                      return firstNameComparison;
                  })
                : rows;

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Vendor Order listed successfully`,
                data: resultData,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/vendor-order-group
     * @apiName listVendorOrderGroups
     * @apiGroup AdminVendorOrderGroups
     *
     *
     * @apiSuccess {Object} VendorOrderGroups.
     */
    async listVendorOrdersGroups(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list vendor order groups');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const status = req.query.status;
            const sortByName = req.query.sort_by_name?.toString().toLowerCase() === 'true';
            const orderId = req.query.order_id;
            const conditions: any = [];
            const role = req[`role`];
            const reqId = req[`id`];

            if (role === adminRole.vendor) {
                conditions.push({ vendor_id: reqId });
            }

            if (orderId) {
                conditions.push({ order_id: orderId });
            }

            if (status) {
                conditions.push({ status: status.toString().toUpperCase() });
            }

            /// fetch vendor orders
            const vendorOrdersData = await models.vendor_orders.findAll({
                where: {
                    [Op.and]: conditions // Your conditions
                },
                include: [
                    { model: models.vendors, attributes: ['id', 'first_name', 'last_name', 'email', 'company_name'] },
                    { model: models.stock_offers, required: false },
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'discounts_ori',
                            'price_per_caret',
                            'price_per_caret_ori',
                            'final_price',
                            'final_price_ori',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'stock_margin'
                        ]
                    }
                ],
                limit, // Pagination limit
                offset: skip // Pagination offset
            });

            const vendorOrders = sortByName
                ? vendorOrdersData.sort((a: any, b: any) => {
                      const firstNameComparison = a.vendor.first_name.localeCompare(b.vendor.first_name);
                      if (firstNameComparison === 0) {
                          return a.vendor.last_name.localeCompare(b.vendor.last_name);
                      }
                      return firstNameComparison;
                  })
                : vendorOrdersData;

            /// fetch orders
            const orders = await models.orders.findAll({
                where: {
                    id: { [Op.in]: vendorOrders.map((item: any) => item.order_id) }
                }
            });

            const groupedOrders = vendorOrders.reduce((result, vendorOrder) => {
                const orderIdKey = vendorOrder.order_id;
                if (!result[orderIdKey]) {
                    result[orderIdKey] = {
                        order: {
                            ...JSON.parse(JSON.stringify(orders.find((item: any) => item?.id === orderIdKey))),
                            vendor_orders: []
                        }
                    };
                }
                result[orderIdKey].order.vendor_orders.push(vendorOrder);
                return result;
            }, {});

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Vendor Order listed successfully`,
                data: Object.values(groupedOrders),
                count: Object.values(groupedOrders)?.length
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /*
        --------------------------------------------------------------------------------
        Update Vendor Order functions
    */

    /**
     * @api {put} /v1/auth/admin/vendor-order
     * @apiName UpdateVendorOrders
     * @apiGroup AdminVendorOrders
     *
     *
     * @apiSuccess {Object} VendorOrders.
     */
    async updateVendorOrders(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update vendor order');
        const transaction = await sequelize.transaction();
        try {
            const { ids, status, dollar_rate } = req.body;
            const reqId = req[`id`];
            const role = req[`role`];

            if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                // if (status === vendorOrderStatus.shipped) {
                //     throw new Error('Admin cannot update to shipped');
                // }
            } else if (adminRole.vendor === role) {
                if ([vendorOrderStatus.delivered, vendorOrderStatus.paid].includes(status)) {
                    throw new Error('Vendor cannot update to delivered or paid');
                }
            }

            const updateObject: any = {};

            const vendorOrders = await models.vendor_orders.findAll({ where: { id: { [Op.in]: ids } } });

            if (vendorOrders.length !== ids.length) {
                throw new Error('Vendor order not found');
            }

            /// make sure all invoices are accepted for orders
            if (vendorOrderStatus.paid === status) {
                /// order ids
                const orderIds: any = [...new Set(vendorOrders.map((item: any) => item?.order_id))];

                if (orderIds.length > 1) {
                    throw new Error(`Couldn't update for multiple orders.`);
                }

                /// vendor ids
                const vendorIds: any = [...new Set(vendorOrders.map((item: any) => item?.vendor_id))];

                if (vendorIds.length > 1) {
                    throw new Error(`Couldn't update for multiple vendors.`);
                }

                const vendorOrderCount = await models.vendor_orders.count({
                    where: { order_id: orderIds[0], vendor_id: vendorIds[0] }
                });

                if (ids.length !== vendorOrderCount) {
                    throw new Error('Please select all vendor Orders');
                }

                const isAllInvoicesAccepted = vendorOrders.every(
                    (item: any) => item?.is_invoice_action_taken && item?.is_invoice_accepted
                );
                if (!isAllInvoicesAccepted) {
                    throw new Error('Please accept all invoices before updating to paid');
                }
            }

            if (status) {
                updateObject.status = status;
            }
            if (dollar_rate) {
                updateObject.dollar_rate = dollar_rate;
            }

            await models.vendor_orders.update(updateObject, { where: { id: { [Op.in]: ids } }, transaction });

            /// fetch vendor orders to create vendor order trails
            const updatedVendorOrders = await models.vendor_orders.findAll({
                where: { id: { [Op.in]: ids } },
                transaction
            });

            /// vendor order trails items
            const vendorOrdersTrailsData: any[] = updatedVendorOrders.map((vendorOrder: any) => ({
                vendor_order_id: vendorOrder.id,
                order_id: vendorOrder.order_id,
                buy_request_id: vendorOrder.buy_request_id,
                vendor_id: vendorOrder.vendor_id,
                updated_by_id: reqId,
                payload: JSON.stringify(vendorOrder),
                status: vendorOrder.status // pending, shipped, paid
            }));

            /// create vendor order trail
            await models.vendor_order_trails.bulkCreate(vendorOrdersTrailsData, { transaction });

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Vendor Order updated successfully`
            });

            try {
                /// send vendor order email
                if (status === vendorOrderStatus.paid) {
                    userNotification.vendorOrderUpdatedNotification(NotificationType.vendorOrderPaid, ids);
                    if ([adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                        userNotification.sendPaymentCompletedEmailToVendor(NotificationType.vendorOrderPaid, ids);
                    }
                }
                //  else if (status === vendorOrderStatus.shipped) {
                //     userNotification.vendorOrderUpdatedNotification(NotificationType.vendorOrderShipped, ids);
                // }

                // Check for late shipment violations when status is updated to shipped
                if (status === vendorOrderStatus.shipped) {
                    try {
                        policyViolationsService.checkLateShipment(ids);
                    } catch (error: any) {
                        logger.error(`Error checking late shipment for vendor order: ${error}`);
                        // Don't throw error to avoid breaking the update process
                    }
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/vendor-order-invoice
     * @apiName UpdateVendorOrderInvoice
     * @apiGroup VendorOrder
     *
     *
     * @apiSuccess {Object} UpdateOrderInvoice.
     */
    async updateVendorOrderInvoice(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update vendor order invoice');

        try {
            const { id, file_name } = req.body;
            const role = req[`role`];
            const reqId = req[`id`];

            const updateObject: any = {};

            if (![adminRole.vendor].includes(role)) {
                throw new Error('unauthorized!!');
            }

            const vendorOrders: any = await models.vendor_orders.findAll({
                where: { [Op.and]: [{ order_id: id }, { vendor_id: reqId }] }
            });

            const isInvoiceAccepted: any = vendorOrders.every((item: any) => item?.is_invoice_accepted);

            if (isInvoiceAccepted) {
                throw new Error('Invoice already accepted!!!!');
            }

            if (!vendorOrders.length) {
                throw new Error('Order not found!!');
            }

            if (file_name) {
                updateObject.invoice_name = file_name;

                // updateObject.is_invoice_sent = true;

                updateObject.invoice_url = `${process.env.BASE_URL}/vendor/order/invoice/${file_name}`;

                updateObject.is_invoice_accepted = false;
                updateObject.is_invoice_action_taken = false;
            }

            /// update order status
            await models.vendor_orders.update(updateObject, {
                where: { [Op.and]: [{ order_id: id }, { vendor_id: reqId }] }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice updated successfully`,
                data: {
                    invoice_name: file_name,
                    invoice_url: updateObject.invoice_url
                }
            });

            try {
                /// invoice available notification to admin
                userNotification.sendInvoiceAvailableNotificationsToAdmin(NotificationType.invoiceAvailable, id, reqId);
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/accept-invoice
     * @apiName AcceptVendorOrderInvoice
     * @apiGroup VendorOrder
     *
     *
     * @apiSuccess {Object} UpdateOrderInvoice.
     */
    async acceptVendorOrderInvoice(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling accept vendor order invoice');
        const transaction = await sequelize.transaction();
        try {
            const { vendor_order_ids, is_accepted, reject_reason } = req.body;
            const role = req[`role`];

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('unauthorized!!');
            }

            const vendorOrders: any = await models.vendor_orders.findAll({
                where: {
                    id: { [Op.in]: vendor_order_ids }
                },
                transaction
            });

            if (!vendorOrders.length) {
                throw new Error('Vendor orders not found!!');
            }

            const isInvoiceUploaded = vendorOrders.every((item: any) => item?.invoice_url);

            if (!isInvoiceUploaded) {
                throw new Error('Invoice not found!!');
            }

            const vendorIds: any = [...new Set(vendorOrders.map((item: any) => item?.vendor_id))];

            const orderIds: any = [...new Set(vendorOrders.map((item: any) => item?.order_id))];

            /// orders
            for (const orderId of orderIds) {
                /// vendor orders
                const vendorOrdersData: any = await models.vendor_orders.findAll({
                    where: { order_id: orderId },
                    transaction
                });

                if (!vendorOrdersData.length) {
                    continue;
                }

                /// group by vendor for each order
                const groupedVendorOrders = vendorOrdersData.reduce((grouped, order) => {
                    const { vendor_id } = order;
                    if (!grouped[vendor_id]) {
                        grouped[vendor_id] = [];
                    }
                    grouped[vendor_id].push(JSON.parse(JSON.stringify(order)));
                    return grouped;
                }, {});

                /// vendor wise list for orders
                const vendorOrdersList: any[] = Object.values(groupedVendorOrders);

                for (const uniqueVendorsOrders of vendorOrdersList) {
                    /// continue if empty array
                    if (!uniqueVendorsOrders.length) {
                        continue;
                    }

                    /// check vendor id exists in requested vendorOrderIds
                    const isVendorIdExistInRequest = vendorIds.includes(uniqueVendorsOrders[0]?.vendor_id);

                    /// don't update for requested vendorsOrders ids
                    if (!isVendorIdExistInRequest) {
                        continue;
                    }

                    const isUniqueVendorsInvoiceUploaded = vendorOrders.every((item: any) => item?.invoice_url);

                    if (!isUniqueVendorsInvoiceUploaded) {
                        continue;
                    }

                    /// check already action taken
                    const checkAlreadyActionTakenOrders: any = await models.vendor_orders.findAll({
                        where: {
                            order_id: uniqueVendorsOrders[0].order_id,
                            vendor_id: uniqueVendorsOrders[0].vendor_id
                        },
                        transaction
                    });

                    const isActionTakenAlready: any = checkAlreadyActionTakenOrders.every(
                        (item: any) => item?.is_invoice_action_taken
                    );

                    if (isActionTakenAlready) {
                        continue;
                    }

                    /// update order status
                    await models.vendor_orders.update(
                        {
                            is_invoice_accepted: is_accepted,
                            is_invoice_action_taken: true,
                            reject_reason
                        },
                        {
                            where: {
                                order_id: uniqueVendorsOrders[0].order_id,
                                vendor_id: uniqueVendorsOrders[0].vendor_id
                            },
                            transaction
                        }
                    );
                }
            }

            await transaction.commit();

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice Status Changed Successfully`
            });

            try {
                /// invoice available notification
                if (String(is_accepted).toLowerCase() === 'false') {
                    userNotification.sendInvoiceAcceptedEmailToVendor(
                        NotificationType.invoiceRejected,
                        vendor_order_ids
                    );
                }
            } catch (error: any) {
                logger.error(error);
            }

            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/raise-invoice-cron
     * @apiName RaiseInvoiceCron
     * @apiGroup VendorOrder
     *
     *
     * @apiSuccess {Object} RaiseInvoiceCron.
     */
    async raiseInvoiceCron(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling raise invoice cron');

        try {
            /// fetch all vendor orders
            const outDatedVendorOrders: any = await models.vendor_orders.findAll({
                where: {
                    [Op.and]: [
                        { invoice_url: { [Op.eq]: null } },
                        {
                            createdAt: {
                                [Op.lte]: sequelize.literal(
                                    `NOW() - INTERVAL '${process.env.VENDOR_INVOICE_VALIDITY || 30} days'`
                                )
                            }
                        },
                        { status: vendorOrderStatus.delivered },
                        { email_count: { [Op.lt]: 3 } }
                    ]
                },
                include: [{ model: models.orders, attributes: ['id', 'buy_request_id'] }]
            });

            const buyRequestIds: any = [
                ...new Set(outDatedVendorOrders.map((item: any) => item?.order?.buy_request_id))
            ];

            /// send raise invoice notifications if not uploaded after 30 days
            for (const buyRequestId of buyRequestIds) {
                try {
                    userNotification.sendRaiseInvoiceEmailToVendor(NotificationType.raiseInvoice, buyRequestId);
                } catch (error: any) {
                    logger.error(error);
                }
            }

            // res.send({
            //     status: httpStatusCodes.SUCCESS_CODE,
            //     message: `Invoice updated successfully`
            // });

            // try {
            //     /// invoice available notification
            //     userNotification.sendInvoiceAvailableNotifications(NotificationType.invoiceAvailable, order.id);
            // } catch (error: any) {
            //     logger.error(error);
            // }

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new VendorOrders();
