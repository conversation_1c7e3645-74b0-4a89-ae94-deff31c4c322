import { IRouter, Router, Request, Response, NextFunction } from 'express';
import returnOrder from '../../../controllers/user/return_order';
import { createdReturnOrderSchema } from '../../../utils/schema/return_order_schema';
import { fieldsValidator } from '../../../middlewares/validator';

const routes: IRouter = Router();

// create return order
routes.post(
    '/return-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createdReturnOrderSchema),
    returnOrder.createReturnOrder
);

// list return order
routes.get('/return-order', returnOrder.listReturnOrder);

export default routes;
