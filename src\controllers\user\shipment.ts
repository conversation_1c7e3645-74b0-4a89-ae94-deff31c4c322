import { NextFunction, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import models from '../../models';
import { httpStatusCodes } from '../../utils/constants';
import { Op } from 'sequelize';

class Shipment {
    /**
     * @api {get} /v1/auth/user/shipments
     * @apiName ListShipment
     * @apiGroup Shipment
     *
     *
     * @apiSuccess {Object} Shipment.
     */
    async listShipment(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list shipments');
        try {
            const userId = req[`id`];

            const user = await models.users.findOne({ where: { id: userId }, attributes: ['country'] });

            if (!user) {
                throw new Error(`User not found`);
            }

            let shipments: any[] = [];

            const { rows } = await models.shipments.findAndCountAll({
                where: user.country
                    ? { countries: { [Op.contains]: [user.country] }, is_default: false }
                    : { is_default: true },
                order: [['createdAt', 'DESC']]
            });

            shipments = rows;

            if (shipments.length === 0) {
                const result = await models.shipments.findAndCountAll({
                    where: { is_default: true },
                    order: [['createdAt', 'DESC']]
                });
                shipments = result.rows;
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `shipments listed successfully`,
                data: shipments,
                tax: parseFloat(process.env.TAX_PERCENT || '0'),
                count: shipments.length
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Shipment();
