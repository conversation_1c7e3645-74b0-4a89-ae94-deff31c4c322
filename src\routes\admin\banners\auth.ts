import banner from '../../../controllers/admin/banner';
import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import { addBannerSchema, changeBannerStatusSchema, updateBannerSchema } from '../../../utils/schema/banner_schema';

const router: IRouter = Router();

// list banner
router.get('/banner', banner.listBanner);

// add banner
router.post(
    '/banner',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addBannerSchema),
    banner.addBanner
);

// update banner
router.put(
    '/banner',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateBannerSchema),
    banner.updateBanner
);

// change status
router.put(
    '/banner/status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeBannerStatusSchema),
    banner.changeBannerStatus
);

// delete banner
router.delete('/banner', banner.deleteBanner);

export default router;
