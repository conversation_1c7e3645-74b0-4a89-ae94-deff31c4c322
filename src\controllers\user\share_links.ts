import { NextFunction, Request, Response } from "express";
import { logger } from "../../utils/logger";
import models from "../../models";
import { adminRole, httpStatusCodes } from "../../utils/constants";


class ShareLinks {

    /**
     * @api {get} /v1/auth/user/share-links
     * @apiName getShareLinks
     * @apiGroup ShareLinks
     *
     *
     * @apiSuccess {Object} ShareLink.
     */
    async getShareLinks(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling get share links');
        try {

            /// request user role
            const role = req[`role`];

            /// redirect key
            const redirectKey = req.query.redirect_key;

            /// user allowed only
            if (adminRole.user !== role) {
                throw new Error(`You are not allowed to access this resource`);
            }

            /// if redirect key not provided
            if (!redirectKey) {
                throw new Error(`redirect_key is required`);
            }

            /// find share link
            const shareLink = await models.share_links.findOne({
                where: { redirect_key: redirectKey }
            });

            /// if share link not found
            if (!shareLink) {
                throw new Error(`Share link not found`);
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `share link retrieved successfully`,
                data: shareLink
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }


    /**
     * @api {post} /v1/auth/user/share-links
     * @apiName createShareLink
     * @apiGroup ShareLinks
     *
     * @apiParam {Object} filter_data - Filter data for the share link.
     *
     * @apiSuccess {Object} ShareLink.
     */
    async createShareLink(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling create share link');
        try {

            /// request user role
            const role = req[`role`];

            /// user allowed only
            if (adminRole.user !== role) {
                throw new Error(`You are not allowed to access this resource`);
            }

            /// filter data
            const filterData = req.body.filter_data;

            /// create share link
            const shareLink = await models.share_links.create({
                filter_data: JSON.parse(JSON.stringify(filterData))
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `share link created successfully`,
                data: shareLink
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }

    }

}


export default new ShareLinks();