<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px;
            background-color: #fff;
            border-bottom: 1px solid #ddd;
        }
        .header img {
            max-width: 120px;
        }
        .content {
            padding: 20px;
        }
        .content h1 {
            font-size: 20px;
            margin-bottom: 10px;
        }
        .content p {
            margin-bottom: 15px;
        }
        .table-container {
            margin-top: 15px;
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
        }
        .table-container th, .table-container td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .table-container th {
            background-color: #f4f4f4;
            text-align: left;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            background-color: #fff;
            border-top: 1px solid #ddd;
        }
        .footer a {
            color: #007bff;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .social-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 10px 0;
        }
        .social-icons img {
            width: 20px;
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="logo.png" alt="Nivoda Logo">
        </div>
        <div class="content">
            <h1>Payment Notification</h1>
            <p>Hi <%- payloadData.name %>,</p>
            <p>
                Diamond Company has transferred <strong><%- payloadData.amount %></strong> including adjustments made for TDS, Debit notes, and Tax invoices. 
                Please confirm receipt of funds.
            </p>
            <p>Please find details below about adjustments:</p>
            <table class="table-container">
                <tr>
                    <th>Invoice Number:</th>
                    <td><%- payloadData.invoiceNumber %></td>
                    <th>Invoice Value:</th>
                    <td><%- payloadData.invoiceValue %></td>
                </tr>
                <tr>
                    <th colspan="4">Adjustments</th>
                </tr>
                <% payloadData.adjustments.forEach(function(adjustment) { %>
                    <tr>
                        <td><%- adjustment.documentNumber %></td>
                        <td>(<%- adjustment.adjustmentAmount %>)</td>
                    </tr>
                <% }); %>
                <tr>
                    <td colspan="2"><strong>Total Adjusted Amount</strong></td>
                    <td colspan="2"><%- payloadData.totalAdjustmentAmount %></td>
                </tr>
                <tr>
                    <td colspan="2"><strong>Final Payable Amount</strong></td>
                    <td colspan="2"><%- payloadData.finalPayableAmount %></td>
                </tr>
            </table>
        </div>
        <div class="footer">
            <div class="social-icons">
                <a href="#"><img src="instagram-icon.png" alt="Instagram"></a>
                <a href="#"><img src="whatsapp-icon.png" alt="WhatsApp"></a>
                <a href="#"><img src="linkedin-icon.png" alt="LinkedIn"></a>
                <a href="#"><img src="twitter-icon.png" alt="Twitter"></a>
                <a href="#"><img src="email-icon.png" alt="Email"></a>
                <a href="#"><img src="facebook-icon.png" alt="Facebook"></a>
            </div>
            <p>
                Nivoda, Suite 202, <a href="https://goo.gl/maps/xxxxxxxxx" target="_blank">100 Hatton Garden, London, EC1N 8NX, United Kingdom</a>
            </p>
        </div>
    </div>
</body>
</html>
