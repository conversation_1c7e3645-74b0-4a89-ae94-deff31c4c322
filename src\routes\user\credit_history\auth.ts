import { addCreditHistorySchema } from '../../../utils/schema/credit_history_schema';
import creditHistory from '../../../controllers/user/credit_history';
import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';

const router: IRouter = Router();

// credit history
router.post(
    '/credit-history',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addCreditHistorySchema),
    creditHistory.addCreditHistory
);

// user credit history
router.get('/credit-history', creditHistory.getUserCreditHistory);

// user available credit
router.get('/available-credit', creditHistory.userAvailableCredit);

export default router;
