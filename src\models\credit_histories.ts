import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface CreditHistoryAttributes {
    id?: string; // id is an auto-generated UUID
    user_id: string;
    credit: number;
    type: string; // credit/debit
    transaction_type: string; // verify / create buy request / CANCEL-BUY-REQUEST
    buy_request_id: string;
    order_id: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    // Association Fields
}

interface CreditHistoryCreationAttributes extends Optional<CreditHistoryAttributes, 'id'> {}

interface CreditHistoryInstance
    extends Model<CreditHistoryAttributes, CreditHistoryCreationAttributes>,
        CreditHistoryAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type CreditHistoryStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => CreditHistoryInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const creditHistory = sequelize.define<CreditHistoryInstance>(
        'credit_history',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            credit: {
                type: DataTypes.DOUBLE,
                allowNull: false
            },
            type: {
                type: DataTypes.ENUM(
                    'VERIFY',
                    'UPDATE',
                    'CREATE-BUY-REQUEST',
                    'UPDATE-BUY-REQUEST',
                    'ORDER-DELIVERED',
                    'CANCEL-BUY-REQUEST',
                    'CREATE-ORDER',
                    'RETURN-ORDER',
                    'CANCEL-ORDER'
                ),
                allowNull: false
            },
            transaction_type: {
                type: DataTypes.ENUM('CREDIT', 'DEBIT'),
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as CreditHistoryStatic;

    // TODO: make common function to sync
    // await creditHistory.sync({ alter: true });

    return creditHistory;
};
