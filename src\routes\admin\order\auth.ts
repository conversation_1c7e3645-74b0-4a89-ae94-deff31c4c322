import vendorOrder from '../../../controllers/admin/vendor_orders';
import order from '../../../controllers/admin/orders';

import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import { updateVendorOrderSchema } from '../../../utils/schema/vendor_order_schema';
import {
    acceptVendorOrderInvoiceSchema,
    cancelOrderStatusSchema,
    updateOrderInvoiceSchema,
    updateOrderStatusSchema
} from '../../../utils/schema/order_schema';

const router: IRouter = Router();

// list orders
router.get('/order', order.listOrder);

// update vendor
router.get('/order-details', order.orderDetails);

// update order status
router.put(
    '/order-status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateOrderStatusSchema),
    order.updateOrderStatus
);

// list vendor orders
router.get('/vendor-order', vendorOrder.listVendorOrders);

// list vendor orders group by order
router.get('/vendor-order-group', vendorOrder.listVendorOrdersGroups);

// update vendor
router.put(
    '/vendor-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateVendorOrderSchema),
    vendorOrder.updateVendorOrders
);

// update vendor
router.put(
    '/vendor-order-invoice',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateOrderInvoiceSchema),
    vendorOrder.updateVendorOrderInvoice
);

// update order
router.put(
    '/update-invoice',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateOrderInvoiceSchema),
    order.updateOrderInvoice
);

// accept invoice
router.put(
    '/accept-invoice',
    (req: Request, res: Response, next: NextFunction) =>
        fieldsValidator(req, res, next, acceptVendorOrderInvoiceSchema),
    vendorOrder.acceptVendorOrderInvoice
);

// update order
router.put(
    '/cancel-order',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, cancelOrderStatusSchema),
    order.cancelOrder
);

export default router;
