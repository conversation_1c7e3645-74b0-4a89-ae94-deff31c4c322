import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface OrderAttributes {
    id?: string;
    user_id: string;
    buy_request_id: string;
    order_code: string;
    amount: string;
    payment_status: string; // pending, paid, failed, canceled
    order_status: string; // pending, processing, shipped, delivered, canceled
    delivered_on: Date;
    billing_address: object;
    shipping_address: object;
    payment_method: string;
    payment_mode: string;
    payment_details: string;
    is_paid: boolean;
    invoice_number: string;
    invoice_url: string;
    invoice_file_name: string;
    courier_company: string;
    awb_number: string;
    financial_year: string;
    is_invoice_sent: boolean;
    user_details: object;
    failure_reason: string;
    cancel_reason: string;
    shipment_id: string;
    authorize_payment_details: object;
    shipment_price: number;
    tax_price: number;
    grand_total: number;
    updated_amount: number;
    updated_grand_total: number;
    type: string;
    unified_order_type?: string;
    jewellery_array: object;
    melee_array: object;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface OrderCreationAttributes extends Optional<OrderAttributes, 'id'> {}

interface OrderInstance extends Model<OrderAttributes, OrderCreationAttributes>, OrderAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type OrderStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => OrderInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const orders = sequelize.define<OrderInstance>(
        'orders',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
            amount: {
                type: DataTypes.DOUBLE,
                allowNull: false
            },
            payment_status: {
                type: DataTypes.ENUM('PENDING', 'PAID', 'FAILED', 'CANCELED'),
                allowNull: false
            },
            order_status: {
                type: DataTypes.ENUM('PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELED'),
                allowNull: false
            },
            delivered_on: {
                type: DataTypes.DATE,
                allowNull: true
            },
            billing_address: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            shipping_address: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            payment_method: {
                type: DataTypes.STRING,
                allowNull: true
            },
            payment_details: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            is_paid: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            invoice_number: {
                type: DataTypes.STRING,
                allowNull: true
            },
            invoice_url: {
                type: DataTypes.STRING,
                allowNull: true
            },
            invoice_file_name: {
                type: DataTypes.STRING,
                allowNull: true
            },
            courier_company: {
                type: DataTypes.STRING,
                allowNull: true
            },
            awb_number: {
                type: DataTypes.STRING,
                allowNull: true
            },
            financial_year: {
                type: DataTypes.STRING,
                allowNull: true
            },
            is_invoice_sent: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            user_details: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            shipment_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            authorize_payment_details: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            shipment_price: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            tax_price: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            grand_total: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            updated_amount: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            updated_grand_total: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            jewellery_array: {
                type: DataTypes.ARRAY(DataTypes.JSONB),
                allowNull: true
            },
            melee_array: {
                type: DataTypes.ARRAY(DataTypes.JSONB),
                allowNull: true
            },
            payment_mode: {
                type: DataTypes.ENUM,
                allowNull: true,
                values: ['CREDIT_LIMIT', 'CREDIT_CARD', 'APPLE_PAY']
            },
            failure_reason: {
                type: DataTypes.STRING,
                allowNull: true
            },
            cancel_reason: {
                type: DataTypes.STRING,
                allowNull: true
            },
            type: {
                type: DataTypes.ENUM,
                defaultValue: 'NORMAL',
                values: ['NORMAL', 'OFFER']
            },
            unified_order_type: {
                type: DataTypes.ENUM,
                allowNull: true,
                values: ['DIAMONDS', 'JEWELLERY', 'MELEE']
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as OrderStatic;

    orders.associate = (models) => {
        orders.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        orders.belongsTo(models.buy_requests, {
            foreignKey: 'buy_request_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        orders.hasMany(models.vendor_orders, {
            foreignKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        orders.hasMany(models.return_orders, {
            foreignKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        orders.hasMany(models.stock_offers, {
            foreignKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    // TODO: make common function to sync
    // await orders.sync({ alter: true });

    return orders;
};
