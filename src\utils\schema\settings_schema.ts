import Joi from 'joi';
import { isPhoneNumber } from '../../utils/validators';

export const changeEmailSchema = Joi.object({
    email: Joi.string().trim().trim().required()
});

export const changePasswordSchema = Joi.object({
    // email: Joi.string().trim().email().optional(),
    // phone: Joi.string()
    //     .trim()
    //     .custom((value, helper) => isPhoneNumber(value, helper))
    //     .optional(),
    old_password: Joi.string().trim().min(6).required(),
    new_password: Joi.string().trim().min(6).required()
});

export const resetPasswordSchema = Joi.object({
    email: Joi.string().trim().email().optional(),
    phone: Joi.string()
        .trim()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional(),
    otp: Joi.number().min(6).required(),
    new_password: Joi.string().trim().min(4).required()
});

export const sendVerificationOtpSchema = Joi.object({
    email: Joi.string().trim().email().optional(),
    phone: Joi.string()
        .trim()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .optional()
});

export const sendOtpSchema = Joi.object({
    mobile: Joi.string()
        .trim()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .required()
});

export const resendOtpSchema = Joi.object({
    mobile: Joi.string()
        .trim()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .required()
});

export const verifyOtpSchema = Joi.object({
    mobile: Joi.string()
        .trim()
        .custom((value, helper) => isPhoneNumber(value, helper))
        .required(),
    otp: Joi.number().min(6).required()
});

export const updateSystemSettingsSchema = Joi.object({
    billing_details: Joi.object({
        recipients_name: Joi.string().trim().required(),
        bank_name: Joi.string().trim().required(),
        bank_account_number: Joi.string().trim().required(),
        bank_routing_number: Joi.string().trim().required(),
        bank_swift_bic_code: Joi.string().trim().required()
    })
        .min(5)
        .optional(),
    return_within: Joi.number().optional()
});
