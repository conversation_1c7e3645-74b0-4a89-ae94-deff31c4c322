import { logger } from '../../utils/logger';
import { parse } from 'json2csv';
import fs from 'fs';
import { UploadConstant } from '../../utils/upload_constant';

export const saveCsvFile = async (jsonData: any, fileName: string) => {
    logger.info(`!!!!!!!!!!saveCsvFile function started!!!!!!!!!!!!`);
    const filePathToSaveCsv = `${UploadConstant.DOWNLOAD_DIR_STOCK}/${fileName}`;
    logger.info(`!!!!!!!!!!!Saving Path!!!!!!!!!!!! ${filePathToSaveCsv}`);

    try {
        const csvData = parse(jsonData);

         const dirPath = UploadConstant.DOWNLOAD_DIR_STOCK; // Extract the directory path

         // Check if the directory exists; if not, create it
         if (!fs.existsSync(dirPath)) {
             fs.mkdirSync(dirPath, { recursive: true }); // Create directories recursively
         }

        fs.writeFileSync(filePathToSaveCsv, csvData, 'utf8');
        logger.info(`CSV file saved at ${filePathToSaveCsv}`);

        return filePathToSaveCsv;
    } catch (error) {
        logger.error('Error saving CSV file', error);
        throw error;
    }
};
