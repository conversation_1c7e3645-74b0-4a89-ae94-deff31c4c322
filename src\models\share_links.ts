import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface ShareLinksAttributes {
    id: string;
    redirect_key: string;
    filter_data: object;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface ShareLinksCreationAttributes extends Optional<ShareLinksAttributes, 'id'> { }

interface ShareLinksInstance
    extends Model<ShareLinksAttributes, ShareLinksCreationAttributes>,
    ShareLinksAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type ShareLinksStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => ShareLinksInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const share_links = sequelize.define<ShareLinksInstance>(
        'share_links',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            redirect_key: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true,
                /// defatult random number min 8 max 8
                defaultValue: () => Math.random().toString(36).substring(2, 10)
            },
            filter_data: {
                type: DataTypes.JSONB,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as ShareLinksStatic;

    // TODO: make common function to sync
    // await share_links.sync({ alter: true });

    return share_links;
};
