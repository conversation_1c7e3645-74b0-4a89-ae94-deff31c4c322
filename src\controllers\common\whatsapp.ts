import * as request from 'postman-request';

const whatsAppTemplate: Record<string, string | undefined> = {
    new_user: process.env.WHATSAPP_NEW_USER_TEMPLATE_KEY,
    kyc_verified: process.env.WHATSAPP_KYC_VERIFY_TEMPLATE_KEY,
    kyc_rejected: process.env.WHATSAPP_KYC_REJECT_TEMPLATE_KEY,
    kyc_schedule: process.env.WHATSAPP_KYC_VERIFY_SCHEDULED_TEMPLATE_KEY,
    new_inquiry: process.env.WHATSAPP_NEW_INQUIRY_TEMPLATE_KEY
};

interface TemplateData {
    [key: string]: any;
}

class Whatsapp {
    async sendWhatsappMessage(
        phone: string,
        templateName: keyof typeof whatsAppTemplate,
        templateData: TemplateData = {}
    ): Promise<any> {
        try {
            const templateId = whatsAppTemplate[templateName];
            if (!templateId) {
                throw new Error(`Template ID for '${templateName}' not found.`);
            }

            const bodyObject: Record<string, any> = {
                appkey: process.env.WHATSAPP_APP_KEY,
                authkey: process.env.WHATSAPP_AUTH_KEY,
                to: phone,
                template_id: templateId
            };

            if (Object.keys(templateData).length > 0) {
                bodyObject.variables = templateData;
            }

            const options = {
                method: 'POST',
                url: `${process.env.WHATSAPP_URL}api/create-message`,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyObject)
            };

            return await new Promise((resolve, reject) => {
                request(options, (error, response) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    resolve(response.body);
                });
            });
        } catch (error) {
            throw error; // Re-throw the error for the caller to handle
        }
    }
}

export default new Whatsapp();
