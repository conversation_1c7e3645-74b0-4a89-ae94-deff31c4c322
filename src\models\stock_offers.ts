import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface StockOfferAttributes {
    id?: string; // id is an auto-generated UUID
    user_id: string;
    stock_id: string;
    admin_id: string;
    vendor_id: string;
    offer_price: number;
    last_offer_price: number;
    updated_last_offer_price: number;
    updated_offer_price: number;
    buy_request_id: string;
    order_id: string;
    base_price: number;
    base_price_vendor: number;
    vendor_margin: number;
    status: string; // PENDING | ACCEPTED | REJECTED
    last_offer_id: string;
    last_action_id: string;
    last_action_type: string; // USER | VENDOR | ADMIN
    is_response_required: boolean;
    is_completed: boolean;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    // Association Fields
}

interface StockOfferCreationAttributes extends Optional<StockOfferAttributes, 'id'> {}

interface StockOfferInstance extends Model<StockOfferAttributes, StockOfferCreationAttributes>, StockOfferAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type StockOfferStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => StockOfferInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const stock_offers = sequelize.define<StockOfferInstance>(
        'stock_offers',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            last_offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            updated_last_offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            updated_offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            base_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            base_price_vendor: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            vendor_margin: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            status: {
                type: DataTypes.ENUM('PENDING', 'ACCEPTED', 'REJECTED', 'REVISED'),
                defaultValue: 'PENDING',
                allowNull: false
            },
            last_offer_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            last_action_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            last_action_type: {
                type: DataTypes.ENUM('USER', 'VENDOR', 'ADMIN'),
                allowNull: false
            },
            is_response_required: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_completed: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as StockOfferStatic;

    stock_offers.associate = (models) => {
        stock_offers.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        stock_offers.belongsTo(models.stocks, {
            foreignKey: 'stock_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        stock_offers.belongsTo(models.vendors, {
            foreignKey: 'vendor_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        stock_offers.belongsTo(models.buy_requests, {
            foreignKey: 'buy_request_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        stock_offers.belongsTo(models.orders, {
            foreignKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        stock_offers.hasOne(models.vendor_orders, {
            foreignKey: 'order_id',
            sourceKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    // TODO: make common function to sync
    // await stock_offers.sync({ alter: true });

    return stock_offers;
};
