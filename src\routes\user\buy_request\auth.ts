import { IRouter, NextFunction, Request, Response, Router } from 'express';
import buyRequest from '../../../controllers/user/buy_request/buy_request';
import marginApproval from '../../../controllers/user/buy_request/margin_approval';
import buyRequestTrail from '../../../controllers/user/buy_request/buy_request_trail';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    createBuyRequestSchema,
    createNewBuyRequestSchema,
    marginApprovalSchema,
    updateBuyRequestSchema,
    vendorStockApprovalSchema
} from '../../../utils/schema/buy_request_schema';

const routes: IRouter = Router();

// create buy request
routes.post(
    '/buy-request',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createBuyRequestSchema),
    buyRequest.createBuyRequest
);

// create new buy request
routes.post(
    '/new-buy-request',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createNewBuyRequestSchema),
    buyRequest.createNewBuyRequest
);

// update buy request
routes.put(
    '/buy-request',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateBuyRequestSchema),
    buyRequest.updateBuyRequest
);

// list buy request
routes.get('/buy-request', buyRequest.listBuyRequest);

// list vendor buy request
routes.get('/vendor-buy-request', buyRequest.listVendorBuyRequest);

// vendor stock approval
routes.post(
    '/vendor-stock-approval',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, vendorStockApprovalSchema),
    buyRequest.vendorStockApproval
);

// buy request trail
routes.get('/buy-request-trail', buyRequestTrail.listTrailsForBuyRequest);

// buy request details
routes.get('/buy-request-details', buyRequest.buyRequestDetails);

// buy request margin approval
routes.put(
    '/buy-request/margin-approval',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, marginApprovalSchema),
    marginApproval.approveMarginBuyRequest
);

export default routes;
