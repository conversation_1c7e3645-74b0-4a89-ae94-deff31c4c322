import { NextFunction, Request, Response } from 'express';
import models from '../../../models';
import {
    NotificationMessage,
    NotificationType,
    ReturnedByType,
    adminRole,
    getNotificationMessage,
    httpStatusCodes,
    stockStatus,
    vendorOrderStatus
} from '../../../utils/constants';
import { UserNotificationAttributes } from '../../../models/user_notifications';
import { Op, Sequelize } from 'sequelize';
import { logger } from '../../../utils/logger';
import mailServices from './mail_services';
import commonNotifications from './common_notifications';
import { from } from 'form-data';

class UserNotification {
    /// send notification
    async sendNotification(type: string, stockIds?: any, buyRequest?: any, orders?: any) {
        logger.info('calling send notification!!');
        try {
            /// send notification on stock update
            if (type === NotificationType.stockUpdate) {
                /// stocks conditions
                const stockConditions = stockIds.map((stock: any) => {
                    return {
                        [Op.and]: [
                            { stock_id: stock?.stock_id },
                            stock?.vendor_id ? { vendor_id: stock?.vendor_id } : {},
                            stock?.admin_id ? { admin_id: stock?.admin_id } : {}
                        ]
                    };
                });

                /// stocks
                const stocks = await models.stocks.findAll({
                    where: {
                        [Op.or]: stockConditions
                    },
                    attributes: ['id', 'stock_id', 'status', 'final_price', 'vendor_id', 'admin_id']
                });

                /// get users from wish list table
                const allWishlistUsers: any = await models.wishlist_stocks.findAll({
                    where: {
                        [Op.or]: stockConditions
                    },
                    attributes: ['id', 'stock_id', 'user_id', 'final_price', 'status']
                });

                /// unique users list
                const uniqueUsersList = [...new Set(allWishlistUsers.map((user: any) => user?.user_id))];

                /// get users fcm tokens
                const allUsersList: any[] = await models.users.findAll({
                    where: { id: { [Op.in]: uniqueUsersList } },
                    attributes: ['id', 'fcm_token', 'email']
                });

                /// iterate all stock ids
                for (const stock of stocks) {
                    /// fcm lists
                    const priceIncreasedList: any[] = [];
                    const priceDecreasedList: any[] = [];
                    const holdStatusList: any[] = [];
                    const availableStatusList: any[] = [];
                    const soldStatusList: any[] = [];

                    /// get users from wish list table
                    const wishlistUsers: any = allWishlistUsers.filter(
                        (item: any) => item?.stock_id === stock?.stock_id
                    );

                    if (!wishlistUsers?.length) {
                        continue;
                    }

                    /// extract fcm and user id
                    for (const wishlistUser of wishlistUsers) {
                        const user: any = allUsersList.find((userData: any) => userData.id === wishlistUser.user_id);
                        if (user?.fcm_token) {
                            /// check price increased
                            if (
                                parseFloat(parseFloat(stock.final_price).toFixed(2)) >
                                parseFloat(parseFloat(wishlistUser.final_price).toFixed(2))
                            ) {
                                priceIncreasedList.push({
                                    fcm_token: user.fcm_token,
                                    user_id: user.id,
                                    final_price: stock.final_price,
                                    wish_list_id: wishlistUser.id,
                                    stock_id: stock.stock_id
                                });
                            }
                            /// check price decreased
                            else if (
                                parseFloat(parseFloat(stock.final_price).toFixed(2)) <
                                parseFloat(parseFloat(wishlistUser.final_price).toFixed(2))
                            ) {
                                priceDecreasedList.push({
                                    fcm_token: user.fcm_token,
                                    user_id: user.id,
                                    final_price: stock.final_price,
                                    wish_list_id: wishlistUser.id,
                                    stock_id: stock.stock_id
                                });
                            }

                            /// check status change
                            if (wishlistUser.status !== stock.status) {
                                /// available
                                if (stock.status === stockStatus.available) {
                                    availableStatusList.push({
                                        fcm_token: user.fcm_token,
                                        user_id: user.id,
                                        status: stock.status,
                                        wish_list_id: wishlistUser.id,
                                        stock_id: stock.stock_id
                                    });
                                }
                                /// hold
                                else if (stock.status === stockStatus.hold) {
                                    holdStatusList.push({
                                        fcm_token: user.fcm_token,
                                        user_id: user.id,
                                        status: stock.status,
                                        wish_list_id: wishlistUser.id,
                                        stock_id: stock.stock_id
                                    });
                                }
                                /// sold
                                else if (stock.status === stockStatus.sold) {
                                    soldStatusList.push({
                                        fcm_token: user.fcm_token,
                                        user_id: user.id,
                                        status: stock.status,
                                        wish_list_id: wishlistUser.id,
                                        stock_id: stock.stock_id
                                    });
                                }
                            }
                        }
                    }

                    //// send price increased notification
                    if (priceIncreasedList.length) {
                        this.sendStockPriceStatusNotification(
                            NotificationType.stockPriceIncreased,
                            priceIncreasedList,
                            NotificationMessage.stockPriceIncreased,
                            stock
                        );
                    }

                    //// send price decreased notification
                    if (priceDecreasedList.length) {
                        this.sendStockPriceStatusNotification(
                            NotificationType.stockPriceDecreased,
                            priceDecreasedList,
                            NotificationMessage.stockPriceDecreased,
                            stock
                        );
                    }

                    //// hold status changed notification
                    if (holdStatusList.length) {
                        this.sendStockPriceStatusNotification(
                            NotificationType.stockHold,
                            holdStatusList,
                            NotificationMessage.stockOnHold,
                            stock
                        );
                    }

                    //// available status changed notification
                    if (availableStatusList.length) {
                        this.sendStockPriceStatusNotification(
                            NotificationType.stockAvailable,
                            availableStatusList,
                            NotificationMessage.stockIsAvailable,
                            stock
                        );
                    }

                    //// sold status changed notification
                    if (soldStatusList.length) {
                        this.sendStockPriceStatusNotification(
                            NotificationType.stockSold,
                            soldStatusList,
                            NotificationMessage.stockSoldOut,
                            stock
                        );
                    }
                }
            }
            /// send notification on buy request updated
            else if (type === NotificationType.buyRequestUpdated || type === NotificationType.buyRequestRejected) {
                /// send push notification
                /// fetch user token
                const user: any = await models.users.findOne({
                    where: { id: buyRequest?.user_id },
                    attributes: ['id', 'fcm_token', 'phone']
                });

                /// notification object
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message: getNotificationMessage(type),
                    payload: { buy_request_id: buyRequest?.id }
                };

                if (user?.fcm_token) {
                    /// send to device
                    // FirebaseController.sendToDevice(user?.fcm_token, {
                    //     notification: userNotificationObject
                    // });

                    commonNotifications.callNotifications({
                        isPushNotifications: true,
                        isTextSMS: true,
                        isWhatsAppMessage: true,
                        token: user?.fcm_token,
                        payload: {
                            notification: userNotificationObject
                        },
                        mobile: user.phone,
                        data: `${getNotificationMessage(type)}`
                    });

                    /// create entries in user_notification table
                    models.user_notifications.create({
                        ...userNotificationObject,
                        user_id: user.id,
                        payload: JSON.stringify(userNotificationObject.payload)
                    });
                }

                //// send email
                try {
                    const userData: any = await models.users.findOne({
                        where: { id: buyRequest?.user_id },
                        attributes: ['email', 'first_name', 'last_name']
                    });

                    let emailMessage: string = '';

                    if (type === NotificationType.buyRequestUpdated) {
                        emailMessage = `We are pleased to inform you that your buy request has been updated!!<br><br><b>Order Code: ${buyRequest?.order_code}</b><br><br>`;
                    } else if (type === NotificationType.buyRequestRejected) {
                        emailMessage = `We regret to inform you that your buy request has been rejected!!<br><br><b>Order Code: ${buyRequest?.order_code}</b><br><br>`;
                    }

                    /// send mail
                    mailServices.send({
                        to: userData.email,
                        subject: getNotificationMessage(type),
                        data: { name: `${userData.first_name} ${userData.last_name}`, message: emailMessage }
                    });

                    ///
                } catch (e) {
                    logger.error(e);
                }
            }
            /// send notification on order updated
            else if (
                type === NotificationType.orderCreated ||
                type === NotificationType.orderCanceled ||
                type === NotificationType.orderDelivered ||
                type === NotificationType.orderShipped
            ) {
                /// order created and buy request accepted
                if (type === NotificationType.orderCreated) {
                    /// fetch order using buy request
                    const orderData: any = await models.orders.findOne({ where: { buy_request_id: buyRequest.id } });

                    if (orderData) {
                        orders = [orderData];
                    }
                }

                /// iterate all orders
                for (const order of orders) {
                    if ([NotificationType.orderCreated, NotificationType.orderCanceled, NotificationType.orderShipped].includes(type)) {
                        /// sen push notification
                        try {
                            /// get users from wish list table
                            const user: any = await models.users.findOne({
                                where: { id: order.user_id },
                                attributes: ['id', 'fcm_token', 'phone']
                            });

                            /// notification object
                            const userNotificationObject: UserNotificationAttributes = {
                                notification_type: type,
                                title: getNotificationMessage(type),
                                message: getNotificationMessage(type),
                                payload: { order_id: order.id }
                            };

                            if (user?.fcm_token) {
                                /// send to device
                                // FirebaseController.sendToDevice(user?.fcm_token, {
                                //     notification: userNotificationObject
                                // });

                                commonNotifications.callNotifications({
                                    isPushNotifications: true,
                                    token: user?.fcm_token,
                                    payload: {
                                        notification: userNotificationObject
                                    },
                                });

                                /// create entries in user_notification table
                                models.user_notifications.create({
                                    ...userNotificationObject,
                                    user_id: user.id,
                                    payload: JSON.stringify(userNotificationObject.payload)
                                });
                            }
                        } catch (e) {
                            logger.error(e);
                        }
                    }

                    //// send email for canceled, delivered and shipped orders
                    if ([NotificationType.orderCanceled, NotificationType.orderDelivered, NotificationType.orderShipped].includes(type)) {
                        try {
                            const user: any = await models.users.findOne({
                                where: { id: order.user_id },
                                attributes: ['email', 'first_name', 'last_name']
                            });

                            let emailMessage: string = '';

                            if (type === NotificationType.orderCreated) {
                                emailMessage = `We are pleased to inform you that your order has been created!<br><br><b>Order Code: ${order?.order_code}</b><br><br>`;
                            } else if (type === NotificationType.orderCanceled) {
                                emailMessage = `We regret to inform you that your order has been cancelled!!<br><br><b>Order Code: ${order?.order_code}</b><br><br>`;
                            } else if (type === NotificationType.orderDelivered) {
                                emailMessage = `We are pleased to inform you that your order has been delivered!!<br><br><b>Order Code: ${order?.order_code}</b><br><br>`;
                            } else if (type === NotificationType.orderShipped) {
                                emailMessage = `We are pleased to inform you that your order has been shipped!!<br><br><b>Order Code: ${order?.order_code}</b><br><br><b>Courier Company: ${order?.courier_company}</b><br><br><b>AWB No/Tracking number: ${order?.awb_number}</b><br><br>`;
                            }

                            if (user?.email) {
                                /// send mail
                                mailServices.send({
                                    to: user.email,
                                    subject: getNotificationMessage(type),
                                    data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
                                });
                            }

                            ///
                        } catch (e) {
                            logger.error(e);
                        }
                    }
                }
            }

            ////
        } catch (error) {
            logger.error(error);
        }
    }

    /// common function calling from sendNotification
    async sendStockPriceStatusNotification(type: any, userList: any[], message: string, stock: any) {
        /// send push notifications
        try {
            /// notification object
            const userNotificationObject: UserNotificationAttributes = {
                notification_type: type,
                title: getNotificationMessage(type),
                message,
                payload: {
                    id: stock?.id,
                    stock_id: stock?.stock_id,
                    vendor_id: stock?.vendor_id,
                    admin_id: stock?.admin_id,
                    final_price: userList[0]?.final_price,
                    status: userList[0]?.status
                }
            };

            /// send to device
            // FirebaseController.sendToDevice(
            //     userList.map((item: any) => item.fcm_token),
            //     {
            //         notification: userNotificationObject
            //     }
            // );

            commonNotifications.callNotifications({
                isPushNotifications: true,
                token: userList.map((item: any) => item.fcm_token),
                payload: {
                    notification: userNotificationObject
                }
            });

            /// create entries in user_notification table
            models.user_notifications.bulkCreate(
                userList.map((item: any) => ({
                    ...userNotificationObject,
                    user_id: item.user_id,
                    payload: JSON.stringify(userNotificationObject.payload)
                }))
            );
            /// update whish list entries
            for (const user of userList) {
                const updatedWishlist: any = {};

                /// final price
                if (user?.final_price) {
                    updatedWishlist.final_price = user.final_price;
                }
                /// status
                else if (user?.status) {
                    updatedWishlist.status = user.status;
                }
                /// updated new price and status
                models.wishlist_stocks.update(updatedWishlist, { where: { id: user.wish_list_id } });
            }
        } catch (e) {
            logger.error(`stock price status notification error ${JSON.stringify(e, null, 2)}`);
        }

        //////////// send stock updated email
        try {
            /// users
            const users: any = await models.users.findAll({
                where: { id: { [Op.in]: userList.map((item: any) => item.user_id) } },
                attributes: ['email', 'first_name', 'last_name']
            });

            let notificationMessage: string = 'Stock updated';
            const price = userList[0]?.final_price;

            if (type === NotificationType.stockPriceIncreased) {
                notificationMessage =
                    `We wanted to inform you that the price of the stock with ID <b>${userList[0]?.stock_id}</b> has increased.` +
                    `The new price is <b>$${parseFloat(price).toFixed(2)}</b>.`;
            } else if (type === NotificationType.stockPriceDecreased) {
                notificationMessage =
                    `We wanted to inform you that the price of the stock with ID <b>${userList[0]?.stock_id}</b> has decreased.` +
                    `The new price is <b>$${parseFloat(price).toFixed(2)}</b>.`;
            } else if (type === NotificationType.stockAvailable) {
                notificationMessage = `We are pleased to inform you that the stock with ID <b>${userList[0]?.stock_id}</b> is now available.`;
            } else if (type === NotificationType.stockHold) {
                notificationMessage = `Please note that the stock with ID <b>${userList[0]?.stock_id}</b> is currently on hold.`;
            } else if (type === NotificationType.stockSold) {
                notificationMessage = `We regret to inform you that the stock with ID <b>${userList[0]?.stock_id}</b> has been sold out.`;
            }

            if (users.length) {
                for (const user of users) {
                    mailServices.send({
                        to: user.email,
                        subject: message,
                        data: { name: `${user.first_name} ${user.last_name}`, message: notificationMessage }
                    });
                }
            }
        } catch (e) {
            logger.error(`stock price status email error ${JSON.stringify(e, null, 2)}`);
        }
    }

    /// check status change for buy request stockIds UUID
    async stockStatusChangeNotification(stockIds: any[]) {
        try {
            /// fetching stocks with stock_id
            const stocks: any = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: ['stock_id', 'vendor_id', 'admin_id']
            });

            if (stocks.length) {
                /// send notifications to users who have updated stocks in wish list
                const notifyStockObjects: any[] = stocks.map((stock: any) => {
                    if (stock?.vendor_id) {
                        return {
                            stock_id: stock.stock_id,
                            vendor_id: stock.vendor_id
                        };
                    } else if (stock?.admin_id) {
                        return {
                            stock_id: stock.stock_id,
                            admin_id: stock.admin_id
                        };
                    }
                });

                this.sendNotification(NotificationType.stockUpdate, notifyStockObjects);
            }
        } catch (e) {
            logger.error(`stock status change notification error function ${JSON.stringify(e, null, 2)}`);
        }
    }

    /// send KYC notifications
    async sendKYCNotifications(type: any, userId?: any, vendorId?: any) {
        /// send push notification
        try {
            const user: any = vendorId
                ? await models.vendors.findOne({
                    where: { id: vendorId },
                    attributes: ['fcm_token', 'kyc_reject_reason', 'phone']
                })
                : await models.users.findOne({
                    where: { id: userId },
                    attributes: ['fcm_token', 'kyc_reject_reason', 'phone']
                });

            let message: any = getNotificationMessage(type);

            if (NotificationType.KYCRejected === type) {
                message = `${user.kyc_reject_reason}`;
            }

            if (user?.fcm_token) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message,
                    payload: { user_id: userId, vendor_id: vendorId }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    isTextSMS: true,
                    isWhatsAppMessage: true,
                    token: user?.fcm_token,
                    payload: {
                        notification: userNotificationObject
                    },
                    mobile: user?.phone,
                    data: `${getNotificationMessage(type)}\n${message}`
                });

                /// create entries in user_notification table
                models.user_notifications.create({
                    ...userNotificationObject,
                    user_id: userId,
                    vendor_id: vendorId,
                    payload: JSON.stringify(userNotificationObject.payload)
                });
            }
        } catch (e) {
            logger.error(e);
        }

        /// send email
        try {
            const user: any = vendorId
                ? await models.vendors.findOne({
                    where: { id: vendorId },
                    attributes: ['email', 'first_name', 'last_name']
                })
                : await models.users.findOne({
                    where: { id: userId },
                    attributes: ['email', 'first_name', 'last_name']
                });

            let emailMessage = 'KYC updated';

            if (type === NotificationType.KYCAccepted) {
                emailMessage =
                    'We are pleased to inform you that your KYC (Know Your Customer) information has been successfully accepted';
            } else if (type === NotificationType.KYCAccepted) {
                emailMessage =
                    'We regret to inform you that your KYC (Know Your Customer) information has been rejected';
            }

            /// send mail
            mailServices.send({
                to: user.email,
                subject: getNotificationMessage(type),
                data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
            });

            ///
        } catch (e) {
            logger.error(e);
        }
    }

    /// send Credit Limit notifications
    async sendCreditLimitNotifications(type: any, userId: string) {
        /// send push notifications
        // try {
        //     const user: any = await models.users.findOne({ where: { id: userId }, attributes: ['fcm_token'] });

        //     /// get  available credit limit
        //     const totalCredit = await models.credit_histories.sum('credit', {
        //         where: {
        //             transaction_type: 'CREDIT',
        //             user_id: userId
        //         }
        //     });

        //     const totalDebit = await models.credit_histories.sum('credit', {
        //         where: {
        //             transaction_type: 'DEBIT',
        //             user_id: userId
        //         }
        //     });

        //     const availableCreditLimit =
        //         parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
        //         parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

        //     if (user?.fcm_token) {
        //         const userNotificationObject: UserNotificationAttributes = {
        //             notification_type: type,
        //             title: getNotificationMessage(type),
        //             message: `Available Credit Limit: \$${availableCreditLimit.toFixed(2)}`,
        //             payload: { user_id: userId }
        //         };
        //         /// send to device
        //         // FirebaseController.sendToDevice(user?.fcm_token, {
        //         //     notification: userNotificationObject
        //         // });

        //         commonNotifications.callNotifications({
        //             isPushNotifications: true,
        //             token: user?.fcm_token,
        //             payload: {
        //                 notification: userNotificationObject
        //             }
        //         });

        //         /// create entries in user_notification table
        //         models.user_notifications.create({
        //             ...userNotificationObject,
        //             user_id: userId,
        //             payload: JSON.stringify(userNotificationObject.payload)
        //         });
        //     }
        // } catch (e) {
        //     logger.error(e);
        // }

        // /// send email
        // try {
        //     const user: any = await models.users.findOne({
        //         where: { id: userId },
        //         attributes: ['email', 'first_name', 'last_name']
        //     });

        //     /// get  available credit limit
        //     const totalCredit = await models.credit_histories.sum('credit', {
        //         where: {
        //             transaction_type: 'CREDIT',
        //             user_id: userId
        //         }
        //     });

        //     const totalDebit = await models.credit_histories.sum('credit', {
        //         where: {
        //             transaction_type: 'DEBIT',
        //             user_id: userId
        //         }
        //     });

        //     const availableCreditLimit =
        //         parseFloat(parseFloat(totalCredit ?? 0).toFixed(2)) -
        //         parseFloat(parseFloat(totalDebit ?? 0).toFixed(2));

        //     const emailMessage = `We are pleased to inform you that your credit limit has been increased!<br><br><b>Available Credit Limit: \$${availableCreditLimit.toFixed(
        //         2
        //     )}</b><br><br>`;

        //     /// send mail
        //     mailServices.send({
        //         to: user.email,
        //         subject: getNotificationMessage(type),
        //         data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
        //     });

        //     ///
        // } catch (e) {
        //     logger.error(e);
        // }
    }

    /// send invoice available notification
    async sendInvoiceAvailableNotifications(type: any, orderId: string) {
        /// send push notification
        try {
            const order: any = await models.orders.findOne({
                where: { id: orderId },
                attributes: ['id', 'user_id', 'invoice_url', 'order_code', 'unified_order_type']
            });

            const user: any = await models.users.findOne({
                where: { id: order.user_id },
                attributes: ['fcm_token', 'phone']
            });

            if (user?.fcm_token) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message: `${order?.unified_order_type ?? 'Diamond'} Order Code #${order?.order_code}`,
                    payload: { order_id: order.id, invoice_url: order?.invoice_url }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    isTextSMS: true,
                    isWhatsAppMessage: true,
                    token: user?.fcm_token,
                    payload: {
                        notification: userNotificationObject
                    },
                    mobile: user.phone,
                    data: `${getNotificationMessage(type)}\n${order?.invoice_url}`
                });

                /// create entries in user_notification table
                models.user_notifications.create({
                    ...userNotificationObject,
                    user_id: order.user_id,
                    payload: JSON.stringify(userNotificationObject.payload)
                });
            }
        } catch (e) {
            logger.error(e);
        }

        /// send email
        // try {
        //     const order: any = await models.orders.findOne({
        //         where: { id: orderId },
        //         attributes: ['id', 'user_id', 'invoice_url']
        //     });

        //     const user: any = await models.users.findOne({ where: { id: order.user_id }, attributes: ['email'] });

        //     let emailMessage = `We are pleased to inform you that your invoice is now available for download.\n\n`;

        //     if (order?.invoice_url) {
        //         emailMessage =
        //             emailMessage +
        //             `You can download your invoice using the following link:<br><a href="${order.invoice_url}">Download Invoice</a><br><br>`;
        //     }

        //     if (user?.email) {
        //         /// send mail
        //         mailServices.send({
        //             to: user.email,
        //             subject: getNotificationMessage(type),
        //             data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
        //         });
        //     }

        //     ///
        // } catch (e) {
        //     logger.error(e);
        // }
    }

    /// send invoice available notification to admin
    async sendInvoiceAvailableNotificationsToAdmin(type: any, orderId: string, vendorId: string) {
        logger.info('send invoice available notifications to admin!!');
        /// send push notification to admin
        try {

            const order: any = await models.orders.findOne({
                where: { id: orderId },
                attributes: ['id', 'user_id', 'invoice_url', 'order_code', 'unified_order_type']
            });

            const vendorOrders: any = await models.vendor_orders.findAll({
                where: { [Op.and]: [{ order_id: orderId }, { vendor_id: vendorId }] },
                attributes: ['id', 'invoice_url']
            });

            if (!vendorOrders.length) {
                throw new Error('vendor orders not found!!!!');
            }

            const admins: any = await models.admins.findAll({
                where: { role: adminRole.superAdmin },
                attributes: ['fcm_token', 'phone', 'id']
            });

            const vendor: any = await models.vendors.findOne({
                where: { id: vendorId },
                attributes: ['fcm_token', 'phone', 'first_name', 'last_name', 'email', 'company_name']
            });

            const tokens: any[] = admins?.filter((admin: any) => admin?.fcm_token);

            if (tokens?.length) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message: `${order?.unified_order_type ?? 'Diamond'} Order Code #${order?.order_code}\nby ${vendor?.company_name}`,
                    payload: { order_id: orderId, invoice_url: vendorOrders[0]?.invoice_url }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    token: tokens,
                    payload: {
                        notification: userNotificationObject
                    },
                });

                /// create entries in user_notification table
                models.user_notifications.bulkCreate(admins.map((item: any) => ({
                    ...userNotificationObject,
                    admin_id: item?.id,
                    payload: JSON.stringify(userNotificationObject.payload),
                })));

            }
        } catch (e) {
            logger.error(e);
        }

        /// send email
        try {

            const vendorOrders: any = await models.vendor_orders.findAll({
                where: { [Op.and]: [{ order_id: orderId }, { vendor_id: vendorId }] },
                attributes: ['id', 'invoice_url']
            });

            if (!vendorOrders.length) {
                throw new Error('vendor orders not found!!!!');
            }

            const admins: any = await models.admins.findAll({
                where: { role: adminRole.superAdmin },
                attributes: ['email', 'phone', 'id', 'first_name', 'last_name',]
            });

            const vendor: any = await models.vendors.findOne({
                where: { id: vendorId },
                attributes: ['fcm_token', 'phone', 'first_name', 'last_name', 'email', 'company_name']
            });

            let emailMessage = `We are pleased to inform you that your invoice is now available from ${vendor?.company_name}.\n\n`;

            emailMessage =
                emailMessage +
                `You can download your invoice using the following link:<br><a href="${vendorOrders[0].invoice_url}">Download Invoice</a><br><br>`;


            /// send email to admin
            for (const admin of admins) {
                mailServices.send({
                    to: admin.email,
                    subject: getNotificationMessage(type),
                    data: { name: `${admin?.first_name} ${admin?.last_name}`, message: emailMessage }
                });
            }

            ///
        } catch (e) {
            logger.error(e);
        }
    }

    /// send invoice accepted email to vendor
    async sendInvoiceAcceptedEmailToVendor(type: any, vendorOrderIds: any) {
        /// send email and push notifications
        try {
            /// vendor order
            const vendorOrders: any = await models.vendor_orders.findAll({
                where: { id: { [Op.in]: vendorOrderIds } },
                attributes: ['id', 'order_id']
            });

            if (!vendorOrders.length) {
                throw new Error('vendor orders not found!!!!');
            }

            const orderIds: any = [...new Set(vendorOrders.map((item: any) => item?.order_id))];

            /// find vendor orders for each order
            const vendorOrdersDataList: any = await models.vendor_orders.findAll({
                where: { order_id: { [Op.in]: orderIds } },
                include: [
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts_ori',
                            'price_per_caret_ori',
                            'final_price_ori',
                            'diamond_type',
                            'is_lab_grown',
                            'growth_type',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'certificate_number',
                            'stock_margin'
                        ]
                    }
                ]
            });

            for (const orderId of orderIds) {
                /// find vendor orders for each order
                const vendorOrdersData: any = vendorOrdersDataList.filter(
                    (vendorOrder: any) => vendorOrder?.order_id === orderId
                );

                if (!vendorOrdersData.length) {
                    continue;
                }

                /// check all invoices accepted for order not vendor wise
                /// to prevent sending mails same vendor
                /// send mails only after all vendor orders invoices accepted including different vendors
                const isAllOrderInvoicesAccepted: boolean = vendorOrdersData.every(
                    (item: any) => item?.is_invoice_accepted && item?.is_invoice_action_taken
                );

                if (!isAllOrderInvoicesAccepted) {
                    continue;
                }

                const groupedVendorOrders = vendorOrdersData.reduce((grouped, order) => {
                    const { vendor_id } = order;
                    if (!grouped[vendor_id]) {
                        grouped[vendor_id] = [];
                    }
                    grouped[vendor_id].push(JSON.parse(JSON.stringify(order)));
                    return grouped;
                }, {});

                const vendorOrdersList: any[] = Object.values(groupedVendorOrders);

                for (const uniqueVendorsOrders of vendorOrdersList) {
                    if (!uniqueVendorsOrders.length) {
                        continue;
                    }

                    /// check invoices accepted vendor wise
                    const isAllVendorInvoicesAccepted: boolean = uniqueVendorsOrders.every(
                        (item: any) => item?.is_invoice_action_taken && item?.is_invoice_accepted
                    );

                    if (!isAllVendorInvoicesAccepted) {
                        continue;
                    }

                    /// vendor ids
                    const vendor: any = await models.vendors.findOne({
                        where: { id: uniqueVendorsOrders[0].vendor_id },
                        attributes: ['id', 'email', 'first_name', 'last_name', 'phone', 'fcm_token']
                    });

                    /// send push notifications
                    try {
                        const vendorNotificationObject: UserNotificationAttributes = {
                            notification_type: type,
                            title: getNotificationMessage(type),
                            message: `Invoice ${type === NotificationType.invoiceAccepted ? 'Accepted' : 'Rejected'
                                } for ${uniqueVendorsOrders
                                    .map((item: any) => String(item?.stock?.stock_id).toUpperCase())
                                    .join(', ')}`,
                            payload: { vendor_orders: vendorOrderIds, invoice_url: uniqueVendorsOrders[0].invoice_url }
                        };

                        if (vendor?.fcm_token) {
                            commonNotifications.callNotifications({
                                isPushNotifications: true,
                                token: vendor?.fcm_token,
                                payload: {
                                    notification: vendorNotificationObject
                                }
                            });
                        }

                        /// create entries in user_notification table
                        models.user_notifications.create({
                            ...vendorNotificationObject,
                            vendor_id: vendor?.id,
                            payload: JSON.stringify(vendorNotificationObject.payload)
                        });
                    } catch (e) {
                        logger.error(e);
                    }

                    /// send email
                    try {
                        /// email body
                        let emailMessage =
                            type === NotificationType.invoiceRejected
                                ? `<div>The following invoices are rejected, please resubmit.<br><br></div><hr><br>`
                                : `<div>The following invoices are accepted and queued for payment.<br><br>
                We have reviewed all details on the invoices and we have confirmed that all goods have been received in good order.<br><br>
                We would like to formally acknowledge acceptance of the invoices. The invoices have been queued for payment on the following dates as per the agreed payment terms.<br><br>
                </div><hr><br>`;
                        uniqueVendorsOrders.forEach((vendorOrder: any) => {
                            emailMessage += `<h2>Stock ID: ${String(vendorOrder?.stock?.stock_id).toUpperCase()}</h2>
                    <p>Uploaded on: ${new Date(vendorOrder?.updatedAt)}</p>
                    <p>Expected Payment date: ${new Date(
                                new Date().setDate(new Date(vendorOrder?.updatedAt).getDate() + 3)
                            ).toDateString()}</p>
                    <p>Total items: 1</p>
                    <p>Status: ${type === NotificationType.invoiceAccepted ? 'APPROVED' : 'DECLINED'}</p>
                    <p>Invoice amount: USD ${vendorOrder?.stock?.final_price_ori}</p><hr><br>`;
                        });

                        if (vendor?.email) {
                            /// send mail
                            mailServices.send({
                                to: vendor.email,
                                subject: NotificationType.invoiceAccepted
                                    ? 'Accepted and Queued for payment'
                                    : 'Invoices Rejected',
                                data: { name: `${vendor.first_name} ${vendor.last_name}`, message: emailMessage },
                            });
                        }

                        ///
                    } catch (e) {
                        logger.error(e);
                    }
                }
            }
        } catch (e) {
            logger.error(e);
        }
    }

    /// raise invoice email to vendor when invoice is not uploaded and order created
    async sendRaiseInvoiceEmailToVendor(type: any, buy_request_id: any) {
        /// send email and notification to vendor to raise invoice
        try {
            const buyRequest: any = await models.buy_requests.findOne({
                where: { id: buy_request_id }
            });

            if (!buyRequest) {
                throw new Error('buy request not found!!!!');
            }

            const order: any = await models.orders.findOne({
                where: { buy_request_id },
                attributes: ['id']
            });

            if (!order) {
                throw new Error('order not found!!!!');
            }

            const stockIds = buyRequest.stock_ids.map((item: any) => item?.stock_id);

            const stocks: any = await models.stocks.findAll({
                where: { id: { [Op.in]: stockIds } },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts_ori',
                    'price_per_caret_ori',
                    'final_price_ori',
                    'diamond_type',
                    'is_lab_grown',
                    'growth_type',
                    'admin_id',
                    'vendor_id',
                    'sku_number',
                    'certificate_number'
                ]
            });

            /// vendor ids
            const vendors: any = await models.vendors.findAll({ where: { id: { [Op.in]: buyRequest?.vendor_ids } } });

            for (const vendor of vendors) {
                /// vendor stocks
                const vendorStocks: any = stocks.filter((stock: any) => stock?.vendor_id === vendor?.id);

                if (!vendorStocks?.length) {
                    continue;
                }

                /// vendor stock ids
                const vendorStockIds = vendorStocks.map((item: any) => String(item?.stock_id).toUpperCase());

                /// send push notifications
                // try {
                //     const vendorNotificationObject: UserNotificationAttributes = {
                //         notification_type: type,
                //         title: getNotificationMessage(type),
                //         message: `Please raise invoice for ${vendorStockIds.join(', ')}`,
                //         payload: { stock_ids: vendorStockIds }
                //     };

                //     if (vendor?.fcm_token) {
                //         commonNotifications.callNotifications({
                //             isPushNotifications: true,
                //             token: vendor?.fcm_token,
                //             payload: {
                //                 notification: vendorNotificationObject
                //             }
                //         });
                //     }

                //     /// create entries in user_notification table
                //     models.user_notifications.create({
                //         ...vendorNotificationObject,
                //         vendor_id: vendor?.id,
                //         payload: JSON.stringify(vendorNotificationObject.payload)
                //     });

                //     ///
                // } catch (e) {
                //     logger.error(e);
                // }

                /// send email
                try {
                    /// email body
                    let emailMessage = `<div>This is a reminder to send the invoice for the following stones.</div><hr><br><br>`;
                    vendorStocks.forEach((stock: any) => {
                        emailMessage += `<h2>${String(stock?.shape).toUpperCase()} ${String(
                            stock?.weight
                        ).toUpperCase()}CT ${String(stock?.color).toUpperCase()} ${String(
                            stock?.clarity
                        ).toUpperCase()} ${String(stock?.cut).toUpperCase()} ${String(
                            stock?.polish
                        ).toUpperCase()} ${String(stock?.symmetry).toUpperCase()}</h2>
                    <p>Stock Id: ${String(stock?.stock_id).toUpperCase()}</p>
                    <p>Order Code: ${String(buyRequest?.order_code).toUpperCase()}</p>
                    <p>Certificate Number: ${String(stock?.certificate_number).toUpperCase()}</p>
                    <p>Diamond Price: \$${stock?.final_price_ori}</p><hr><br>`;
                    });

                    emailMessage += 'Please ignore this mail if invoice is already raised.<br>';

                    if (vendor?.email) {
                        /// send mail
                        mailServices.send({
                            to: vendor?.email,
                            subject: 'Reminder: Please raise an invoice for the Orders',
                            data: { name: `${vendor?.first_name} ${vendor?.last_name}`, message: emailMessage },
                        });

                        /// update sent email count in vendor orders
                        await models.vendor_orders.update(
                            { email_count: Sequelize.literal('email_count + 1') },
                            { where: { order_id: order?.id, vendor_id: vendor?.id } }
                        );
                    }

                    ///
                } catch (e) {
                    logger.error(e);
                }
            }
        } catch (e) {
            logger.error(e);
        }
    }

    /// send payment completed email to vendor when
    async sendPaymentCompletedEmailToVendor(type: any, vendorOrderIds: any) {
        /// send push notifications to vendor for payment completed
        // try {
        //     const vendorOrders: any = await models.vendor_orders.findAll({
        //         where: { id: { [Op.in]: vendorOrderIds } },
        //         include: [
        //             { model: models.orders, attributes: ['id', 'invoice_number'] },
        //             { model: models.stocks, attributes: ['id', 'stock_id'] }
        //         ]
        //     });

        //     if (!vendorOrders.length) {
        //         throw new Error(`vendor orders not found for send notifications`);
        //     }

        //     /// check all paid for vendor
        //     const isAllVendorOrderPaid: boolean = vendorOrders.every(
        //         (item: any) => item.status === vendorOrderStatus.paid
        //     );

        //     if (!isAllVendorOrderPaid) {
        //         throw new Error('All orders are not paid!!!');
        //     }

        //     const vendorNotificationObject: UserNotificationAttributes = {
        //         notification_type: type,
        //         title: 'Payment Notification',
        //         message: `Payment completed for ${vendorOrders.map((item: any) => item?.stock?.stock_id).join(', ')}`,
        //         payload: { vendor_orders: vendorOrderIds, invoice_url: vendorOrders[0].invoice_url }
        //     };

        //     const vendor: any = await models.vendors.findOne({
        //         where: { id: vendorOrders[0].vendor_id },
        //         attributes: ['id', 'email', 'first_name', 'last_name', 'phone', 'fcm_token']
        //     });

        //     if (vendor?.fcm_token) {
        //         commonNotifications.callNotifications({
        //             isPushNotifications: true,
        //             token: vendor?.fcm_token,
        //             payload: {
        //                 notification: vendorNotificationObject
        //             }
        //         });
        //     }

        //     /// create entries in user_notification table
        //     models.user_notifications.create({
        //         ...vendorNotificationObject,
        //         vendor_id: vendor?.id,
        //         payload: JSON.stringify(vendorNotificationObject.payload)
        //     });
        // } catch (e) {
        //     logger.error(e);
        // }

        /// send email to vendor for payment completed
        try {
            /// send email
            try {
                const vendorOrders: any = await models.vendor_orders.findAll({
                    where: { id: { [Op.in]: vendorOrderIds } },
                    include: [{ model: models.orders, attributes: ['id', 'invoice_number'] }]
                });

                /// check all paid for vendor
                const isAllVendorOrderPaid: boolean = vendorOrders.every(
                    (item: any) => item.status === vendorOrderStatus.paid
                );

                if (!isAllVendorOrderPaid) {
                    throw new Error('All orders are not paid!!!');
                }

                const vendorStocks: any = await models.stocks.findAll({
                    where: { id: { [Op.in]: vendorOrders.map((item: any) => item.stock_id) } },
                    attributes: [
                        'id',
                        'stock_id',
                        'status',
                        'weight',
                        'color',
                        'clarity',
                        'shape',
                        'cut',
                        'polish',
                        'symmetry',
                        'fluorescence_intensity',
                        'discounts_ori',
                        'price_per_caret_ori',
                        'final_price_ori',
                        'diamond_type',
                        'is_lab_grown',
                        'growth_type',
                        'admin_id',
                        'vendor_id',
                        'sku_number',
                        'certificate_number'
                    ]
                });

                /// vendor ids
                const vendor: any = await models.vendors.findOne({
                    where: { id: vendorOrders[0].vendor_id },
                    attributes: ['email', 'first_name', 'last_name']
                });

                /// vendor total amount
                const totalAmount = vendorStocks.reduce((sum, stock) => sum + (stock?.final_price_ori || 0), 0);

                /// email body
                let emailMessage = `<div>Diamond Company has transferred \$${totalAmount}. Please confirm receipt of funds.</div><hr><br><br>`;
                emailMessage += `<p>Invoice Number: ${String(vendorOrders[0]?.order?.invoice_number).toUpperCase()}</p>
            <p>Invoice Value: \$${totalAmount}</p>
            <p>Final Payable Amount: \$${totalAmount}</p><hr><br>`;

                if (vendor?.email) {
                    /// send mail
                    mailServices.send({
                        to: vendor?.email,
                        subject: 'Payment Notifications',
                        data: {
                            name: `${vendor?.first_name} ${vendor?.last_name}`,
                            message: emailMessage
                        },
                    });
                }

                ///
            } catch (e) {
                logger.error(e);
            }

            // const vendorOrders: any = await models.vendor_orders.findAll({
            //     where: { id: { [Op.in]: vendorOrderIds } },
            //     attributes: ['id', 'order_id']
            // });

            // if (!vendorOrders.length) {
            //     throw new Error('vendor orders not found!!!!');
            // }

            // const orderIds: any = [...new Set(vendorOrders.map((item: any) => item.order_id))];

            // for (const orderId of orderIds) {
            //     const vendorOrdersData: any = await models.vendor_orders.findAll({
            //         where: { order_id: orderId },
            //         include: [{ model: models.orders, attributes: ['id', 'invoice_number'] }]
            //     });

            //     if (!vendorOrdersData.length) {
            //         continue;
            //     }

            //     /// check all paid for order not vendor wise
            //     /// to prevent sending mails same vendor
            //     /// send mails only after all vendor orders marked as paid including different vendors
            //     // const isAllOrdersPaid: boolean = vendorOrdersData.every(
            //     //     (item: any) => item.status === vendorOrderStatus.paid
            //     // );

            //     // if (!isAllOrdersPaid) {
            //     //     continue;
            //     // }

            //     // const groupedVendorOrders = vendorOrdersData.reduce((grouped, order) => {
            //     //     const { vendor_id } = order;
            //     //     if (!grouped[vendor_id]) {
            //     //         grouped[vendor_id] = [];
            //     //     }
            //     //     grouped[vendor_id].push(JSON.parse(JSON.stringify(order)));
            //     //     return grouped;
            //     // }, {});

            //     // const vendorOrdersList: any[] = Object.values(groupedVendorOrders);

            //     // for (const uniqueVendorsOrders of vendorOrdersList) {
            //     //     /// send email
            //     //     try {
            //     //         if (!uniqueVendorsOrders.length) {
            //     //             continue;
            //     //         }

            //     //         /// check all paid for vendor
            //     //         const isAllVendorOrderPaid: boolean = uniqueVendorsOrders.every(
            //     //             (item: any) => item.status === vendorOrderStatus.paid
            //     //         );

            //     //         if (isAllVendorOrderPaid) {
            //     //             const vendorStocks: any = await models.stocks.findAll({
            //     //                 where: { id: { [Op.in]: uniqueVendorsOrders.map((item: any) => item.stock_id) } },
            //     //                 attributes: [
            //     //                     'id',
            //     //                     'stock_id',
            //     //                     'status',
            //     //                     'weight',
            //     //                     'color',
            //     //                     'clarity',
            //     //                     'shape',
            //     //                     'cut',
            //     //                     'polish',
            //     //                     'symmetry',
            //     //                     'fluorescence_intensity',
            //     //                     'discounts_ori',
            //     //                     'price_per_caret_ori',
            //     //                     'final_price_ori',
            //     //                     'diamond_type',
            //     //                     'is_lab_grown',
            //     //                     'growth_type',
            //     //                     'admin_id',
            //     //                     'vendor_id',
            //     //                     'sku_number',
            //     //                     'certificate_number'
            //     //                 ]
            //     //             });

            //     //             /// vendor ids
            //     //             const vendor: any = await models.vendors.findOne({
            //     //                 where: { id: uniqueVendorsOrders[0].vendor_id },
            //     //                 attributes: ['email', 'first_name', 'last_name']
            //     //             });

            //     //             /// vendor total amount
            //     //             const totalAmount = vendorStocks.reduce(
            //     //                 (sum, stock) => sum + (stock?.final_price_ori || 0),
            //     //                 0
            //     //             );

            //     //             /// email body
            //     //             let emailMessage = `<div>Diamond Company has transferred \$${totalAmount}. Please confirm receipt of funds.</div><hr><br><br>`;
            //     //             emailMessage += `<p>Invoice Number: ${String(uniqueVendorsOrders[0]?.order?.invoice_number).toUpperCase()}</p>
            //     //     <p>Invoice Value: \$${totalAmount}</p>
            //     //     <p>Final Payable Amount: \$${totalAmount}</p><hr><br>`;

            //     //             if (vendor?.email) {
            //     //                 /// send mail
            //     //                 mailServices.send({
            //     //                     to: vendor?.email,
            //     //                     subject: 'Payment Notifications',
            //     //                     data: {
            //     //                         name: `${vendor?.first_name} ${vendor?.last_name}`,
            //     //                         message: emailMessage
            //     //                     }
            //     //                 });
            //     //             }
            //     //         }
            //     //         ///
            //     //     } catch (e) {
            //     //         logger.error(e);
            //     //     }
            //     // }
            // }
        } catch (e) {
            logger.error(e);
        }
    }

    /// return order accepted notification
    async sendReturnOrderAcceptedNotification(type: any, returnOrderId: string) {
        /// send push notifications
        try {
            const returnOrder: any = await models.return_orders.findOne({
                where: { id: returnOrderId }
            });

            const user: any = await models.users.findOne({
                where: { id: returnOrder.user_id },
                attributes: ['fcm_token', 'phone']
            });

            if (user?.fcm_token) {
                const title: string = (returnOrder?.returned_by_type === ReturnedByType.admin) ? getNotificationMessage(NotificationType.diamondReturned) : getNotificationMessage(type);
                const message: string = type === NotificationType.returnOrderRejected
                    ? returnOrder?.reject_reason
                    : (returnOrder?.returned_by_type === ReturnedByType.admin) ? getNotificationMessage(NotificationType.diamondReturned) : getNotificationMessage(type);

                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title,
                    message,
                    payload: { return_order_id: returnOrder.id, order_id: returnOrder.order_id }
                };

                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    isTextSMS: true,
                    isWhatsAppMessage: true,
                    token: user?.fcm_token,
                    payload: {
                        notification: userNotificationObject
                    },
                    mobile: user.phone,
                    data: `${getNotificationMessage(type)}`
                });

                /// create entries in user_notification table
                models.user_notifications.create({
                    ...userNotificationObject,
                    user_id: returnOrder.user_id,
                    payload: JSON.stringify(userNotificationObject.payload)
                });
            }
        } catch (e) {
            logger.error(e);
        }

        /// send email user
        // try {
        //     const returnOrder: any = await models.return_orders.findOne({
        //         where: { id: returnOrderId }
        //     });

        //     const order: any = await models.orders.findOne({
        //         where: { id: returnOrder.order_id }
        //     });

        //     const user: any = await models.users.findOne({
        //         where: { id: returnOrder.user_id },
        //         attributes: ['email', 'first_name', 'last_name']
        //     });

        //     let emailMessage = ``;

        //     if (type === NotificationType.returnOrderAccepted) {
        //         emailMessage = `We are pleased to inform you that your return order has been accepted!!<br><br><b>Order Code: ${order?.order_code}</b><br><br>`;
        //     } else if (type === NotificationType.returnOrderRejected) {
        //         emailMessage = `We regret to inform you that your return order has been rejected!!<br><br><b>Order Code: ${order?.order_code}</b><br><br>`;
        //     }

        //     if (user?.email) {
        //         /// send mail
        //         mailServices.send({
        //             to: user.email,
        //             subject: getNotificationMessage(type),
        //             data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
        //         });
        //     }

        //     ///
        // } catch (e) {
        //     logger.error(e);
        // }

        /// send email to vendor for diamonds being return to them
        try {
            if (type === NotificationType.returnOrderAccepted) {
                const returnOrder: any = await models.return_orders.findOne({
                    where: { id: returnOrderId }
                });

                if (returnOrder.vendor_id) {
                    const vendor: any = await models.vendors.findOne({
                        where: { id: returnOrder.vendor_id },
                        attributes: ['email', 'first_name', 'last_name', 'phone']
                    });

                    const order: any = await models.orders.findOne({
                        where: { id: returnOrder.order_id },
                        attributes: ['order_code']
                    });

                    const emailMessage =
                        `We are writing to inform you that a return request for diamonds associated with` +
                        `<b> Order Code: ${order?.order_code}</b> has been accepted. The diamonds will be returned to you shortly.`;

                    if (vendor?.email) {
                        /// send mail
                        mailServices.send({
                            to: vendor.email,
                            subject: getNotificationMessage(type),
                            data: { name: `${vendor.first_name} ${vendor.last_name}`, message: emailMessage }
                        });
                    }

                    // if (vendor?.phone) {
                    //     commonNotifications.callNotifications({
                    //         isTextSMS: true,
                    //         isWhatsAppMessage: true,
                    //         isEmail: true,
                    //         mobile: vendor?.phone,
                    //         data: `${getNotificationMessage(type)}\n${emailMessage}`,
                    //         to: vendor?.email,
                    //         subject: getNotificationMessage(type),
                    //         body: { name: `${vendor.first_name} ${vendor.last_name}`, message: emailMessage }
                    //     });
                    // }
                }
            }

            ///
        } catch (e) {
            logger.error(e);
        }

        /// send push notifications to vendor for diamonds being return to them
        // try {
        //     if (type === NotificationType.returnOrderAccepted) {
        //         const returnOrder: any = await models.return_orders.findOne({
        //             where: { id: returnOrderId }
        //         });

        //         const vendor: any = await models.vendors.findOne({
        //             where: { id: returnOrder.vendor_id },
        //             attributes: ['fcm_token']
        //         });

        //         const vendorNotificationObject: UserNotificationAttributes = {
        //             notification_type: type,
        //             title: getNotificationMessage(type),
        //             message:
        //                 type === NotificationType.returnOrderRejected
        //                     ? returnOrder?.reject_reason
        //                     : getNotificationMessage(type),
        //             payload: { return_order_id: returnOrder.id, order_id: returnOrder.order_id }
        //         };

        //         /// send to device
        //         // FirebaseController.sendToDevice(user?.fcm_token, {
        //         //     notification: userNotificationObject
        //         // });
        //         if (vendor?.fcm_token) {
        //             commonNotifications.callNotifications({
        //                 isPushNotifications: true,
        //                 token: vendor?.fcm_token,
        //                 payload: {
        //                     notification: vendorNotificationObject
        //                 }
        //             });
        //         }

        //         /// create entries in user_notification table
        //         models.user_notifications.create({
        //             ...vendorNotificationObject,
        //             vendor_id: returnOrder?.vendor_id,
        //             payload: JSON.stringify(vendorNotificationObject.payload)
        //         });
        //     }
        // } catch (e) {
        //     logger.error(e);
        // }
    }

    /// offer created notification to user
    async sendOfferCreatedNotification(type: any, offerId: string) {
        try {
            const offer: any = await models.stock_offers.findOne({
                where: { id: offerId }
            });

            const user: any = await models.users.findOne({
                where: { id: offer.user_id },
                attributes: ['fcm_token']
            });

            const message = `Offer Price: \$${offer?.offer_price}`;

            if (user?.fcm_token) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message,
                    payload: { offer_id: offer.id }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    token: user?.fcm_token,
                    payload: {
                        notification: userNotificationObject
                    }
                });

                /// create entries in user_notification table
                models.user_notifications.create({
                    ...userNotificationObject,
                    user_id: offer.user_id,
                    payload: JSON.stringify(userNotificationObject.payload)
                });
            }
        } catch (e) {
            logger.error(e);
        }
    }

    /// offer accepted rejected notification
    async sendOfferAcceptedRejectedNotification(type: any, offerId: string) {
        /// send notification to user
        try {
            const offer: any = await models.stock_offers.findOne({
                where: { id: offerId }
            });

            const user: any = await models.users.findOne({
                where: { id: offer.user_id },
                attributes: ['fcm_token']
            });

            const message = `Offer Price: \$${offer?.offer_price}`;


            if (user?.fcm_token) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message,
                    payload: { offer_id: offer.id }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    token: user?.fcm_token,
                    payload: {
                        notification: userNotificationObject
                    }
                });

                /// create entries in user_notification table
                models.user_notifications.create({
                    ...userNotificationObject,
                    user_id: offer.user_id,
                    payload: JSON.stringify(userNotificationObject.payload)
                });
            }

        } catch (e) {
            logger.error(e);
        }


        /// send notification to admin and vendor
        try {
            const offer: any = await models.stock_offers.findOne({
                where: { id: offerId }
            });

            let admin: any;
            let vendor: any;

            if (offer?.admin_id) {
                admin = await models.admins.findOne({
                    where: { id: offer?.admin_id },
                    attributes: ['id', 'fcm_token']
                });
            }

            if (offer?.vendor_id) {
                vendor = await models.vendors.findOne({
                    where: { id: offer?.vendor_id },
                    attributes: ['id', 'fcm_token']
                });
            }

            const message = `Offer Price: \$${offer?.offer_price}`;

            const fcmToken = vendor?.fcm_token || admin?.fcm_token;

            if (fcmToken) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message,
                    payload: { offer_id: offer.id }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    token: fcmToken,
                    payload: {
                        notification: userNotificationObject
                    }
                });

                /// create entries in user_notification table
                models.user_notifications.create({
                    ...userNotificationObject,
                    vendor_id: vendor ? offer?.vendor_id : null,
                    admin_id: admin ? offer?.admin_id : null,
                    payload: JSON.stringify(userNotificationObject.payload)
                });
            }

        } catch (e) {
            logger.error(e);
        }
    }

    /// counter offer notification
    async counterOfferNotification(type: any, offerId: string, role: any) {
        /// send notification and mail to user
        if ([adminRole.subAdmin, adminRole.superAdmin, adminRole.vendor].includes(role)) {
            /// send notification to user
            try {
                const offer: any = await models.stock_offers.findOne({
                    where: { id: offerId }
                });

                const user: any = await models.users.findOne({
                    where: { id: offer.user_id },
                    attributes: ['fcm_token']
                });

                const message = `Offer Price: \$${offer?.offer_price}`;

                if (user?.fcm_token) {
                    const userNotificationObject: UserNotificationAttributes = {
                        notification_type: type,
                        title: getNotificationMessage(type),
                        message,
                        payload: { offer_id: offer.id }
                    };
                    /// send to device
                    // FirebaseController.sendToDevice(user?.fcm_token, {
                    //     notification: userNotificationObject
                    // });

                    commonNotifications.callNotifications({
                        isPushNotifications: true,
                        token: user?.fcm_token,
                        payload: {
                            notification: userNotificationObject
                        }
                    });

                    /// create entries in user_notification table
                    models.user_notifications.create({
                        ...userNotificationObject,
                        user_id: offer.user_id,
                        payload: JSON.stringify(userNotificationObject.payload)
                    });
                }
            } catch (e) {
                logger.error(e);
            }

            /// send mail to user
            // try {
            //     const offer: any = await models.stock_offers.findOne({
            //         where: { id: offerId }
            //     });

            //     const user: any = await models.users.findOne({
            //         where: { id: offer.user_id },
            //         attributes: ['email', 'first_name', 'last_name']
            //     });

            //     const stock: any = await models.stocks.findOne({
            //         where: { id: offer.stock_id },
            //         attributes: ['stock_id']
            //     });

            //     const emailMessage = `You've received a counter offer for <b>Stock ID:</b> ${stock?.stock_id} <br>
            //     <b>Stock Price:</b> \$${offer?.base_price} <br><b>Offer Price:</b> \$${offer?.offer_price} <br>.`;

            //     if (user?.email) {
            //         /// send mail
            //         mailServices.send({
            //             to: user.email,
            //             subject: 'Offer received!!',
            //             data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
            //         });
            //     }
            //     ///
            // } catch (e) {
            //     logger.error(e);
            // }
        }
        /// send mail to admin and vendor
        else if (adminRole.user === role) {
            /// send mail to admin and vendor
            try {
                const offer: any = await models.stock_offers.findOne({
                    where: { id: offerId }
                });

                const admin: any = offer.admin_id
                    ? await models.admins.findOne({
                        where: { id: offer.admin_id },
                        attributes: ['email', 'first_name', 'last_name']
                    })
                    : await models.vendors.findOne({
                        where: { id: offer.vendor_id },
                        attributes: ['email', 'first_name', 'last_name']
                    });

                const stock: any = await models.stocks.findOne({
                    where: { id: offer.stock_id },
                    attributes: ['stock_id']
                });

                const emailMessage = `You've received a counter offer for <b>Stock ID:</b> ${stock?.stock_id
                    } <br><b>Stock Price:</b> \$${offer?.base_price} <br><b>Offer Price:</b> \$${offer.vendor_id ? offer?.updated_offer_price : offer.offer_price
                    } <br>.`;

                if (admin?.email) {
                    /// send mail
                    mailServices.send({
                        to: admin.email,
                        subject: 'Offer received!!',
                        data: { name: `${admin.first_name} ${admin.last_name}`, message: emailMessage }
                    });
                }
                ///
            } catch (e) {
                logger.error(e);
            }


            /// send notification to vendor
            try {
                const offer: any = await models.stock_offers.findOne({
                    where: { id: offerId }
                });

                const vendor: any = await models.vendors.findOne({
                    where: { id: offer?.vendor_id },
                    attributes: ['fcm_token']
                });


                const message = `Offer Price: \$${offer?.offer_price}`;

                if (vendor?.fcm_token) {
                    const userNotificationObject: UserNotificationAttributes = {
                        notification_type: type,
                        title: getNotificationMessage(type),
                        message,
                        payload: { offer_id: offer.id }
                    };
                    /// send to device
                    // FirebaseController.sendToDevice(user?.fcm_token, {
                    //     notification: userNotificationObject
                    // });

                    commonNotifications.callNotifications({
                        isPushNotifications: true,
                        token: vendor?.fcm_token,
                        payload: {
                            notification: userNotificationObject
                        }
                    });

                    /// create entries in user_notification table
                    models.user_notifications.create({
                        ...userNotificationObject,
                        vendor_id: offer?.vendor_id,
                        // admin_id: offer?.admin_id,
                        payload: JSON.stringify(userNotificationObject.payload)
                    });
                }
            } catch (e) {
                logger.error(e);
            }
        }
    }

    /// vendor order updated notifications
    async vendorOrderUpdatedNotification(type: any, vendorOrderIds: any) {
        /// send email to vendor
        if (type !== NotificationType.vendorOrderShipped) {
            try {
                const vendorOrders: any = await models.vendor_orders.findAll({
                    where: { id: { [Op.in]: vendorOrderIds } }
                });

                const vendors: any = await models.vendors.findAll({
                    where: { id: { [Op.in]: vendorOrders.map((item: any) => item?.vendor_id) } },
                    attributes: ['id', 'email', 'first_name', 'last_name', 'phone']
                });

                const orders: any = await models.orders.findAll({
                    where: { id: { [Op.in]: vendorOrders.map((item: any) => item?.order_id) } },
                    attributes: ['id', 'order_code']
                });

                const stocks: any = await models.stocks.findAll({
                    where: { id: { [Op.in]: vendorOrders.map((item: any) => item?.stock_id) } },
                    attributes: ['id', 'stock_id']
                });

                for (const vendorOder of vendorOrders) {
                    /// find vendor
                    const vendor: any = vendors.find((item: any) => vendorOder?.vendor_id === item?.id);

                    /// find order
                    const order: any = orders.find((item: any) => vendorOder?.order_id === item?.id);

                    /// find stock
                    const stock: any = stocks.find((item: any) => vendorOder?.stock_id === item?.id);

                    let emailMessage = ``;
                    if (type === NotificationType.vendorOrderPaid) {
                        emailMessage = `We are pleased to inform you that your order has been paid.`;
                    } else if (type === NotificationType.vendorOrderShipped) {
                        emailMessage = `We are pleased to inform you that your order has been shipped.`;
                    } else if (type === NotificationType.vendorOrderRejected) {
                        emailMessage = `We regret to inform you that your order has been cancelled.`;
                    }

                    emailMessage =
                        emailMessage +
                        `<br><br><b>Order Code: ${order?.order_code}</b><br>` +
                        `<br><b>Stock Id: ${stock?.stock_id}</b><br>`;

                    if (vendorOder?.dollar_rate) {
                        emailMessage = emailMessage + `<br><b>Dollar Rate: \$${vendorOder?.dollar_rate}</b><br><br>`;
                    }

                    if (vendor?.email) {
                        /// send mail
                        mailServices.send({
                            to: vendor.email,
                            subject: getNotificationMessage(type),
                            data: { name: `${vendor.first_name} ${vendor.last_name}`, message: emailMessage }
                        });

                        commonNotifications.callNotifications({
                            isTextSMS: true,
                            isWhatsAppMessage: true,
                            mobile: vendor?.phone,
                            data: `${getNotificationMessage(type)}\n${emailMessage}`
                        });
                    }
                }

                ///
            } catch (e) {
                logger.error(e);
            }
        }
    }

    /// welcome email
    async accountCreatedNotification(userId: any) {
        /// send email
        try {
            const user: any = await models.users.findOne({
                where: { id: userId },
                attributes: ['email', 'legal_registered_name', 'first_name', 'last_name']
            });

            const emailMessage = `Welcome! Your Account Setup is Complete`;

            if (user?.email) {
                /// send mail
                mailServices.send({
                    to: user.email,
                    subject: 'Successfully Registered!!',
                    data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
                });
            }
            ///
        } catch (e) {
            logger.error(e);
        }
    }

    /// return request initiated
    async returnRequestInitiatedNotificationToUser(orderId: any) {
        /// send email
        // try {
        //     const order: any = await models.orders.findOne({
        //         where: { id: orderId },
        //         attributes: ['order_code', 'user_id']
        //     });

        //     const user: any = await models.users.findOne({
        //         where: { id: order.user_id },
        //         attributes: ['email', 'first_name', 'last_name', 'phone']
        //     });

        //     const emailMessage = `We've received a request to return diamonds for <b>Order Code:</b> ${order?.order_code}.`;

        //     if (user?.email) {
        //         /// send mail
        //         mailServices.send({
        //             to: user.email,
        //             subject: 'Return diamonds request received!!',
        //             data: { name: `${user.first_name} ${user.last_name}`, message: emailMessage }
        //         });
        //     }

        //     ///
        // } catch (e) {
        //     logger.error(e);
        // }

        /// send push notifications admin
        try {
            const order: any = await models.orders.findOne({
                where: { id: orderId },
                attributes: ['order_code', 'user_id', 'id']
            });

            const admins: any = await models.admins.findAll({
                where: { role: adminRole.superAdmin },
                attributes: ['fcm_token', 'id']
            });

            const tokens: any = admins?.map((item: any) => item?.fcm_token);

            /// short message
            const message = `Return diamonds request received!!\nOrder Code: ${order?.order_code}`;

            if (tokens?.length) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: NotificationType.returnOrderInitiated,
                    title: getNotificationMessage(NotificationType.returnOrderInitiated),
                    message,
                    payload: { order_id: order.id }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    token: tokens,
                    payload: {
                        notification: userNotificationObject
                    }
                });

                /// bulk create entries in user_notification table
                const bulkCreateObject: any[] = admins?.map((item: any) => ({
                    ...userNotificationObject,
                    admin_id: item?.id,
                    payload: JSON.stringify(userNotificationObject.payload)
                }));

                /// create entries in user_notification table
                models.user_notifications.bulkCreate(bulkCreateObject);
            }

        } catch (e) {
            logger.error(e);
        }

    }

    /// send email to vendor and admin buy request created
    async buyRequestUpdatedNotificationToVendorAndAdmin(type: any, buyRequestId: any) {
        logger.info('Calling buy request update notifications');
        /// send email
        if (type !== NotificationType.buyRequestCreated) {
            try {
                const buyRequest: any = await models.buy_requests.findOne({
                    where: { id: buyRequestId },
                    attributes: ['stock_ids', 'order_code']
                });

                const stocks: any = await models.stocks.findAll({
                    where: { id: { [Op.in]: buyRequest.stock_ids.map((stockId: any) => stockId.stock_id) } },
                    attributes: ['id', 'stock_id', 'admin_id', 'vendor_id']
                });

                /// extract vendor ids
                const vendor_ids: any[] = [
                    ...new Set(
                        buyRequest.stock_ids.filter((item: any) => item?.vendor_id).map((stockId: any) => stockId.vendor_id)
                    )
                ];

                /// extract admin ids
                const admin_ids: any[] = [
                    ...new Set(
                        buyRequest.stock_ids.filter((item: any) => item?.admin_id).map((stockId: any) => stockId.admin_id)
                    )
                ];

                /// send email to vendor
                if (vendor_ids.length) {
                    /// fetch vendor details
                    const vendors: any = await models.vendors.findAll({
                        where: { id: { [Op.in]: vendor_ids } },
                        attributes: ['id', 'email', 'first_name', 'last_name']
                    });

                    ///
                    for (const vendorId of vendor_ids) {
                        try {
                            /// fetch vendor details
                            const vendor: any = vendors.find((item: any) => item?.id === vendorId);

                            let emailMessage = '';

                            if (type === NotificationType.buyRequestUpdated) {
                                emailMessage = `We are pleased to inform you that a buy request has been updated for the diamonds listed below:<br><br>`;
                            } else if (type === NotificationType.buyRequestCreated) {
                                emailMessage = `We are pleased to inform you that a new buy request has been created for the diamonds listed below:<br><br>`;
                            } else if (
                                [NotificationType.buyRequestRejected, NotificationType.vendorOrderRejected].includes(type)
                            ) {
                                emailMessage = `We regret to inform you that order has been cancelled for the diamonds listed below:<br><br>`;
                            }

                            const vendorStocks = stocks.filter((stock: any) => stock.vendor_id === vendorId);

                            if (buyRequest?.order_code) {
                                emailMessage = emailMessage + `<b>Order Code:</b> ${buyRequest?.order_code}<br><br>`;
                            }

                            for (const stock of vendorStocks) {
                                emailMessage = emailMessage + `<b>Diamond ID:</b> ${stock?.stock_id}<br>`;
                            }

                            if (vendor?.email) {
                                /// send mail
                                mailServices.send({
                                    to: vendor.email,
                                    subject: getNotificationMessage(type),
                                    data: { name: `${vendor.first_name} ${vendor.last_name}`, message: emailMessage },
                                });
                            }
                        } catch (e) {
                            logger.error(e);
                        }
                    }
                }

                /// send email to admin
                if (admin_ids.length) {
                    for (const adminId of admin_ids) {
                        try {
                            /// fetch admin details
                            const admin: any = await models.admins.findOne({
                                where: { id: adminId },
                                attributes: ['email', 'first_name', 'last_name']
                            });

                            let emailMessage = '';

                            if (type === NotificationType.buyRequestUpdated) {
                                emailMessage = `We are pleased to inform you that a buy request has been updated for the diamonds listed below:<br><br>`;
                            } else if (type === NotificationType.buyRequestCreated) {
                                emailMessage = `We are pleased to inform you that a new buy request has been created for the diamonds listed below:<br><br>`;
                            } else if (
                                [NotificationType.buyRequestRejected, NotificationType.vendorOrderRejected].includes(type)
                            ) {
                                emailMessage = `We regret to inform you that order has been cancelled for the diamonds listed below:<br><br>`;
                            }

                            const adminStocks = stocks.filter((stock: any) => stock.admin_id === adminId);

                            if (buyRequest?.order_code) {
                                emailMessage = emailMessage + `<b>Order Code:</b> ${buyRequest?.order_code}<br><br>`;
                            }

                            for (const stock of adminStocks) {
                                emailMessage = emailMessage + `<b>Diamond ID:</b> ${stock?.stock_id}<br>`;
                            }

                            if (admin?.email) {
                                /// send mail
                                mailServices.send({
                                    to: admin.email,
                                    subject: getNotificationMessage(type),
                                    data: { name: `${admin.first_name} ${admin.last_name}`, message: emailMessage }
                                });
                            }
                        } catch (e) {
                            logger.error(e);
                        }
                    }
                }
                ///
            } catch (e) {
                logger.error(e);
            }
        }


        /// send push notifications to vendor and admin for buy request updated
        try {
            const buyRequest: any = await models.buy_requests.findOne({
                where: { id: buyRequestId },
                attributes: ['stock_ids', 'order_code', 'user_id']
            });
            /// extract vendor ids
            const vendor_ids: any[] = [
                ...new Set(
                    buyRequest.stock_ids.filter((item: any) => item?.vendor_id).map((stockId: any) => stockId.vendor_id)
                )
            ];

            /// extract admin ids
            const admin_ids: any[] = [
                ...new Set(
                    buyRequest.stock_ids.filter((item: any) => item?.admin_id).map((stockId: any) => stockId.admin_id)
                )
            ];

            const vendors: any = await models.vendors.findAll({
                where: { id: { [Op.in]: vendor_ids } },
                attributes: ['fcm_token', 'id']
            });

            const user: any = await models.users.findOne({
                where: { id: buyRequest.user_id },
                attributes: ['fcm_token', 'id']
            });

            const admins: any = await models.admins.findAll({
                where: { id: { [Op.in]: admin_ids } },
                attributes: ['fcm_token', 'id']
            });

            const tokens: any[] = [...admins.map((admin: any) => admin?.fcm_token), ...vendors.map((vendor: any) => vendor?.fcm_token), user?.fcm_token];

            if (tokens.length) {
                const vendorNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message: getNotificationMessage(type),
                    payload: { buy_request_id: buyRequestId }
                };

                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });

                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    token: tokens,
                    payload: {
                        notification: vendorNotificationObject
                    }
                });

                const bulkCreateObject = [...vendors.map((vendor: any) => ({
                    ...vendorNotificationObject,
                    vendor_id: vendor?.id,
                    payload: JSON.stringify(vendorNotificationObject.payload)
                })), ...admins.map((admin: any) => ({
                    ...vendorNotificationObject,
                    admin_id: admin?.id,
                    payload: JSON.stringify(vendorNotificationObject.payload)
                }))];

                if (user) {
                    bulkCreateObject.push({
                        ...vendorNotificationObject,
                        user_id: user?.id,
                        payload: JSON.stringify(vendorNotificationObject.payload)
                    });
                }

                /// create entries in user_notification table
                models.user_notifications.bulkCreate(bulkCreateObject);
            }
        } catch (e) {
            logger.error(e);
        }
    }

    /// send order created notifications to user for jewellery and melle
    async sendOderCreatedNotifications(type: any, userId?: any, orderId?: any, buyRequestId?: any) {
        /// send push notification
        try {

            const tokens: any[] = [];

            /// fetch order details
            const order: any = await models.orders.findOne({
                where: { [Op.or]: [orderId ? { id: orderId } : {}, buyRequestId ? { buy_request_id: buyRequestId } : {}] },
                attributes: ['id', 'user_id']
            });

            /// prevent duplicate notifications to user
            if (!buyRequestId) {
                const user: any = await models.users.findOne({
                    where: { id: userId },
                    attributes: ['id', 'fcm_token', 'kyc_reject_reason', 'phone']
                });

                if (user?.fcm_token) {
                    tokens.push(user?.fcm_token);
                }
            }

            const admins: any = await models.admins.findAll({
                where: { role: adminRole.superAdmin },
                attributes: ['fcm_token', 'id']
            });

            tokens.push(...admins.map((admin: any) => admin?.fcm_token));

            if (tokens?.length) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: type,
                    title: getNotificationMessage(type),
                    message: getNotificationMessage(type),
                    payload: { user_id: userId, order_id: order?.id }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });
                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    //  isTextSMS: true,
                    //  isWhatsAppMessage: true,
                    token: tokens,
                    payload: {
                        notification: userNotificationObject
                    }
                    // mobile: user?.phone
                    //  data: `${getNotificationMessage(type)}\n${message}`
                });

                const bulkCreateObject: any[] = [];

                if (!buyRequestId) {
                    bulkCreateObject.push({
                        ...userNotificationObject,
                        user_id: userId,
                        payload: JSON.stringify(userNotificationObject.payload)
                    });
                }

                bulkCreateObject.push(...admins.map((admin: any) => ({
                    ...userNotificationObject,
                    admin_id: admin?.id,
                    payload: JSON.stringify(userNotificationObject.payload)
                })));

                /// create entries in user_notification table
                models.user_notifications.bulkCreate(bulkCreateObject);
            }
        } catch (e) {
            logger.error(e);
        }
    }

    /**
     * @api {get} /v1/auth/user/notification
     * @apiName UserNotificationList
     * @apiGroup UserNotification
     *
     *
     * @apiSuccess {Object} UserNotification.
     */
    async getUserNotification(req: Request, res: Response, next: NextFunction) {
        try {
            const user_id = req[`id`];
            const skip = req.query.skip;
            const limit = req.query.limit;

            const { rows, count } = await models.user_notifications.findAndCountAll({
                where: { user_id },
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/vendor/notification
     * @apiName VendorNotificationList
     * @apiGroup VendorNotification
     *
     *
     * @apiSuccess {Object} VendorNotification.
     */
    async getVendorNotification(req: Request, res: Response, next: NextFunction) {
        try {
            const vendor_id = req[`id`];
            const skip = req.query.skip;
            const limit = req.query.limit;

            const { rows, count } = await models.user_notifications.findAndCountAll({
                where: { vendor_id },
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/notification-unread-count
     * @apiName UserNotificationUnreadCount
     * @apiGroup UserNotification
     *
     *
     * @apiSuccess {Object} UserNotification.
     */
    async getUserNotificationUnreadCount(req: Request, res: Response, next: NextFunction) {
        try {
            const user_id = req[`id`];

            const count = await models.user_notifications.count({ where: { user_id, is_read: false } });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/user/notification-read
     * @apiName UserNotificationUpdateRead
     * @apiGroup UserNotification
     *
     *
     * @apiSuccess {Object} UserNotification.
     */
    async markAsReadUserNotification(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;
            const readAll = String(req.query.read_all).toLowerCase() === 'true';
            const userId = req[`id`];

            if (readAll) {
                /// mark all notifications as read
                await models.user_notifications.update({ is_read: true }, { where: { user_id: userId } });
            } else if (id) {
                /// mark notifications as read
                const notification = await models.user_notifications.findOne({ where: { id } });

                if (!notification) {
                    throw new Error(`Notification not found!!`);
                }

                /// update is_read
                await models.user_notifications.update({ is_read: true }, { where: { id } });
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                data: 'Notification read successfully'
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    // TODO: This function used when create notifications any operation or functionality

    /**
     * @api {post} /v1/auth/user/notification
     * @apiName UserNotificationList
     * @apiGroup UserNotification
     *
     *
     * @apiSuccess {Object} UserNotification.
     */
    async sendUserInquiryNotification(req: Request, res: Response, next: NextFunction) {
        logger.info(`calling send user inquiry notifications`);
        try {
            /// send push notification

            const authorization = req.headers.authorization;
            logger.info(`!!!authorization!!!! ${authorization}`)

            if (authorization !== process.env.ADD_STOCK_KEY) {
                throw new Error(`Unauthorized access`);
            }

            const { user_id, admin_id, from_admin, from_user, inquiry_id, inquiry, last_message } = req.body;

            const tokens: any[] = [];
            let admins: any;
            let user: any;

            if (from_user) {
                admins = await models.admins.findAll({
                    where: { role: adminRole.superAdmin },
                    attributes: ['id', 'fcm_token', 'phone']
                });

                const adminTokens: any[] = admins.map((admin: any) => admin?.fcm_token);

                if (adminTokens?.length) {
                    tokens.push(...adminTokens);
                }
            } else {
                user = await models.users.findOne({
                    where: { id: user_id },
                    attributes: ['id', 'fcm_token', 'phone']
                });

                if (user?.fcm_token) {
                    tokens.push(user?.fcm_token);
                }
            }

            if (tokens.length) {
                const userNotificationObject: UserNotificationAttributes = {
                    notification_type: NotificationType.inquiryMessage,
                    title: getNotificationMessage(NotificationType.inquiryMessage),
                    message: last_message,
                    payload: {
                        user_id,
                        admin_id,
                        inquiry_id,
                        inquiry: { type: inquiry?.inquiry?.type, title: inquiry?.inquiry?.title }
                    }
                };
                /// send to device
                // FirebaseController.sendToDevice(user?.fcm_token, {
                //     notification: userNotificationObject
                // });
                commonNotifications.callNotifications({
                    isPushNotifications: true,
                    //  isTextSMS: true,
                    //  isWhatsAppMessage: true,
                    token: tokens,
                    payload: {
                        notification: userNotificationObject
                    }
                    // mobile: user?.phone
                    //  data: `${getNotificationMessage(type)}\n${message}`
                });

                const bulkCreateObject: any[] = [];

                if (admins?.length) {
                    bulkCreateObject.push(...admins.map((admin: any) => ({
                        ...userNotificationObject,
                        admin_id: admin?.id,
                        payload: JSON.stringify({ ...userNotificationObject.payload, inquiry })
                    })));
                }

                if (user) {
                    bulkCreateObject.push({
                        ...userNotificationObject,
                        user_id: user?.id,
                        payload: JSON.stringify({ ...userNotificationObject.payload, inquiry })
                    });
                }

                /// create entries in user_notification table
                models.user_notifications.bulkCreate(bulkCreateObject);
            }

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new UserNotification();
