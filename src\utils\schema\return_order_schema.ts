import Joi from 'joi';

export const createdReturnOrderSchema = Joi.object({
    order_id: Joi.string().uuid().required(),
    return_stock_ids: Joi.array()
        .items(
            Joi.object({
                stock_id: Joi.string().uuid().required(),
                vendor_id: Joi.string().uuid().optional(),
                admin_id: Joi.string().uuid().optional(),
                reason: Joi.string().required()
            })
                .required()
                .or('vendor_id', 'admin_id')
        )
        .required()
});

export const returnOrderActionSchema = Joi.object({
    return_order_id: Joi.string().uuid().required(),
    is_accepted: Joi.boolean().required(),
    reject_reason: Joi.string().optional()
});

export const returnOrderStatusSchema = Joi.object({
    return_order_id: Joi.string().uuid().required(),
    status: Joi.string().valid('PENDING', 'CLIENT-SHIPPED', 'VENDOR-SHIPPED', 'RECEIVED').required(),
    received_amount: Joi.number().optional()
});
