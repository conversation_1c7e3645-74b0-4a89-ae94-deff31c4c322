import { IRouter, NextFunction, Request, Response, Router } from 'express';
import user from '../../../controllers/user/user';
import smsServices from '../../../controllers/user/user_notifications/sms_services';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    changeUserEmailVerificationStatusSchema,
    changeUserPhoneVerificationStatusSchema,
    checkEmailPhoneExistsSchema,
    checkRegistrationStatusSchema,
    createUserSchema,
    loginUserSchema
} from '../../../utils/schema/user_schema';
import {
    resendOtpSchema,
    resetPasswordSchema,
    sendOtpSchema,
    sendVerificationOtpSchema,
    verifyOtpSchema
} from '../../../utils/schema/settings_schema';

const routes: IRouter = Router();

// register user
routes.post(
    '/register',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createUserSchema),
    user.userRegister
);

// login user
routes.post(
    '/login',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, loginUserSchema),
    user.loginUser
);

// check registration status
routes.post(
    '/registration-status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, checkRegistrationStatusSchema),
    user.registrationStatus
);

// user reset password
routes.put(
    '/reset-password',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, resetPasswordSchema),
    user.resetUserPassword
);

// user send otp
routes.put(
    '/send-verification-otp',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, sendVerificationOtpSchema),
    user.sendUserVerificationOtp
);

// verify phone
routes.put(
    '/verify-phone',
    (req: Request, res: Response, next: NextFunction) =>
        fieldsValidator(req, res, next, changeUserPhoneVerificationStatusSchema),
    user.changeUserPhoneVerificationStatus
);

// verify email
routes.put(
    '/verify-email',
    (req: Request, res: Response, next: NextFunction) =>
        fieldsValidator(req, res, next, changeUserEmailVerificationStatusSchema),
    user.changeUserEmailVerificationStatus
);

// verify email
routes.post(
    '/email-phone-available',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, checkEmailPhoneExistsSchema),
    user.checkEmailPhoneExists
);

// MSG91 send otp
routes.put(
    '/send-otp',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, sendOtpSchema),
    smsServices.sendOTP
);

// MSG91 verify otp
routes.put(
    '/verify-otp',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, verifyOtpSchema),
    smsServices.verifyOTP
);

// MSG91 resend otp
routes.put(
    '/resend-otp',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, resendOtpSchema),
    smsServices.resendOTP
);

// MSG91 send SMS
routes.put(
    '/send-sms',
    // (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, sendVerificationOtpSchema),
    smsServices.sendSMS
);

// MSG91 resend otp
routes.put(
    '/resend-whatsapp',
    // (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, sendVerificationOtpSchema),
    smsServices.sendWhatsAppMessage
);

export default routes;
