import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface UserNotificationAttributes {
    id?: string; // id is an auto-generated UUID
    user_id?: string;
    vendor_id?: string;
    admin_id?: string;
    notification_type: string; // BUY_REQUEST_UPDATED, BUY_REQUEST_ACCEPTED, BUY_REQUEST_REJECTED, ORDER_UPDATED, ORDER_COMPLETED, VENDOR_ORDER_SHIPPED,VENDOR_ORDER_PAID,RETURN_ORDER_ACCEPTED,RETURN_ORDER_REJECTED,BUY_REQUEST_CREATED
    title: string;
    message: string;
    payload?: object;
    is_read?: boolean;
    is_active?: boolean;
    _deleted?: boolean;
}

interface UserNotificationCreationAttributes extends Optional<UserNotificationAttributes, 'id'> { }

interface UserNotificationInstance
    extends Model<UserNotificationAttributes, UserNotificationCreationAttributes>,
    UserNotificationAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type UserNotificationStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => UserNotificationInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const userNotification = sequelize.define<UserNotificationInstance>(
        'user_notifications',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            notification_type: {
                type: DataTypes.ENUM(
                    'BUY_REQUEST_UPDATED',
                    'ORDER_CREATED',
                    'BUY_REQUEST_REJECTED',
                    'ORDER_CANCELED',
                    'ORDER_DELIVERED',
                    'ORDER_SHIPPED',
                    'STOCK_UPDATED',
                    'STOCK_PRICE_INCREASED',
                    'STOCK_PRICE_DECREASED',
                    'STOCK_AVAILABLE',
                    'STOCK_HOLD',
                    'STOCK_SOLD',
                    'KYC_REJECTED',
                    'KYC_ACCEPTED',
                    'CREDIT_LIMIT_ADDED',
                    'INVOICE_AVAILABLE',
                    'RETURN_ORDER_ACCEPTED',
                    'RETURN_ORDER_REJECTED',
                    'RETURN_ORDER_INITIATED',
                    'OFFER_ACCEPTED',
                    'OFFER_REJECTED',
                    'OFFER_REVISED',
                    'VENDOR_ORDER_SHIPPED',
                    'VENDOR_ORDER_REJECTED',
                    'VENDOR_ORDER_PAID',
                    'BUY_REQUEST_CREATED',
                    'OFFER_CREATED',
                    'RAISE_INVOICE',
                    'INVOICE_ACCEPTED',
                    'INVOICE_REJECTED',
                    'INQUIRY_MESSAGE'
                ),
                allowNull: false
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false
            },
            message: {
                type: DataTypes.STRING,
                allowNull: true
            },
            payload: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            is_read: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as UserNotificationStatic;

    userNotification.associate = (models) => {
        userNotification.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };
    //
    // await userNotification.sync({ alter: true })

    return userNotification;
};
