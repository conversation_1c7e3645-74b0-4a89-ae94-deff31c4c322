import { NextFunction, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import models from '../../models/index';
import { adminRole, httpStatusCodes } from '../../utils/constants';
import { Op, Sequelize } from 'sequelize';

class JewelleryShowcase {
    /**
     * @api {get} /v1/auth/admin/jewellery-showcase
     * @apiName ListJewelleryShowcase
     * @apiGroup JewelleryShowcase
     *
     *
     * @apiSuccess {Object} JewelleryShowcase.
     */

    /// list JewelleryShowcase
    async listJewelleryShowcaseVideos(req: Request, res: Response, next: NextFunction) {
        logger.info('calling listJewelleryShowcaseVideos');
        try {
            const skip = req.query.skip;
            const limit = req.query.limit;
            const tag = req.query.tag;
            const isLikedVideo = String(req.query.is_liked) === 'true';
            const userId = req[`id`];
            const role = req[`role`];
            const conditions: any = [];
            const orderClause: any[] = [];
            let jewelleryLikedVideoIds: any[] = [];

            /// if role is user
            if (role === adminRole.user) {
                let priorityCase = `CASE`;

                /// check if the user has liked the jewellery showcase
                const jewelleryLikedVideos = await models.jewellery_liked_videos.findAll({
                    order: [['createdAt', 'DESC']],
                    where: {
                        user_id: userId
                    },
                    raw: true
                });

                /// jewelleryLikedVideoIds
                jewelleryLikedVideoIds = jewelleryLikedVideos.map(
                    (jewelleryLikedVideo: any) => jewelleryLikedVideo.jewellery_showcase_id
                );

                /// isLiked
                if (isLikedVideo) {
                    /// check if the jewellery showcase is liked
                    if (jewelleryLikedVideoIds.length) {
                        jewelleryLikedVideoIds.forEach((id: any) => {
                            priorityCase += ` WHEN id = '${id}' THEN 0`; // liked = priority 0
                        });
                    }
                }

                /// check if the user has seen the jewellery showcase
                const jewellerySeenVideos = await models.jewellery_seen_videos.findAll({
                    order: [['createdAt', 'DESC']],
                    where: {
                        user_id: userId
                    },
                    raw: true
                });

                /// jewellerySeenVideoIds
                const jewellerySeenVideoIds = jewellerySeenVideos.map(
                    (jewellerySeenVideo: any) => jewellerySeenVideo.jewellery_showcase_id
                );

                if (jewellerySeenVideoIds.length) {
                    jewellerySeenVideoIds.forEach((id: any) => {
                        priorityCase += ` WHEN id = '${id}' THEN 2`; // seen = priority 2
                    });
                }

                priorityCase += ` ELSE 1 END`; // unseen/unliked = priority 1

                if ((isLikedVideo && jewelleryLikedVideoIds.length) || jewellerySeenVideoIds.length) {
                    orderClause.push([Sequelize.literal(priorityCase), 'ASC']); // 0 → 1 → 2
                }
            }

            /// order by createdAt
            orderClause.push(['createdAt', 'DESC']); // secondary order within priority

            if (tag) {
                /// check if the tag is in the jewellery showcase tags
                conditions.push({
                    tags: {
                        [Op.overlap]: [tag]
                    }
                });
            }

            const { rows, count } = await models.jewellery_showcases.findAndCountAll({
                order: orderClause,
                offset: skip,
                limit,
                where: {
                    [Op.and]: conditions
                }
            });

            /// add is_liked true otherwise false to the jewellery showcase rows id present in jewelleryLikedVideoIds
            if (jewelleryLikedVideoIds.length) {
                /// check if the jewellery showcase is liked
                rows.forEach((row: any) => {
                    row.is_liked = jewelleryLikedVideoIds.includes(row.id);
                });
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Jewellery showcase listed successfully',
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error('error:Jewellery showcase', error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/admin/jewellery-showcase
     * @apiName AddJewelleryShowcaseVideo
     * @apiGroup AddJewelleryShowcaseVideo
     *
     *
     * @apiSuccess {Object} AddJewelleryShowcaseVideo.
     */

    /// add Jewellery Showcase
    async addJewelleryShowcase(req: Request, res: Response, next: NextFunction) {
        logger.info('calling addJewelleryShowcase');
        try {
            const { file_name, description, thumbnail_name, tags } = req.body;
            const role = req[`role`];

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('You are not authorized to perform this action');
            }

            if (!file_name) {
                throw new Error('file_name is required');
            }

            await models.jewellery_showcases.create({
                video_url: `${process.env.BASE_URL}/jewellery/video/${file_name}`,
                thumbnail_url: `${process.env.BASE_URL}/jewellery/video/thumbnail/${thumbnail_name}`,
                description,
                file_name,
                thumbnail_name,
                tags
            });

            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'ListJewelleryShowcase created successfully'
            });
        } catch (error: any) {
            logger.error('error:ListJewelleryShowcase', error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/jewellery-showcase
     * @apiName EditJewelleryShowcaseVideo
     * @apiGroup EditJewelleryShowcaseVideo
     *
     *
     * @apiSuccess {Object} EditJewelleryShowcaseVideo.
     */

    /// edit Jewellery Showcase
    async editJewelleryShowcase(req: Request, res: Response, next: NextFunction) {
        logger.info('calling editJewelleryShowcase');
        try {
            const { id, file_name, description, thumbnail_name, tags } = req.body;
            const role = req[`role`];
            const updateObject: any = {};

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('You are not authorized to perform this action');
            }

            const jewelleryShowcase = await models.jewellery_showcases.findOne({ where: { id } });

            if (!jewelleryShowcase) {
                throw new Error('JewelleryShowcase not found');
            }

            if (file_name) {
                const videoUrl = `${process.env.BASE_URL}/jewellery/video/${file_name}`;
                updateObject.file_name = file_name;
                updateObject.video_url = videoUrl;
            }

            if (thumbnail_name) {
                const thumbnailUrl = `${process.env.BASE_URL}/jewellery/video/thumbnail/${thumbnail_name}`;
                updateObject.thumbnail_name = thumbnail_name;
                updateObject.thumbnail_url = thumbnailUrl;
            }

            if (description) {
                updateObject.description = description;
            }

            if (tags) {
                updateObject.tags = tags;
            }

            await models.jewellery_showcases.update(updateObject, { where: { id } });

            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'jewelleryShowcase updated successfully'
            });
        } catch (error: any) {
            logger.error('error:jewelleryShowcase', error);
            next(error);
        }
    }

    /**
     * @api {delete} /v1/auth/admin/jewellery-showcase
     * @apiName DeleteJewelleryShowcaseVideo
     * @apiGroup JewelleryShowcaseVideo
     *
     *
     * @apiSuccess {Object} JewelleryShowcaseVideo.
     */

    /// delete banner
    async deleteJewelleryShowcaseVideo(req: Request, res: Response, next: NextFunction) {
        logger.info('calling deleteJewelleryShowcaseVideo');
        try {
            const id = req.query.id;
            const role = req[`role`];

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('You are not authorized to perform this action');
            }

            if (!id) {
                throw new Error('id required!!!');
            }

            const jewelleryShowcase = await models.jewellery_showcases.findOne({ where: { id } });

            if (!jewelleryShowcase) {
                throw new Error('JewelleryShowcase not found');
            }

            await models.jewellery_showcases.update({ _deleted: true }, { where: { id } });

            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'ListJewelleryShowcase deleted successfully'
            });
        } catch (error: any) {
            logger.error('error:deleteListJewelleryShowcase', error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/add-like-jewellery/:id
     * @apiName AddLikeJewelleryShowcase
     * @apiGroup AddLikeJewelleryShowcase
     *
     *
     * @apiSuccess {Object} AddLikeJewelleryShowcase.
     */
    /// add like to jewellery showcase
    async addRemoveLikeJewelleryShowcase(req: Request, res: Response, next: NextFunction) {
        logger.info('calling addRemoveLikeJewelleryShowcase');
        try {
            const id = req.query.id;
            const userId = req[`id`];

            if (!id) {
                throw new Error('id required!!!');
            }

            /// check if the jewellery showcase exists
            const jewelleryShowcase = await models.jewellery_showcases.findOne({ where: { id } });

            if (!jewelleryShowcase) {
                throw new Error('JewelleryShowcase not found');
            }

            /// check if the jewellery showcase already liked
            const jewelleryLikedVideo = await models.jewellery_liked_videos.findOne({
                where: { jewellery_showcase_id: id, user_id: userId }
            });

            if (jewelleryLikedVideo) {
                /// remove like from jewellery showcase
                await models.jewellery_liked_videos.destroy({
                    where: { id: jewelleryLikedVideo.id }
                });
            } else {
                /// create jewellery liked video
                await models.jewellery_liked_videos.create({
                    jewellery_showcase_id: id,
                    user_id: userId
                });
            }

            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `JewelleryShowcase ${jewelleryLikedVideo ? 'unliked' : 'liked'} successfully`
            });

            ///
        } catch (error: any) {
            logger.error('error:addLikeJewelleryShowcase', error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/admin/add-seen-jewellery
     * @apiName AddSeenJewelleryShowcase
     * @apiGroup AddSeenJewelleryShowcase
     *
     *
     * @apiSuccess {Object} AddSeenJewelleryShowcase.
     */
    /// add seen to jewellery showcase
    async addSeenJewelleryShowcase(req: Request, res: Response, next: NextFunction) {
        logger.info('calling addSeenJewelleryShowcase');
        try {
            const { jewellery_showcase_ids } = req.body;

            const userId = req[`id`];

            if (!jewellery_showcase_ids || !jewellery_showcase_ids?.length) {
                throw new Error('jewellery_showcase_ids required!!!');
            }

            /// check if the jewellery showcase exists
            const jewelleryShowcases: any = await models.jewellery_showcases.findAll({
                where: { id: jewellery_showcase_ids },
                raw: true
            });

            if (!jewelleryShowcases || jewelleryShowcases.length === 0) {
                throw new Error('JewelleryShowcase not found');
            }

            /// bulk create jewellery seen videos
            const jewellerySeenVideos = jewelleryShowcases.map((jewelleryShowcase: any) => ({
                jewellery_showcase_id: jewelleryShowcase.id,
                user_id: userId
            }));

            /// check if the jewellery showcase already seen
            const jewellerySeenVideosExists = await models.jewellery_seen_videos.findAll({
                where: {
                    [Op.and]: [
                        {
                            jewellery_showcase_id: {
                                [Op.in]: jewellery_showcase_ids
                            }
                        },
                        {
                            user_id: userId
                        }
                    ]
                },
                raw: true
            });

            /// remove entries from jewellerySeenVideos
            const jewellerySeenVideosFiltered = jewellerySeenVideos.filter((jewellerySeenVideo: any) => {
                return !jewellerySeenVideosExists.some((jewellerySeenVideoExists: any) => {
                    return (
                        jewellerySeenVideo.jewellery_showcase_id === jewellerySeenVideoExists.jewellery_showcase_id &&
                        jewellerySeenVideo.user_id === jewellerySeenVideoExists.user_id
                    );
                });
            });

            /// bulk create jewellery seen videos
            if (jewellerySeenVideosFiltered.length) {
                await models.jewellery_seen_videos.bulkCreate(jewellerySeenVideosFiltered);
            }

            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'JewelleryShowcase added seen successfully'
            });

            ///
        } catch (error: any) {
            logger.error('error:addSeenJewelleryShowcase', error);
            next(error);
        }
    }

    /**
     * @api {put} /v1/auth/user/jewellery-tags
     * @apiName AddJewelleryTags
     * @apiGroup AddJewelleryTags
     *
     *
     * @apiSuccess {Object} AddJewelleryTags.
     */
    /// list jewellery tags
    async listJewelleryTags(req: Request, res: Response, next: NextFunction) {
        logger.info('calling listJewelleryTags');
        try {
            const jewelleryShowcases = await models.jewellery_showcases.findAll({
                attributes: ['tags'],
                raw: true
            });

            /// get unique tags
            const jewelleryTags = [
                ...new Set(jewelleryShowcases.flatMap((jewelleryShowcase: any) => jewelleryShowcase.tags))
            ].filter((tag: any) => tag);

            return res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Jewellery tags listed successfully',
                data: jewelleryTags
            });
        } catch (error: any) {
            logger.error('error:listJewelleryTags', error);
            next(error);
        }
    }
}

export default new JewelleryShowcase();
