import stock from '../../../controllers/admin/stock/stock';
import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import { createStockSchema, changeStockStatusSchema, deleteStockSchema } from '../../../utils/schema/stock_schema';

const router: IRouter = Router();

// list stocks
router.post('/stock-list', stock.listStocks);

// add stock
router.post(
    '/stock',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createStockSchema),
    stock.addStock
);

// change stock status
router.put(
    '/stock/status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeStockStatusSchema),
    stock.changeStockStatus
);

// delete stock
router.delete(
    '/stock',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, deleteStockSchema),
    stock.deleteStock
);

// stock details
router.get('/stock-details', stock.getStockDetails);

// get stock details
router.get('/get-stock-details', stock.getStockDetailsUsingStockId);

export default router;
