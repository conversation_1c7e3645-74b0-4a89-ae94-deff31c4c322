import Joi from 'joi';

export const createStockSchema = Joi.object({
    file_name: Joi.string().required(),
    replace_all: Joi.boolean().optional(),
    offset: Joi.number().optional()
});

// export const createStockFromApiSchema = Joi.object({
//     stock_array_object: Joi.array().items(Joi.object()).required(),
//     replace_all: Joi.boolean().optional(),
//     offset: Joi.number().optional(),
//     vendor_id: Joi.string().uuid().optional(),
//     admin_id: Joi.string().uuid().optional()
// }).or('vendor_id', 'admin_id');
export const createStockFromApiSchema = Joi.object({
    filePath: Joi.string().required(),
    replace_all: Joi.boolean().optional(),
    offset: Joi.number().optional(),
    vendor_id: Joi.string().uuid().optional(),
    admin_id: Joi.string().uuid().optional()
}).or('vendor_id', 'admin_id');

export const changeStockStatusSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_active: Joi.boolean().optional(),
    is_featured: Joi.boolean().optional(),
    is_new_arrival: Joi.boolean().optional()
});

export const getStockStatusSchema = Joi.object({
    ids: Joi.array().items(Joi.string().uuid().required()).required()
});

export const deleteStockSchema = Joi.object({
    stock_ids: Joi.array().items(Joi.string().uuid().required()).min(1).required()
});
