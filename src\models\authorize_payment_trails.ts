import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface AuthorizePaymentTrailsAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    buy_request_id: string;
    parent_buyrequest_id: string;
    order_id: string;
    buy_request_status: string;
    status: string;
    amount: number;
    card_details: string;
    trans_id: string;
    ref_id: string;
    ref_trans_id: string;
    customer_profile_id: string;
    payload: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface AuthorizePaymentTrailsCreationAttributes extends Optional<AuthorizePaymentTrailsAttributes, 'id'> { }

interface AuthorizePaymentTrailsInstance
    extends Model<AuthorizePaymentTrailsAttributes, AuthorizePaymentTrailsCreationAttributes>,
    AuthorizePaymentTrailsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type AuthorizePaymentTrailsStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => AuthorizePaymentTrailsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const authorize_payment_trails = sequelize.define<AuthorizePaymentTrailsInstance>(
        'authorize_payment_trails',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            parent_buyrequest_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            buy_request_status: {
                type: DataTypes.ENUM,
                allowNull: true,
                values: ['PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED']
            },
            status: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: [
                    'CREATE-CUSTOMER',
                    'AUTHORIZE',
                    'RE-AUTHORIZE',
                    'CAPTURE',
                    'VOID',
                    'REFUND',
                    'CREATE-APPLE-PAY',
                    'CREATE-APPLE-PAY-ERROR',
                    'AUTHORIZE-ERROR',
                    'RE-AUTHORIZE-ERROR',
                    'CAPTURE-ERROR',
                    'VOID-ERROR',
                    'REFUND-ERROR',
                    'CREATE-CUSTOMER-ERROR'
                ]
            },
            amount: {
                type: DataTypes.DOUBLE,
                allowNull: true
            },
            customer_profile_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            card_details: {
                type: DataTypes.STRING,
                allowNull: true
            },
            trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            ref_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            ref_trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            payload: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as AuthorizePaymentTrailsStatic;
    //
    // await authorize_payment_trails.sync({ alter: true })

    return authorize_payment_trails;
};
