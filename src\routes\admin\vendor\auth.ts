import vendor from '../../../controllers/admin/vendor';

import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../../middlewares/validator';
import {
    blackListVendorSchema,
    changeVendorStatusSchema,
    createVendorSchema,
    updateVendorSchema,
    verifyVendorSchema
} from '../../../utils/schema/vendor_schema';

const router: IRouter = Router();

// add vendor
router.post(
    '/vendor',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createVendorSchema),
    vendor.addVendor
);

// list vendors
router.get('/vendor', vendor.listVendors);

// update vendor
router.put(
    '/vendor',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateVendorSchema),
    vendor.updateVendor
);

// change vendor status
router.put(
    '/vendor/status',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeVendorStatusSchema),
    vendor.changeVendorStatus
);

// blacklist vendor
router.put(
    '/vendor/black-list',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, blackListVendorSchema),
    vendor.blackListVendor
);

// delete vendor
router.delete('/vendor', vendor.deleteVendor);

// vendor details
router.get('/vendor/details', vendor.vendorDetails);

// verify vendor
router.put(
    '/vendor/verify',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, verifyVendorSchema),
    vendor.verifyVendor
);

// verify vendor
router.get('/vendor/fetch-api-stocks', vendor.fetchAPIStocks);

export default router;
