import { randomBytes } from 'crypto';

// This limit is for shopify product listing, it can be maximum 100
export const DEFAULT_PAGINATION_LIMIT_SHOPIFY_PRODUCT_LIST = 100;

export const TOKEN = 'super.super.secret.shhh';

export const contactEmail = '<EMAIL>';

export enum httpStatusCodes {
    APP_NAME = 'income_shows',
    SUCCESS_CODE = 200,
    FORBIDDEN_CODE = 403,
    BAD_REQUEST_CODE = 400,
    SERVER_ERROR_CODE = 500,
    UNAUTHORIZED_CODE = 401,
    TOKEN_EXPIRED_CODE = 401,
    NOT_FOUND_CODE = 404,
    SERVER_ERROR = 'Oops! Something went wrong',
    TOKEN_EXPIRED = 'Your token is Invalid/expired. Please login again',
    INSUFFICIENT_PARAMETER = 'Insufficient parameters have been passed',
    UNAUTHORIZED_ACCESS = 'Unauthorize access',
    UNAUTHORIZED_URL = 'You are unauthorized user to access this url'
}

export enum adminRole {
    superAdmin = 'super_admin',
    subAdmin = 'sub_admin',
    vendor = 'vendor',
    user = 'user'
}

export enum stockStatus {
    available = 'AVAILABLE',
    hold = 'HOLD',
    sold = 'SOLD',
    memo = 'MEMO',
    returned = 'RETURNED',
    onHold = 'ON HOLD',
    onMemo = 'ON MEMO'
}

export enum cartItemType {
    diamond = 'diamond',
    jewellery = 'jewellery',
    melee = 'melee',
}

export enum buyRequestStatus {
    pending = 'PENDING',
    accepted = 'ACCEPTED',
    updated = 'UPDATED',
    cancelled = 'CANCELED',
    autoCanceled = 'AUTO-CANCELED'
}

export enum buyRequestType {
    offer = 'OFFER',
    normal = 'NORMAL'
}

export enum orderStatus {
    pending = 'PENDING',
    processing = 'PROCESSING',
    shipped = 'SHIPPED',
    delivered = 'DELIVERED',
    canceled = 'CANCELED'
}

export enum unifiedOrderType {
    DIAMONDS = 'DIAMONDS',
    JEWELLERY = 'JEWELLERY',
    MELEE = 'MELEE'
}

export enum paymentType {
    credit = 'CREDIT',
    cash = 'CASH'
}

export enum paymentStatus {
    pending = 'PENDING',
    shipped = 'PAID',
    paid = 'FAILED',
    cancelled = 'CANCELED'
}

export enum vendorOrderStatus {
    pending = 'PENDING',
    shipped = 'SHIPPED',
    paid = 'PAID',
    delivered = 'DELIVERED',
    canceled = 'CANCELED'
}

export enum vendorRejectedHoldRequestStatus {
    accepted = 'ACCEPTED',
    declined = 'DECLINED',
}

export enum transactionStatus {
    settled = 'settledSuccessfully',
    expired = 'expired'
}

export enum applePayRefundStatus {
    pending = 'PENDING',
    success = 'SUCCESS',
    error = 'ERROR'
}

export enum AuthorizeTransactionType {
    authCaptureTransaction = 'authCaptureTransaction', /// charge credit card
    authOnlyTransaction = 'authOnlyTransaction', /// authorize credit card
    priorAuthCaptureTransaction = 'priorAuthCaptureTransaction', /// capture previously authorized card
    voidTransaction = 'voidTransaction', /// voidTransaction,
    refundTransaction = 'refundTransaction' /// refundTransaction
}

export enum AuthorizePaymentStatus {
    createCustomer = 'CREATE-CUSTOMER',
    authorized = 'AUTHORIZE',
    reAuthorized = 'RE-AUTHORIZE',
    capture = 'CAPTURE',
    void = 'VOID',
    refund = 'REFUND',
    createApplePay = 'CREATE-APPLE-PAY',
    createApplePayError = 'CREATE-APPLE-PAY-ERROR',
    authorizedError = 'AUTHORIZE-ERROR',
    reAuthorizedError = 'RE-AUTHORIZE-ERROR',
    captureError = 'CAPTURE-ERROR',
    voidError = 'VOID-ERROR',
    refundError = 'REFUND-ERROR',
    createCustomerError = 'CREATE-CUSTOMER-ERROR'
}

export enum ReturnedByType {
    user = 'USER',
    admin = 'ADMIN',
}

export enum PaymentMode {
    creditLimit = 'CREDIT_LIMIT',
    creditCard = 'CREDIT_CARD',
    applePay = 'APPLE_PAY'
}

export enum ReturnOrderStatus {
    pending = 'PENDING',
    clientShipped = 'CLIENT-SHIPPED',
    vendorShipped = 'VENDOR-SHIPPED',
    received = 'RECEIVED'
}

export enum ViolationType {
    DUPLICATE_DIAMOND = 'DUPLICATE_DIAMOND',
    UNFULFILLED_ORDER = 'UNFULFILLED_ORDER',
    LATE_SHIPMENT = 'LATE_SHIPMENT'
}

export enum NotificationType {
    buyRequestCreated = 'BUY_REQUEST_CREATED',
    buyRequestUpdated = 'BUY_REQUEST_UPDATED',
    orderCreated = 'ORDER_CREATED',
    buyRequestRejected = 'BUY_REQUEST_REJECTED',
    orderCanceled = 'ORDER_CANCELED',
    orderDelivered = 'ORDER_DELIVERED',
    orderShipped = 'ORDER_SHIPPED',
    stockUpdate = 'STOCK_UPDATED',
    stockPriceIncreased = 'STOCK_PRICE_INCREASED',
    stockPriceDecreased = 'STOCK_PRICE_DECREASED',
    stockAvailable = 'STOCK_AVAILABLE',
    stockHold = 'STOCK_HOLD',
    stockSold = 'STOCK_SOLD',
    KYCRejected = 'KYC_REJECTED',
    KYCAccepted = 'KYC_ACCEPTED',
    creditLimitAdded = 'CREDIT_LIMIT_ADDED',
    invoiceAvailable = 'INVOICE_AVAILABLE',
    invoiceAccepted = 'INVOICE_ACCEPTED',
    invoiceRejected = 'INVOICE_REJECTED',
    raiseInvoice = 'RAISE_INVOICE',
    returnOrderAccepted = 'RETURN_ORDER_ACCEPTED',
    returnOrderRejected = 'RETURN_ORDER_REJECTED',
    returnOrderInitiated = 'RETURN_ORDER_INITIATED',
    offerCreated = 'OFFER_CREATED',
    offerAccepted = 'OFFER_ACCEPTED',
    offerRejected = 'OFFER_REJECTED',
    offerRevised = 'OFFER_REVISED',
    vendorOrderPaid = 'VENDOR_ORDER_PAID',
    vendorOrderShipped = 'VENDOR_ORDER_SHIPPED',
    vendorOrderRejected = 'VENDOR_ORDER_REJECTED',
    inquiryMessage = 'INQUIRY_MESSAGE',
    diamondReturned = 'DIAMOND_RETURNED'
}

export enum NotificationMessage {
    buyRequestCreated = 'Buy Request Created',
    buyRequestUpdated = 'Buy Request Updated',
    orderCreated = 'Order Created',
    buyRequestRejected = 'Order Rejected',
    orderCanceled = 'ORDER Canceled',
    orderDelivered = 'ORDER Delivered',
    orderShipped = 'ORDER Shipped',
    stockUpdate = 'Stock Updated',
    KYCRejected = 'KYC Rejected',
    KYCAccepted = 'KYC Accepted',
    creditLimitAdded = 'Credit Limit Added',
    stockPriceIncreased = 'Stock Price Increased',
    stockPriceDecreased = 'Stock Price Decreased',
    stockIsAvailable = 'Stock is Available',
    stockOnHold = 'Stock is on Hold',
    stockSoldOut = 'Stock is Sold Out',
    invoiceAvailable = 'Invoice Available',
    invoiceAccepted = 'Invoice Accepted',
    invoiceRejected = 'Invoice Rejected',
    raiseInvoice = 'Raise Invoice',
    diamondReturned = 'Diamond Returned',
    returnOrderAccepted = 'Return Order Accepted',
    returnOrderRejected = 'Return Order Rejected',
    returnOrderInitiated = 'Return Order Initiated',
    offerAccepted = 'Offer Accepted',
    offerRejected = 'Offer Rejected',
    offerRevised = 'Offer Revised',
    offerReceived = 'Offer Received',
    offerCreated = 'Offer Created',
    vendorOrderPaid = 'Vendor Order Paid',
    vendorOrderShipped = 'Vendor Order Shipped',
    vendorOrderRejected = 'Order Rejected',
    inquiryMessage = 'Inquiry Message'
}

export enum OfferActionType {
    user = 'USER',
    vendor = 'VENDOR',
    admin = 'ADMIN',
    adminVendor = 'ADMIN_VENDOR'
}

export enum OfferStatus {
    pending = 'PENDING',
    accepted = 'ACCEPTED',
    rejected = 'REJECTED',
    revised = 'REVISED'
}

export const getNotificationType = (status: any) => {
    switch (status) {
        case buyRequestStatus.updated:
            return NotificationType.buyRequestUpdated;
        case buyRequestStatus.accepted:
            return NotificationType.orderCreated;
        case buyRequestStatus.cancelled:
            return NotificationType.buyRequestRejected;
        case orderStatus.delivered:
            return NotificationType.orderDelivered;
        case orderStatus.shipped:
            return NotificationType.orderShipped;
        default:
            return '';
    }
};

export const getNotificationMessage = (type: any) => {
    switch (type) {
        case NotificationType.buyRequestUpdated:
            return NotificationMessage.buyRequestUpdated;
        case NotificationType.buyRequestCreated:
            return NotificationMessage.buyRequestCreated;
        case NotificationType.orderCreated:
            return NotificationMessage.orderCreated;
        case NotificationType.buyRequestRejected:
            return NotificationMessage.buyRequestRejected;
        case NotificationType.orderCanceled:
            return NotificationMessage.orderCanceled;
        case NotificationType.orderDelivered:
            return NotificationMessage.orderDelivered;
        case NotificationType.orderShipped:
            return NotificationMessage.orderShipped;
        case NotificationType.KYCAccepted:
            return NotificationMessage.KYCAccepted;
        case NotificationType.KYCRejected:
            return NotificationMessage.KYCRejected;
        case NotificationType.creditLimitAdded:
            return NotificationMessage.creditLimitAdded;
        case NotificationType.stockUpdate:
            return NotificationMessage.stockUpdate;
        case NotificationType.stockPriceIncreased:
            return NotificationMessage.stockPriceIncreased;
        case NotificationType.stockPriceDecreased:
            return NotificationMessage.stockPriceDecreased;
        case NotificationType.stockAvailable:
            return NotificationMessage.stockIsAvailable;
        case NotificationType.stockHold:
            return NotificationMessage.stockOnHold;
        case NotificationType.stockSold:
            return NotificationMessage.stockSoldOut;
        case NotificationType.invoiceAvailable:
            return NotificationMessage.invoiceAvailable;
        case NotificationType.invoiceAccepted:
            return NotificationMessage.invoiceAccepted;
        case NotificationType.invoiceRejected:
            return NotificationMessage.invoiceRejected;
        case NotificationType.raiseInvoice:
            return NotificationMessage.raiseInvoice;
        case NotificationType.returnOrderAccepted:
            return NotificationMessage.returnOrderAccepted;
        case NotificationType.returnOrderRejected:
            return NotificationMessage.returnOrderRejected;
        case NotificationType.offerAccepted:
            return NotificationMessage.offerAccepted;
        case NotificationType.offerRejected:
            return NotificationMessage.offerRejected;
        case NotificationType.offerRevised:
            return NotificationMessage.offerReceived;
        case NotificationType.offerCreated:
            return NotificationMessage.offerCreated;
        case NotificationType.vendorOrderShipped:
            return NotificationMessage.vendorOrderShipped;
        case NotificationType.vendorOrderRejected:
            return NotificationMessage.vendorOrderRejected;
        case NotificationType.vendorOrderPaid:
            return NotificationMessage.vendorOrderPaid;
        case NotificationType.inquiryMessage:
            return NotificationMessage.inquiryMessage;
        case NotificationType.diamondReturned:
            return NotificationMessage.diamondReturned;
        case NotificationType.returnOrderInitiated:
            return NotificationMessage.returnOrderInitiated;
        default:
            return '';
    }
};

export const whiteColor = [
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z'
];

export const fancyColor = [
    'yellow',
    'pink',
    'blue',
    'red',
    'green',
    'purple',
    'orange',
    'violate',
    'black',
    'grey',
    'brown',
    'white',
    'champagne',
    'cognac',
    'chameleon'
];

export const shapeDisplayNames = {
    round: 'Round',
    pear: 'Pear',
    emerald: 'Emerald',
    trilliant: 'Trilliant',
    princess: 'Princess',
    marquise: 'Marquise',
    asscher: 'Asscher',
    cushion: 'Cushion',
    cush_mod: 'Cushion Modified',
    cush_brill: 'Cushion Brilliant',
    heart: 'Heart',
    oval: 'Oval',
    radiant: 'Radiant',
    square: 'Square',
    euro_cut: 'Euro Cut',
    old_miner: 'Old Miner',
    briolette: 'Briolette',
    rose_cut: 'Rose Cut',
    lozenge: 'Lozenge',
    baguette: 'Baguette',
    t_baguette: 'T.Baguette',
    half_moon: 'Half Moon',
    flanders: 'Flanders',
    trapezoid: 'Trapezoid',
    bullets: 'Bullets',
    kite: 'Kite',
    shield: 'Shield',
    star: 'Star',
    pentagonal: 'Pentagonal',
    hexagonal: 'Hexagonal',
    octagonal: 'Octagonal'
};

export const cutDisplayNames = {
    id: 'ID',
    ex: 'EX',
    vg: 'VG',
    gd: 'GD',
    f: 'FR',
    p: 'Poor',
    na: 'NA'
};

export const intensityDisplayNames = {
    na: 'NA',
    vsl: 'VSLT',
    sl: 'SLT',
    f: 'FT',
    m: 'MED',
    s: 'STG',
    vs: 'VSTG'
};

export const roundAlias = {
    round: ['B', 'BR', 'RB', 'RD', 'RBC', 'RND', 'ROUND', 'BRILLIANT', 'ROUND BRILLIANT'],
    pear: ['P', 'PS', 'PSH', 'PB', 'PMB', 'PEAR', 'PEAR BRILLIANT', 'PEAR BR', 'PEAR SHAPE', 'PSH', 'PB', 'PMB'],
    emerald: [
        'EMERALD',
        'E',
        'EM',
        'EC',
        'SQUAREEMERALD',
        'SQUARE EMERALD',
        'SQEMERALD',
        'SQ EMERALD',
        'SQEM',
        'SX',
        'SQE',
        'SEC',
        'SQEC',
        'SQ EM',
        'SQUARE EM'
    ],
    trilliant: ['TRILLIANT', 'T', 'TR', 'TRILL', 'TRIL', 'TRIB', 'TRL', 'MTRI', 'TRMB', 'TRSC'],
    princess: ['PRN', 'PR', 'PRINCESS', 'PRIN', 'PN', 'PC', 'MDSQB', 'SMB', 'PRINCESS CUT'],
    marquise: ['MARQUISE', 'MQB', 'M', 'MQ', 'MARQ'],
    asscher: ['ASSCHER', 'A', 'ASH', 'CSS', 'CSSC', 'AC', 'AS'],
    cushion: [
        'CB',
        'CUX',
        'RCRMB',
        'CRC',
        'CSC',
        'CX',
        'RCSB',
        'SCMB',
        'SCX',
        'CUSHION',
        'CU',
        'C',
        'CUSH',
        'CUSHIONBRILLIANT',
        'CUSHION BRILLIANT',
        'CUBR',
        'CUB',
        'CUSHION BR',
        'CUSHIONMODIFIED',
        'CUSHION MODIFIED',
        'CM',
        'CUSHION MOD',
        'CUS',
        'CUS MOD',
        'CUSHION MODIFIED BRILLIANT',
        'CUSHION MOD BR',
        'CUS MOD BR',
        'CMB'
    ],
    cush_mod: ['Cush Mod'],
    cush_brill: ['Cush Brill'],
    heart: ['HEART', 'H', 'HS', 'HT', 'MHRC', 'HEART SHAPE', 'HEART BRILLIANT', 'HEART BR'],
    oval: ['OVAL', 'O', 'OV', 'OMB', 'OS', 'OVAL BRILLIANT', 'OVAL BR', 'OVAL SHAPE', 'OVAL BR'],
    radiant: [
        'RADIANT',
        'LG-RADIANT',
        'R',
        'RAD',
        'RA',
        'RC',
        'RDN',
        'CRB',
        'RCRB',
        'cut cornered rectangular modified brilliant',
        'SQUARERADIANT',
        'SQR',
        'CCSMB',
        'SQUARE RADIANT',
        'SQ RA',
        'SQ RAD',
        'SQ RADIANT',
        'SQRADIANT',
        'SQUARE RA'
    ],
    square: ['square'],
    euro_cut: ['EURO CUT', 'EURO', 'EU'],
    old_miner: ['OLD MINER', 'MINER', 'OM', 'OLD MIN'],
    briolette: ['BRIOLETTE', 'BRIO'],
    rose_cut: ['ROSE CUT', 'ROSE', 'RC', 'ROSE CUT'],
    lozenge: ['LOZENGE', 'LOZ', 'LO'],
    baguette: ['BAGUETTE', 'BG', 'BAG', 'BAGU'],
    t_baguette: ['T.BAGUETTE', 'T-BAG', 'TBAG'],
    half_moon: ['HALF MOON', 'HALF', 'HM'],
    flanders: ['FLANDERS', 'FL', 'F'],
    trapezoid: ['TRAPEZOID', 'TRAP', 'TR'],
    bullets: ['BULLETS', 'BUL', 'B'],
    kite: ['KITE', 'KI', 'K'],
    shield: ['SHIELD', 'SHI', 'S'],
    star: ['STAR', 'ST', 'S'],
    pentagonal: ['PENTAGONAL', 'PENTA', 'PEN'],
    hexagonal: ['HEXAGONAL', 'HEX', 'H'],
    octagonal: ['OCTAGONAL', 'OCT', 'O']
};

export const cutAlias = {
    id: ['ID', 'I', 'IDEAL'],
    ex: ['X', 'EX', 'EXCELLENT', 'EXCEL', 'EXCELL'],
    vg: ['VG', 'Very G', 'V GOOD', 'VERY GOOD', 'V. GOOD'],
    gd: ['GD', 'G', 'GOOD'],
    f: ['F', 'FAIR'],
    p: ['P', 'POOR'],
    na: ['null', 'none', 'NA']
};

export const intensityAlias = {
    n: ['N', 'NONE'],
    vsl: ['VSL', 'VERY SLIGHT'],
    sl: ['SL', 'SLIGHT', 's', 'slt'],
    f: ['FAINT', 'F', 'fnt'],
    m: ['MEDIUM', 'M', 'med'],
    s: ['STRONG', 'S', 'stg'],
    vs: ['VERY STRONG', 'VS'],
    na: ['null', 'none', 'n', 'n/a']
};

export const fluorescenceColorAlias = {
    na: ['0', 'NONE', 'non', 'null'],
    blue: ['b', 'blue'],
    slt: ['SL', 'SLIGHT'],
    vslt: ['vsl', 'very slight'],
    yellow: ['y', 'yellow']
};

export const fluorescenceColorDisplayNames = {
    na: 'NA',
    blue: 'Blue',
    slt: 'SLT',
    vslt: 'VSTL',
    yellow: 'Yellow'
};

export const countryAlias = {
    'new york': ['new york city', 'new york', 'nyc', 'ny'],
    'united states': ['us', 'usa', 'united states', 'united states of america'],
    'hong kong': ['hong kong', 'hongkong', 'hk', 'tsim sha tsui'],
    india: ['india', 'in', 'ind'],
    'united arab emirates': ['united arab emirates', 'dubai', 'uae', 'UAE'],
    canada: ['canada', 'montrel'],
    'united kingdom': ['uk', 'gb', 'u.k.'],
    china: ['china', 'shenzhen'],
    australia: ['australia', 'opera house'],
    mumbai: ['mumbai', 'bombay'],
    delhi: ['delhi', 'new delhi'],
    surat: ['surat']
};

// export const makeUniqueKey = (length: number) => {
//     const result: any = [];
//     const characters: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
//     const charactersLength: number = characters.length;
//     for (let i = 0; i < length; i++) {
//         result.push(characters.charAt(Math.floor(Math.random() * charactersLength)));
//     }

//     return result.join('');
// };

export const makeUniqueKey = (length: number): string => {
    return randomBytes(length).toString('hex').slice(0, length).toUpperCase();
};

export const generateStrongPassword = (length: number): string => {
    if (length < 4) {
        throw new Error('Password length must be at least 4 to ensure strength.');
    }

    const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '@#$%&*';

    // Ensure each character type is included at least once
    let password = '';
    password += upperCase[Math.floor(Math.random() * upperCase.length)];
    password += lowerCase[Math.floor(Math.random() * lowerCase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // Create the remaining characters from all available characters
    const allCharacters = upperCase + lowerCase + numbers + specialChars;
    for (let i = 4; i < length; i++) {
        password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
    }

    // Shuffle the password to avoid predictable patterns
    password = password
        .split('')
        .sort(() => Math.random() - 0.5)
        .join('');

    return password;
};

export const calculateFinancialYear = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();

    // Determine the start year of the financial year
    const startYear = month > 2 ? year : year - 1;

    // Determine the end year of the financial year
    const endYear = startYear + 1;

    // Extract the last two digits of the start year and end year
    const startYearLastTwoDigits = startYear.toString().slice(-2);
    const endYearLastTwoDigits = endYear.toString().slice(-2);

    // Format the financial year as a string
    const financialYear = `FY${startYearLastTwoDigits}-${endYearLastTwoDigits}`;

    return financialYear;
};

export const getInvoiceNumber = (lastInvoice: any) => {
    if (!lastInvoice) {
        return '001';
    }

    // Extracting the numeric part
    const matchResult = lastInvoice.invoice_number.match(/\d+/);
    const numericPart = matchResult ? matchResult[0] : '';

    // Converting the numeric part to a number
    let numericValue = parseInt(numericPart, 10); // Parses the numeric part as a base-10 integer

    numericValue = numericValue + 1;

    if (lastInvoice.financial_year !== calculateFinancialYear()) {
        numericValue = 1;
    }

    // Adding prefix based on the length of the number
    const paddedNumericValue: string =
        numericValue < 10 ? numericValue.toString().padStart(3, '00') : numericValue.toString().padStart(3, '0');

    return paddedNumericValue;
};

export const cleanObject = (obj: any) => {
    if (Array.isArray(obj)) {
        // Filter out empty arrays and clean each item
        return obj
            .map(cleanObject)
            .filter((value) => value !== null && value !== '' && !(Array.isArray(value) && value.length === 0));
    } else if (obj !== null && typeof obj === 'object') {
        return Object.fromEntries(
            Object.entries(obj)
                .map(([key, value]) => [key, cleanObject(value)])
                .filter(
                    ([key, value]) => value !== null && value !== '' && !(Array.isArray(value) && value.length === 0)
                )
        );
    }
    return obj;
};

// export const isStocksAvailable = (stock: any) => {
//     if (stock.admin_id) {
//         if (stock.is_available) {
//             return true;
//         }
//     } else if (stock.vendor_id) {
//         if (stock.is_action_taken) {
//             if (stock.is_available) {
//                 return true;
//             }
//         }
//     }
//     return false;
// };

export const isStocksAvailable = (stock: any) => {
    return stock.is_available;
};

export const isImageUrl = (url: any) => {
    return (url ?? '').match(/\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff|tif|ico|heic|heif|jfif)$/i) != null;
};

export const statusNames = {
    available: ['AVAILABLE', 'AVAIL', 'A', 'STOCK'],
    memo: ['MEMO', 'M', 'ON MEMO', 'ONMEMO'],
    hold: ['HOLD', 'H', 'ON HOLD', 'ONHOLD'],
    sold: ['SOLD', 'S', 'ON SOLD', 'ONSOLD']
};

export const naturalAlias = ['natural', 'n', 'nat', 'natrl', 'natr'];
