import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import { httpStatusCodes, orderStatus } from '../../utils/constants';
import models from '../../models';
import { Op } from 'sequelize';
import moment from 'moment';
import userNotifications from '../user/user_notifications/user_notification';
import policyViolationsService from '../admin/policy_violations';

class ReturnOrder {
    /**
     * @api {post} /v1/auth/user/return-order
     * @apiName createReturnOrder
     * @apiGroup ReturnOrder
     *
     *
     * @apiSuccess {Object} Vendors.
     */
    async createReturnOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!return order function start!!!!!');
        try {
            const { return_stock_ids, order_id } = req.body;
            const userId = req[`id`];

            const order: any = await models.orders.findOne({ where: { id: order_id } });

            if (!order) {
                throw new Error('Order not found!!');
            }

            if (order?.user_id !== userId) {
                throw new Error('Unauthorized access');
            }

            if (order.order_status !== orderStatus.delivered) {
                throw new Error('Order not delivered!!');
            }

            const orderValidity = Math.abs(moment(order.createdAt).diff(moment(), 'days'));

            if (parseInt(process.env.RETURN_ORDER_VALIDITY || '0', 10) < parseInt(orderValidity.toString(), 10)) {
                throw new Error('Cannot return order, validity exceeded!!');
            }

            const stock_ids: any = return_stock_ids.map((item: any) => item.stock_id);

            const stocks: any = await models.return_orders.findAll({ where: { stock_id: { [Op.in]: stock_ids } } });

            if (stocks.length) {
                throw new Error('Stock already exists in return list!!!');
            }

            const bulkCreateObject: any[] = return_stock_ids.map((item: any) => ({
                order_id,
                user_id: userId,
                stock_id: item.stock_id,
                vendor_id: item?.vendor_id,
                admin_id: item?.admin_id,
                status: 'PENDING',
                reason: item?.reason,
                is_accepted: false,
                is_action_taken: false
            }));

            logger.info('Creating return orders');

            await models.return_orders.bulkCreate(bulkCreateObject);

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Return order initiated successfully'
            });

            try {
                /// return order initiated email to user
                userNotifications.returnRequestInitiatedNotificationToUser(order_id);
            } catch (error: any) {
                logger.error(error);
            }

            // try {
            //     /// create unfulfilled order violation
            //     policyViolationsService.createUnfulfilledOrderViolation(order_id, return_stock_ids);

            // } catch (error: any) {
            //     logger.error(`Error creating unfulfilled order violation`);
            //     // Don't throw error to avoid breaking the return order process
            // }

            // try {
            //     /// return order initiated email to vendor
            //     const vendorsIds: any[] = return_stock_ids
            //         .filter((item: any) => item.vendor_id)
            //         .map((item: any) => item.vendor_id);

            //     if (vendorsIds.length) {
            //         userNotifications.returnRequestInitiatedNotificationToVendor(vendorsIds, order_id);
            //     }
            // } catch (error: any) {
            //     logger.error(error);
            // }

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/user/return-order
     * @apiName ReturnOrder
     * @apiGroup ReturnOrder
     *
     *
     * @apiSuccess {Object} ReturnOrder.
     */
    async listReturnOrder(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!return order function start!!!!!');
        try {
            const userId = req[`id`];
            const skip = req.query.skip;
            const limit = req.query.limit;

            const { rows, count } = await models.return_orders.findAndCountAll({
                where: { user_id: userId },
                include: [
                    { model: models.orders, attributes: ['id', 'order_code', 'type'] },
                    {
                        model: models.stocks,
                        attributes: [
                            'id',
                            'stock_id',
                            'status',
                            'weight',
                            'color',
                            'clarity',
                            'shape',
                            'cut',
                            'polish',
                            'symmetry',
                            'fluorescence_intensity',
                            'discounts',
                            'discounts_ori',
                            'price_per_caret',
                            'price_per_caret_ori',
                            'final_price',
                            'final_price_ori',
                            'admin_id',
                            'vendor_id',
                            'sku_number',
                            'stock_margin'
                        ]
                    }
                ],
                offset: skip,
                limit,
                order: [['updatedAt', 'DESC']]
            });

            if (!rows.length) {
                throw new Error('Return orders not found!!');
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Return order listed successfully',
                data: rows,
                count
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new ReturnOrder();
