import Joi from 'joi';

export const createOfferSchema = Joi.object({
    stock_id: Joi.string().uuid().required(),
    offer_price: Joi.number().required()
});

export const updateOfferSchema = Joi.object({
    offer_id: Joi.string().uuid().required(),
    offer_price: Joi.when('status', {
        is: 'PENDING',
        then: Joi.number().required(),
        otherwise: Joi.number().optional()
    }),
    status: Joi.string().valid('PENDING', 'ACCEPTED', 'REJECTED').required()
});

export const placeOrderSchema = Joi.object({
    offer_id: Joi.string().uuid().required(),
    payment_mode: Joi.string().valid('CREDIT_LIMIT', 'CREDIT_CARD', 'APPLE_PAY').required(),
    shipping_address: Joi.object().required(),
    billing_address: Joi.object().required(),
    shipment_id: Joi.string().uuid().required(),
    shipment_price: Joi.number().required(),
    card_holder_name: Joi.string()
        .regex(/^[A-Z\s]+$/)
        .uppercase()
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.empty': 'Card holder name cannot be empty',
            'string.pattern.base': 'Card holder name must only contain uppercase letters and spaces'
        }),
    card_number: Joi.string().creditCard().when('payment_mode', {
        is: 'CREDIT_CARD',
        then: Joi.required()
    }),
    exp_date: Joi.string()
        .pattern(/^(20[2-9][0-9]|2100)-(0[1-9]|1[0-2])$/)
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.pattern.base': 'Expiration Date must be in YYYY-MM format'
        }),
    cvv: Joi.string()
        .pattern(/^[0-9]{3,4}$/)
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.pattern.base': 'CVV must be 3 or 4 digits'
        }),
    apple_pay_response: Joi.object().when('payment_mode', {
        is: 'APPLE_PAY',
        then: Joi.required()
    })
});
