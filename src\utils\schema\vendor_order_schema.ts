import Joi from 'joi';

export const updateVendorOrderSchema = Joi.object({
    ids: Joi.array().items(Joi.string().uuid().required()).required(),
    status: Joi.string().valid('PENDING', 'SHIPPED', 'PAID', 'DELIVERED').required(),
    dollar_rate: Joi.number().when('status', {
        is: 'PAID',
        then: Joi.required(), // Make dollar_rate required when status is 'PAID'
        otherwise: Joi.allow(null).optional() // Otherwise, dollar_rate is optional
    })
});
