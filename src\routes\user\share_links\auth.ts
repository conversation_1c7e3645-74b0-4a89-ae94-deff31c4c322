import { IRouter, Router, Request, Response, NextFunction } from 'express';
import shareLinks from '../../../controllers/user/share_links';
import { fieldsValidator } from '../../../middlewares/validator';
import { shareLinkSchema } from '../../../utils/schema/share_links_schema';

const routes: IRouter = Router();

// create share link
routes.post(
    '/share-links',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, shareLinkSchema),
    shareLinks.createShareLink
);

// get share-links
routes.get('/share-links', shareLinks.getShareLinks);

export default routes;
