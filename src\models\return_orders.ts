import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface ReturnOrderAttributes {
    id?: string;
    order_id: string;
    user_id: string;
    vendor_id?: string;
    admin_id?: string;
    stock_id: string;
    status: string; // pending, client-shipped, vendor-shipped, received
    refund_amount: number;
    vendor_refund_amount: number;
    received_amount: number;
    is_accepted: false;
    is_action_taken: false;
    reason: string;
    reject_reason: string;
    returned_by_type: string; // user, admin
    returned_by_id: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface ReturnOrderCreationAttributes extends Optional<ReturnOrderAttributes, 'id'> { }

interface ReturnOrderInstance
    extends Model<ReturnOrderAttributes, ReturnOrderCreationAttributes>,
    ReturnOrderAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type ReturnOrderStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => ReturnOrderInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const return_orders = sequelize.define<ReturnOrderInstance>(
        'return_orders',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            status: {
                type: DataTypes.ENUM('PENDING', 'CLIENT-SHIPPED', 'VENDOR-SHIPPED', 'RECEIVED'),
                allowNull: false
            },
            refund_amount: {
                type: DataTypes.DOUBLE,
                allowNull: true,
                defaultValue: 0
            },
            vendor_refund_amount: {
                type: DataTypes.DOUBLE,
                allowNull: true,
                defaultValue: 0
            },
            received_amount: {
                type: DataTypes.DOUBLE,
                allowNull: true,
                defaultValue: 0
            },
            is_accepted: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_action_taken: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            reason: {
                type: DataTypes.STRING,
                allowNull: false
            },
            reject_reason: {
                type: DataTypes.STRING,
                allowNull: true
            },
            returned_by_type: {
                type: DataTypes.STRING,
                allowNull: true
            },
            returned_by_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as ReturnOrderStatic;

    return_orders.associate = (models) => {
        return_orders.belongsTo(models.orders, {
            foreignKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        return_orders.belongsTo(models.vendors, {
            foreignKey: 'vendor_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        return_orders.belongsTo(models.stocks, {
            foreignKey: 'stock_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        return_orders.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    // TODO: make common function to sync
    // await return_orders.sync({ alter: true });

    return return_orders;
};
