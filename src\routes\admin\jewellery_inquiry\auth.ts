import { IRouter, NextFunction, Request, Response, Router } from 'express';
import jewelleryInquiry from '../../../controllers/admin/jewellery_inquiry';
import { fieldsValidator } from '../../../middlewares/validator';
import { updateJewelleryInquirySchema } from '../../../utils/schema/jewellery_inquiry_schema';

const routes: IRouter = Router();

// update inquiry
routes.put(
    '/update-inquiry',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, updateJewelleryInquirySchema),
    jewelleryInquiry.updateInquiry
);

// list inquiries
routes.get('/list-inquiries', jewelleryInquiry.listInquiries);

// inquiry details
routes.get('/inquiry-details', jewelleryInquiry.inquiryDetails);

export default routes;
