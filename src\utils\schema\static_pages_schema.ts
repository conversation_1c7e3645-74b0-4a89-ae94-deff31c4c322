import Joi from 'joi';

export const createStaticPageSchema = Joi.object({
    slug: Joi.string()
        .optional()
        .custom((value, helpers) => {
            return value ? value.toLowerCase() : value;
        }),
    title: Joi.string().required(),
    content: Joi.string().required()
});

export const updateStaticPageSchema = Joi.object({
    id: Joi.string().uuid().required(),
    slug: Joi.string()
        .optional()
        .custom((value, helpers) => {
            return value ? value.toLowerCase() : value;
        }),
    title: Joi.string().optional(),
    content: Joi.string().optional()
});

export const changeStaticPageStatusSchema = Joi.object({
    id: Joi.string().uuid().required(),
    is_active: Joi.boolean().required()
});
