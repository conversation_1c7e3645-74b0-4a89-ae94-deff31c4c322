import { Request, Response, NextFunction } from 'express';
import { adminRole, httpStatusCodes } from '../utils/constants';

export async function adminRoleHandler(req: Request, res: Response, next: NextFunction) {
    try {
        if (req[`role`] !== adminRole.superAdmin) {
            throw new Error('Unauthorized access');
        }

        next();
    } catch (error: any) {
        res.json({
            status: httpStatusCodes.UNAUTHORIZED_CODE,
            message: httpStatusCodes.UNAUTHORIZED_ACCESS,
            error
        });
        return;
    }
}

export default adminRoleHandler;
