import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';
import { adminRole, buyRequestType, httpStatusCodes, orderStatus, vendorOrderStatus } from '../../utils/constants';
import models from '../../models';
import { col, fn, Op } from 'sequelize';
import moment from 'moment';

class SalesReporting {
    /**
     * @api {get} /v1/auth/admin/sales-reporting
     * @apiName SalesReporting
     * @apiGroup SalesReporting
     *
     *
     * @apiSuccess {Object} SalesReporting.
     */
    async listSalesReporting(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!list sales reporting function start!!!!!');
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            let start_date = req.query.start_date;
            let end_date = req.query.end_date;
            const role = req[`role`];

            if (![adminRole.superAdmin, adminRole.subAdmin].includes(role)) {
                throw new Error('Unauthorized error!!');
            }

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (!start_date || !end_date) {
                throw new Error('Start date or end date required!!!');
            }

            if (start_date) {
                if (!moment(start_date.toString(), 'YYYY-MM-DD', true).isValid()) {
                    throw new Error(`date format must be YYYY-MM-DD`);
                }
            }

            if (end_date) {
                if (!moment(end_date.toString(), 'YYYY-MM-DD', true).isValid()) {
                    throw new Error(`date format must be YYYY-MM-DD`);
                }
            }

            // Outputs: 2024-09-10 00:00:00
            start_date = moment(start_date.toString()).startOf('day').format('YYYY-MM-DD HH:mm:ss');

            // Add end time (23:59:59) to end_date
            end_date = moment(end_date.toString()).endOf('day').format('YYYY-MM-DD HH:mm:ss');

            /// fetch orders
            const { rows, count } = await models.orders.findAndCountAll({
                where: {
                    [Op.and]: [
                        {
                            order_status: orderStatus.delivered
                        },
                        {
                            updatedAt: {
                                [Op.between]: [start_date, end_date]
                            }
                        }
                    ]
                },
                offset: skip,
                limit,
                order: [['createdAt', 'DESC']]
            });

            /// fetch total amount
            const totalAmount = await models.orders.sum('grand_total', {
                where: {
                    [Op.and]: [
                        {
                            order_status: orderStatus.delivered
                        },
                        {
                            updatedAt: {
                                [Op.between]: [start_date, end_date]
                            }
                        }
                    ]
                }
            });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Sales reporting successfully listed',
                data: rows,
                count,
                total_selling: parseFloat(parseFloat(totalAmount?.toString()).toFixed(2))
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/vendor-sales-reporting
     * @apiName VendorSalesReporting
     * @apiGroup VendorSalesReporting
     *
     *
     * @apiSuccess {Object} VendorSalesReporting.
     */
    async vendorSalesReport(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!list vendor sales reporting function start!!!!!');
        try {
            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            let start_date = req.query.start_date;
            let end_date = req.query.end_date;
            const role = req[`role`];
            const reqId = req[`id`];

            if (![adminRole.vendor].includes(role)) {
                throw new Error('Unauthorized error!!');
            }

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (!start_date || !end_date) {
                throw new Error('Start date or end date required!!!');
            }

            if (start_date) {
                if (!moment(start_date.toString(), 'YYYY-MM-DD', true).isValid()) {
                    throw new Error(`date format must be YYYY-MM-DD`);
                }
            }

            if (end_date) {
                if (!moment(end_date.toString(), 'YYYY-MM-DD', true).isValid()) {
                    throw new Error(`date format must be YYYY-MM-DD`);
                }
            }

            // Outputs: 2024-09-10 00:00:00
            start_date = moment(start_date.toString()).startOf('day').format('YYYY-MM-DD HH:mm:ss');

            // Add end time (23:59:59) to end_date
            end_date = moment(end_date.toString()).endOf('day').format('YYYY-MM-DD HH:mm:ss');

            /// fetch orders
            const { rows, count } = await models.vendor_orders.findAndCountAll({
                where: {
                    [Op.and]: [
                        {
                            vendor_id: reqId
                        },
                        {
                            status: { [Op.in]: [vendorOrderStatus.delivered, vendorOrderStatus.paid] }
                        },
                        {
                            updatedAt: {
                                [Op.between]: [start_date, end_date]
                            }
                        }
                    ]
                },
                offset: skip,
                limit,
                order: [['createdAt', 'DESC']]
            });

            /// stock ids
            const stockIds = JSON.parse(JSON.stringify(rows))
                .filter((item: any) => item?.stock_id)
                .map((vendorOrder: any) => vendorOrder?.stock_id);

            /// fetch all stocks
            const stocks: any = await models.stocks.findAll({
                where: {
                    id: {
                        [Op.in]: stockIds
                    }
                },
                attributes: [
                    'id',
                    'stock_id',
                    'status',
                    'weight',
                    'color',
                    'clarity',
                    'shape',
                    'cut',
                    'polish',
                    'symmetry',
                    'fluorescence_intensity',
                    'discounts',
                    'discounts_ori',
                    'price_per_caret',
                    'price_per_caret_ori',
                    'final_price',
                    'final_price_ori',
                    'admin_id',
                    'vendor_id',
                    'sku_number'
                ]
            });

            const orderIds = JSON.parse(JSON.stringify(rows))
                .filter((item: any) => item?.order_id)
                .map((vendorOrder: any) => vendorOrder?.order_id);

            const orders: any = await models.orders.findAll({
                where: {
                    id: {
                        [Op.in]: orderIds
                    }
                },
                attributes: ['id', 'order_code', 'type']
            });

            const updatedRows = JSON.parse(JSON.stringify(rows))
                .map((vendorOrder: any) => {
                    const stock = stocks.find((s: any) => s.id === vendorOrder.stock_id);
                    const order = orders.find((o: any) => o.id === vendorOrder.order_id);
                    return {
                        ...vendorOrder,
                        stock,
                        order
                    };
                })
                .filter((item: any) => item?.stock && item?.order);

            /// calculate total pending amount
            const totalAmountPending = updatedRows
                .filter((order) => order.status === vendorOrderStatus.delivered)
                .reduce((sum: number, order: any) => {
                    const stockFinalPriceOri = order.stock?.final_price_ori || 0;
                    return sum + stockFinalPriceOri;
                }, 0);

            /// calculate total paid amount
            const totalAmountPaid = updatedRows
                .filter((order) => order.status === vendorOrderStatus.paid)
                .reduce((sum: number, order: any) => {
                    const stockFinalPriceOri = order.stock?.final_price_ori || 0;
                    return sum + stockFinalPriceOri;
                }, 0);

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Sales reporting successfully listed',
                data: updatedRows,
                count,
                total_amount_pending: parseFloat(parseFloat(totalAmountPending?.toString()).toFixed(2)),
                total_amount_paid: parseFloat(parseFloat(totalAmountPaid?.toString()).toFixed(2))
            });

            return;
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new SalesReporting();
