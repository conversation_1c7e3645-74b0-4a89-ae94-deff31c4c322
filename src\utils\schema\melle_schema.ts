import Joi from 'joi';

export const addMelleSchema = Joi.object({
    melle_array: Joi.array()
        .items(
            Joi.object({
                shape: Joi.string().optional().allow(''), // String, optional field
                growth_type: Joi.string().optional().allow(''), // String, optional field
                color: Joi.string().optional().allow(''), // String, optional field
                clarity: Joi.string().optional().allow(''), // String, optional field
                sieve_size: Joi.string().optional().allow(''), // String, optional field
                milimeter: Joi.string().optional().allow(''), // String, optional field
                pointer: Joi.string().optional().allow(''), // String, optional field
                price_per_caret: Joi.string().optional().allow('') // Price as a positive number with precision 2, optional
            })
        )
        .optional()
}); // Array of MelleAttributes objects

export const getMellePricePerCaratSchema = Joi.object({
    shape: Joi.string().optional().allow(''), // String, optional field
    growth_type: Joi.string().optional().allow(''), // String, optional field
    color: Joi.string().optional().allow(''), // String, optional field
    clarity: Joi.string().optional().allow(''), // String, optional field
    sieve_size: Joi.string().optional().allow(''), // String, optional field
    milimeter: Joi.string().optional().allow(''), // String, optional field
    pointer: Joi.string().optional().allow('') // String, optional field
    // price_per_caret: Joi.string().optional().allow('') // Price as a positive number with precision 2, optional
}); // Array of MelleAttributes objects
