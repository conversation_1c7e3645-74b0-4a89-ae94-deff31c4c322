import { NextFunction, Request, Response } from 'express';
import models, { sequelize } from '../../models';
import { adminRole, httpStatusCodes } from '../../utils/constants';
import { logger } from '../../utils/logger';
import { Op } from 'sequelize';
import axios from 'axios';

class JewelleryInquiry {
    /**
     * @api {get} /v1/auth/admin/list-inquiries
     * @apiName JewelleryInquiry
     * @apiGroup JewelleryInquiry
     *
     *
     * @apiSuccess {Object} JewelleryInquiry.
     */
    async listInquiries(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req[`id`];
            const role = req[`role`];
            const skip = req.query.skip;
            const limit = req.query.limit;

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('unauthorized access!!!!');
            }

            /// fetch orders
            const { rows, count } = await models.jewellery_inquiries.findAndCountAll({
                where: { variant_price: 0 },
                offset: skip,
                limit,
                order: [['createdAt', 'DESC']]
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Inquiries listed successfully!!!',
                data: rows,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/admin/inquiry-details
     * @apiName JewelleryInquiryDetails
     * @apiGroup JewelleryInquiry
     *
     *
     * @apiSuccess {Object} JewelleryInquiry.
     */
    async inquiryDetails(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;
            const role = req[`role`];

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('unauthorized access!!!!');
            }
            if (!id) {
                throw new Error('id required!!');
            }

            /// fetch inquiry
            const inquiry = await models.jewellery_inquiries.findOne({
                where: { inquiry_id: id }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Inquiry details listed successfully!!!',
                inquiry
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/user/create-inquiry
     * @apiName JewelleryInquiry
     * @apiGroup JewelleryInquiry
     *
     *
     * @apiSuccess {Object} JewelleryInquiry.
     */
    async createInquiry(req: Request, res: Response, next: NextFunction) {
        logger.info('Calling createInquiry!!!!');
        try {
            const id = req[`id`];
            const role = req[`role`];
            const { product_id, variant_id, inquiry_id, product_details } = req.body;

            await models.jewellery_inquiries.create({ ...req.body });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Inquiry created successfully!!!'
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/auth/update-inquiry
     * @apiName JewelleryInquiry
     * @apiGroup JewelleryInquiry
     *
     *
     * @apiSuccess {Object} JewelleryInquiry.
     */
    async updateInquiry(req: Request, res: Response, next: NextFunction) {
        /// transaction
        const transaction = await sequelize.transaction();
        try {
            const role = req[`role`];
            const reqId = req[`id`];
            const { id, variant_price } = req.body;

            if (![adminRole.subAdmin, adminRole.superAdmin].includes(role)) {
                throw new Error('unauthorized access!!!!');
            }

            const inquiry = await models.jewellery_inquiries.findOne({ where: { inquiry_id: id } });

            if (!inquiry) {
                throw new Error(`Inquiry not found!!!`);
            }

            await models.jewellery_inquiries.update({ variant_price }, { where: { inquiry_id: id }, transaction });

            /// send checkout message
            await new Promise(async (resolve, reject) => {
                try {
                    /// post request
                    await axios.post(
                        process.env.CHECKOUT_MESSAGE_API ?? '',
                        {
                            room_id: inquiry?.inquiry_id,
                            admin_id: reqId,
                            message: JSON.stringify({
                                id: inquiry.id,
                                variant_id: inquiry.variant_id,
                                product_id: inquiry.product_id,
                                inquiry_id: inquiry.inquiry_id,
                                variant_price
                            })
                        },
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                authorization: process.env.ADD_STOCK_KEY
                            }
                        }
                    );

                    resolve(true);
                    ///
                } catch (error) {
                    logger.error(error);
                    reject(error);
                }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Inquiry updated successfully!!!'
            });

            await transaction.commit();
            ///
        } catch (error: any) {
            await transaction.rollback();
            logger.error(error);
            next(error);
        }
    }
}

export default new JewelleryInquiry();
