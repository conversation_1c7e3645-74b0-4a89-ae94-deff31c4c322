import { logger } from '../../../utils/logger';
import nodemailer from 'nodemailer';
import ejs from 'ejs';
import { contactEmail } from '../../../utils/constants';

class MailServices {
    private static instance: MailServices | null = null;

    constructor() {
        if (!MailServices.instance) {
            MailServices.instance = this;
        }

        return MailServices.instance;
    }
    /// send mail
    async send({ to, subject, data, cc }: { to: string; subject: string; data: any; cc?: string }) {
        try {
            const { MAIL_HOST, MAIL_PORT, MAIL_USERNAME, MAIL_PASSWORD, MAIL_SENDER } = process.env;

            // Path to the EJS template
            const templatePath = `./email_template.ejs`;

            // Render the template with the data
            const renderedHtml = await ejs.renderFile(templatePath, { payloadData: data });

            const transporter = nodemailer.createTransport({
                // service: 'gmail',
                host: MAIL_HOST,
                port: MAIL_PORT,
                secure: false,
                debug: true,
                logger: true,
                auth: {
                    user: MAIL_USERNAME,
                    pass: MAIL_PASSWORD
                },
                tls: {
                    rejectUnauthorized: false
                }
            });

            const info = await transporter.sendMail({
                from: MAIL_SENDER,
                to,
                cc: contactEmail,
                subject,
                html: renderedHtml
            });

            logger.info(`E-Mail sent: ${info.messageId}`);
        } catch (error) {
            logger.error('Error:- sendMail:-', error);
            // throw error;
        }
    }
}

export default new MailServices();
