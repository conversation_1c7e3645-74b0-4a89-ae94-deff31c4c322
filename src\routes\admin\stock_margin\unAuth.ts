import { Router, IRouter, Request, Response, NextFunction } from 'express';
import stockMargin from '../../../controllers/admin/stock_margin';
import { fieldsValidator } from '../../../middlewares/validator';
import { addStockMarginSchema } from '../../../utils/schema/stock_margin_schema';

const router: IRouter = Router();

router.post(
    '/stock-margin',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, addStockMarginSchema),
    stockMargin.addStockMargin
);

router.delete('/stock-margin', stockMargin.deleteStockMargin);

router.get('/stock-margin', stockMargin.getStockMargin);

export default router;
