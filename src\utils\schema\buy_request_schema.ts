import Joi from 'joi';

export const createBuyRequestSchema = Joi.object({
    // id: Joi.string().uuid().required(),
    // user_id: Joi.string().uuid().required(),
    stock_ids: Joi.array()
        .items(
            Joi.object({
                stock_id: Joi.string().uuid().required(),
                is_available: Joi.boolean().default(true).optional(),
                is_action_taken: Joi.boolean().default(false).optional(),
                vendor_id: Joi.string().uuid().optional(),
                admin_id: Joi.string().uuid().optional()
            })
                .or('vendor_id', 'admin_id')
                .unknown()
        )
        .min(1)
        .required(),
    // vendor_ids: Joi.array().items(Joi.string().uuid()),
    // status: Joi.string().valid('PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED').required(),
    // updated_by_id: Joi.string().uuid().required(),
    payment_mode: Joi.string().valid('CREDIT_LIMIT', 'CREDIT_CARD', 'APPLE_PAY').required(),
    payment_type: Joi.string().valid('CREDIT', 'CASH').optional(),
    shipping_address: Joi.object().required(),
    billing_address: Joi.object().required(),
    total_amount: Joi.number().required(),
    shipment_id: Joi.string().uuid().required(),
    shipment_price: Joi.number().required(),
    card_holder_name: Joi.string()
        .regex(/^[A-Z\s]+$/)
        .uppercase()
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.empty': 'Card holder name cannot be empty',
            'string.pattern.base': 'Card holder name must only contain uppercase letters and spaces'
        }),
    card_number: Joi.string().creditCard().when('payment_mode', {
        is: 'CREDIT_CARD',
        then: Joi.required()
    }),
    exp_date: Joi.string()
        .pattern(/^(20[2-9][0-9]|2100)-(0[1-9]|1[0-2])$/)
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.pattern.base': 'Expiration Date must be in YYYY-MM format'
        }),
    cvv: Joi.string()
        .pattern(/^[0-9]{3,4}$/)
        .when('payment_mode', {
            is: 'CREDIT_CARD',
            then: Joi.required()
        })
        .messages({
            'string.pattern.base': 'CVV must be 3 or 4 digits'
        }),
    apple_pay_response: Joi.object().when('payment_mode', {
        is: 'APPLE_PAY',
        then: Joi.required()
    })
    // ref_trans_id: Joi.string().optional(),
    // trans_id: Joi.string().optional(),
    // transaction_payload: Joi.string().optional()
    // shipping_address: Joi.object({
    //     street: Joi.string().required(),
    //     city: Joi.string().required(),
    //     state: Joi.string().required(),
    //     postalCode: Joi.string().required(),
    //     country: Joi.string().required()
    // }).required(),
    // billing_address: Joi.object({
    //     street: Joi.string().required(),
    //     city: Joi.string().required(),
    //     state: Joi.string().required(),
    //     postalCode: Joi.string().required(),
    //     country: Joi.string().required()
    // }).required()
    // reject_reason: Joi.string().required()
});

export const createNewBuyRequestSchema = Joi.object({
    buy_request_id: Joi.string().uuid().required(),
    stock_ids: Joi.array()
        .items(
            Joi.object({
                stock_id: Joi.string().uuid().required(),
                is_available: Joi.boolean().valid(true).required(),
                is_action_taken: Joi.boolean().valid(true).required(),
                vendor_id: Joi.string().uuid().optional(),
                admin_id: Joi.string().uuid().optional()
            })
                .or('vendor_id', 'admin_id')
                .unknown()
        )
        .min(1)
        .required()
});

export const updateBuyRequestSchema = Joi.object({
    id: Joi.string().uuid().required(),
    // user_id: Joi.string().uuid().required(),
    stock_ids: Joi.array()
        .items(
            Joi.object({
                stock_id: Joi.string().uuid().required(),
                is_available: Joi.boolean().required(),
                is_action_taken: Joi.boolean().required(),
                vendor_id: Joi.string().uuid().optional(),
                admin_id: Joi.string().uuid().optional(),
                suggested_for: Joi.string().uuid().optional(),
                suggested_stock: Joi.object({
                    stock_id: Joi.string().uuid().required(),
                    is_available: Joi.boolean().required(),
                    is_action_taken: Joi.boolean().required(),
                    vendor_id: Joi.string().uuid().optional(),
                    admin_id: Joi.string().uuid().optional()
                })
                    .or('vendor_id', 'admin_id')
                    .unknown()
            })
                .or('vendor_id', 'admin_id')
                .unknown()
        )
        .min(0)
        .required(),
    // vendor_ids: Joi.array().items(Joi.string().uuid()),
    status: Joi.string().valid('PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED').required(),
    // updated_by_id: Joi.string().uuid().required(),
    // payment_mode: Joi.string().valid('CASH', 'ONLINE').optional(),
    // shipping_method: Joi.string().valid('free', 'ausPost', 'fedex').optional(),
    // shipping_address: Joi.object().optional(),
    // billing_address: Joi.object().optional(),
    card_holder_name: Joi.string()
        .regex(/^[A-Z\s]+$/)
        .uppercase()
        .optional(),
    card_number: Joi.string().creditCard().optional(),
    exp_date: Joi.string()
        .pattern(/^(20[2-9][0-9]|2100)-(0[1-9]|1[0-2])$/)
        .optional(),
    cvv: Joi.string()
        .pattern(/^[0-9]{3,4}$/)
        .optional(),
    apple_pay_response: Joi.object().optional(),
    reject_reason: Joi.string().optional()
});

export const vendorStockApprovalSchema = Joi.object({
    stock_id: Joi.string().uuid().required(),
    is_available: Joi.boolean().required(),
    buy_request_id: Joi.string().uuid().required(),
    vendor_id: Joi.string().uuid().required(),
    reject_reason: Joi.string().optional()
});

export const acceptBuyRequestSchema = Joi.object({
    stock_ids: Joi.array()
        .items(
            Joi.object({
                stock_id: Joi.string().uuid().required(),
                is_available: Joi.boolean().required(),
                is_action_taken: Joi.boolean().required(),
                vendor_id: Joi.string().uuid().optional(),
                admin_id: Joi.string().uuid().optional(),
                suggested_for: Joi.string().uuid().optional()
            })
                .or('vendor_id', 'admin_id')
                .unknown()
        )
        .optional(),
    card_holder_name: Joi.string()
        .regex(/^[A-Z\s]+$/)
        .uppercase()
        .optional(),
    card_number: Joi.string().creditCard().optional(),
    exp_date: Joi.string()
        .pattern(/^(20[2-9][0-9]|2100)-(0[1-9]|1[0-2])$/)
        .optional(),
    cvv: Joi.string()
        .pattern(/^[0-9]{3,4}$/)
        .optional(),
    apple_pay_response: Joi.object().optional()
});

export const marginApprovalSchema = Joi.object({
    buy_request_id: Joi.string().uuid().required(),
    stock_id: Joi.string().uuid().required(),
    updated_price: Joi.number().required()
});
