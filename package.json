{"name": "click2cater-service", "version": "1.0.0", "description": "E-carting", "main": "dist/index.js", "scripts": {"clean": "rimraf dist/", "lint": "tslint --fix -c  tslint.json 'src/**/**.ts'", "tsc": "tsc --project ./tsconfig.json", "prettier-all": "prettier --write 'src/**/*.+(ts|tsx|js|css|json)'", "precompile": "npm run prettier-all && npm run lint && npm run clean", "compile": "npm run tsc", "build": "npm run compile", "postbuild": "rimraf  dist/**/spec dist/**/*.spec.js && cp -r apidoc.json dist/apidoc.json && apidoc -i dist/controllers -o dist/public/apidoc", "watch": "tsc -w -p ./src -p ./tsconfig.json", "dev": "nodemon ./src/index.ts", "dev:debug": "export DEBUG_PORT=9229 && nodemon ./src/index.ts", "start": "node ./dist/index.js", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js -f src/typeorm/config/ormconfig.ts", "tslint-check": "tslint-config-prettier-check ./tslint.json"}, "prettier": {"trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 4, "semi": true}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.4.9", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "nodemon": "^3.0.1", "prettier": "^2.8.8", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "tslint-clean-code": "^0.2.10", "tslint-config-prettier": "^1.18.0", "typescript": "^5.1.6"}, "keywords": ["Shopify", "API", "Client"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@sentry/node": "^7.62.0", "aes-js": "^3.1.2", "apidoc": "^1.1.0", "aws-sdk": "^2.1570.0", "axios": "^1.7.2", "bcrypt": "^5.1.1", "bluebird": "^3.7.2", "bull": "^4.11.2", "cluster": "^0.7.7", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cryptr": "^6.3.0", "csvtojson": "^2.0.10", "discord-webhook-node": "^1.1.8", "dotenv": "^16.3.1", "ejs": "^3.1.10", "eslint": "^8.46.0", "express": "^4.18.2", "firebase-admin": "^12.2.0", "form-data": "^4.0.1", "joi": "^17.12.2", "json2csv": "^6.0.0-alpha.2", "json2xls": "^0.1.2", "jsonwebtoken": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "node-jsencrypt": "^1.0.0", "nodemailer": "^6.9.13", "pg": "^8.11.2", "postman-request": "^2.88.1-postman.40", "redis": "^4.7.0", "reflect-metadata": "^0.1.13", "sequelize": "^6.32.1", "winston": "^3.10.0", "winston-transport": "^4.5.0", "xls-to-json-lc": "^0.3.4", "xlsx-to-json-lc": "^0.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/DharmaTech123/diamond_company_api.git"}, "bugs": {"url": "https://github.com/DharmaTech123/diamond_company_api/issues"}, "homepage": "https://github.com/DharmaTech123/diamond_company_api#readme"}