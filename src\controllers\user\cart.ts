import { NextFunction, raw, Request, Response } from 'express';
import { logger } from '../../utils/logger';
import models from '../../models';
import { cartItemType, httpStatusCodes } from '../../utils/constants';
import { Op } from 'sequelize';

class Cart {
    /**
     * @api {post} /v1/auth/user/list-cart
     * @apiName ListCart
     * @apiGroup UserCart
     *
     *
     * @apiSuccess {Object} Cart.
     */
    async listCartItems(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling list cart items');
        try {
            /// get user id from request
            const userId = req[`id`];

            /// find all cart items
            const { count, rows: cartItems } = await models.carts.findAndCountAll({
                where: {
                    user_id: userId
                }
            });

            /// extract diamond type cart items
            const diamondCartItems = cartItems.filter((item: any) => item.type === cartItemType.diamond);

            /// update diamond details in cart items
            if (diamondCartItems.length) {
                /// extract dimond details from cart items
                const diamondIds = diamondCartItems.map((item: any) => {
                    const { stock_id, vendor_id, admin_id } = item.data.stock;
                    const result: any = {};
                    if (stock_id) {
                        result.stock_id = stock_id;
                    }
                    if (vendor_id) {
                        result.vendor_id = vendor_id;
                    }
                    if (admin_id) {
                        result.admin_id = admin_id;
                    }
                    return result;
                });

                /// fetch diamond details
                const diamondDetails = await models.stocks.findAll({
                    where: {
                        [Op.or]: diamondIds
                    },
                    raw: true
                });

                /// update in cartItems
                for (const cartItem of cartItems) {
                    /// for diamond type cart items, update the data with diamond details
                    if (cartItem.type !== cartItemType.diamond) {
                        continue;
                    }

                    /// find the diamond detail from diamondDetails
                    const diamondDetail = diamondDetails.find((item: any) => {
                        const { stock_id, vendor_id, admin_id } = cartItem.data.stock;
                        return (
                            item.stock_id === stock_id &&
                            (vendor_id ? item.vendor_id === vendor_id : true) &&
                            (admin_id ? item.admin_id === admin_id : true)
                        );
                    });

                    /// if diamond detail found, update the cart item data with diamond detail
                    if (diamondDetail) {
                        /// update markup
                        if (cartItem?.data?.stock?.markup) {
                            const updatedPricePerCaret =
                                diamondDetail?.price_per_caret +
                                (diamondDetail?.price_per_caret * cartItem?.data?.stock?.markup) / 100;
                            const updatedPrice = updatedPricePerCaret * diamondDetail?.weight;

                            /// update prices
                            diamondDetail.markup = cartItem?.data?.stock?.markup;
                            diamondDetail.price_per_caret = updatedPricePerCaret;
                            diamondDetail.final_price = updatedPrice;
                        }

                        cartItem.data.stock = diamondDetail;
                    }
                }

                /// deleted stones from stock table remove from cart items
                const toBeDeletedFromCartItems = cartItems.filter((item: any) => {
                    if (item.type !== cartItemType.diamond) {
                        return false;
                    }
                    const diamondDetail = diamondDetails.find((diamond: any) => {
                        const { stock_id, vendor_id, admin_id } = item.data.stock;
                        return (
                            diamond.stock_id === stock_id &&
                            (vendor_id ? diamond.vendor_id === vendor_id : true) &&
                            (admin_id ? diamond.admin_id === admin_id : true)
                        );
                    });
                    return !diamondDetail;
                });

                /// remove deleted stones from cart items
                if (toBeDeletedFromCartItems.length) {
                    await models.carts.destroy({
                        where: {
                            [Op.and]: [
                                { user_id: userId },
                                { type: cartItemType.diamond },
                                { id: { [Op.in]: toBeDeletedFromCartItems.map((item: any) => item.id) } }
                            ]
                        }
                    });

                    /// remove from list cart items inplace
                    for (const item of toBeDeletedFromCartItems) {
                        const index = cartItems.findIndex((cartItem: any) => cartItem.id === item.id);
                        if (index !== -1) {
                            cartItems.splice(index, 1);
                        }
                    }
                }
            }

            /// extract melee type cart items
            const meleeCartItems = cartItems.filter((item: any) => item.type === cartItemType.melee);

            /// update melee details in cart items
            if (meleeCartItems.length) {
                /// extract melee details from cart items
                const meleeConditions = meleeCartItems.map((item: any) => {
                    const melee = item?.data?.melee || {};
                    const conditions: any = {};

                    if (melee.shape) {
                        conditions.shape = { [Op.iLike]: melee.shape };
                    }

                    if (melee.growth_type) {
                        conditions.growth_type = { [Op.eq]: melee.growth_type };
                    }

                    if (melee.color) {
                        conditions.color = { [Op.iLike]: melee.color };
                    }

                    if (melee.clarity) {
                        conditions.clarity = { [Op.iLike]: melee.clarity };
                    }

                    if (melee.sieve_size) {
                        conditions.sieve_size = { [Op.eq]: melee.sieve_size };
                    }

                    if (melee.milimeter) {
                        conditions.milimeter = { [Op.eq]: melee.milimeter };
                    }

                    if (melee.pointer) {
                        conditions.pointer = { [Op.eq]: melee.pointer };
                    }

                    return conditions;
                });

                /// fetch melee details
                const meleeDetails = await models.melles.findAll({
                    where: {
                        [Op.or]: meleeConditions
                    },
                    raw: true
                });

                /// update price per caret in cart items
                for (const cartItem of cartItems) {
                    /// for melee type cart items, update the price per caret in data
                    if (cartItem.type !== cartItemType.melee) {
                        continue;
                    }

                    /// for melee type cart items, update the data with melee details
                    const meleeDetail = meleeDetails.find((item: any) => {
                        const melee = cartItem?.data?.melee || {};
                        return (
                            (melee.shape
                                ? String(melee?.shape).toLowerCase() === String(item?.shape).toLowerCase()
                                : true) &&
                            (melee.growth_type
                                ? String(melee?.growth_type).toLowerCase() === String(item?.growth_type).toLowerCase()
                                : true) &&
                            (melee.color
                                ? String(melee?.color).toLowerCase() === String(item?.color).toLowerCase()
                                : true) &&
                            (melee.clarity
                                ? String(melee?.clarity).toLowerCase() === String(item?.clarity).toLowerCase()
                                : true) &&
                            (melee.sieve_size
                                ? String(melee?.sieve_size).toLowerCase() === String(item.sieve_size).toLowerCase()
                                : true) &&
                            (melee.milimeter
                                ? String(melee?.milimeter).toLowerCase() === String(item?.milimeter).toLowerCase()
                                : true) &&
                            (melee.pointer
                                ? String(melee?.pointer).toLowerCase() === String(item?.pointer).toLowerCase()
                                : true)
                        );
                    });

                    /// if melee detail found, update the cart item data with melee detail
                    if (meleeDetail) {
                        cartItem.data.melee = {
                            ...meleeDetail,
                            price_per_caret: meleeDetail?.price_per_caret,
                            perDiamondCarat: cartItem?.data?.melee?.perDiamondCarat,
                            diamondQuantity: cartItem?.data?.melee?.diamondQuantity,
                            id: cartItem?.data?.melee?.id,
                            notes: cartItem?.data?.melee?.notes,
                            totalPrice: cartItem?.data?.melee?.totalPrice
                        };
                    }
                }
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Cart items retrieved successfully`,
                data: cartItems,
                count
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/add-to-cart
     * @apiName AddCart
     * @apiGroup UserCart
     *
     *
     * @apiSuccess {Object} Cart.
     */
    async addToCart(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling add to cart');
        try {
            /// get user id from request
            const userId = req[`id`];

            const { type, diamond_id, jewellery_id, melee_id, data, quantity } = req.body;

            /// check if cart item already exists
            const cartItem = await models.carts.findOne({
                where: {
                    [Op.and]: [
                        { user_id: userId },
                        { type },
                        diamond_id ? { diamond_id } : {},
                        jewellery_id ? { jewellery_id } : {},
                        melee_id ? { melee_id } : {}
                    ]
                }
            });

            /// throw error if cart item already exists
            if (cartItem) {
                throw new Error(`Cart item already exists`);
            }

            /// insert new cart item
            await models.carts.create({
                user_id: userId,
                type,
                diamond_id,
                jewellery_id,
                melee_id,
                data,
                quantity
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Item added to cart successfully`
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/remove-from-cart
     * @apiName RemoveCart
     * @apiGroup UserCart
     *
     *
     * @apiSuccess {Object} Cart.
     */
    async removeFromCart(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling remove from cart');
        try {
            /// get user id from request
            const userId = req[`id`];

            const { type, diamond_id, jewellery_id, melee_id } = req.body;

            /// find cart item
            const cartItem = await models.carts.findOne({
                where: {
                    [Op.and]: [
                        { user_id: userId },
                        { type },
                        diamond_id ? { diamond_id } : {},
                        jewellery_id ? { jewellery_id } : {},
                        melee_id ? { melee_id } : {}
                    ]
                }
            });

            if (!cartItem) {
                throw new Error(`Cart item not found`);
            }

            /// remove cart item
            await models.carts.destroy({
                where: {
                    [Op.and]: [{ id: cartItem.id }, { user_id: userId }]
                }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Item removed from cart successfully`
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/update-cart-quantity
     * @apiName UpdateCart
     * @apiGroup UserCart
     *
     *
     * @apiSuccess {Object} Cart.
     */
    async updateCartItem(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling update cart item');
        try {
            /// get user id from request
            const userId = req[`id`];

            const { type, diamond_id, jewellery_id, melee_id, quantity, notes } = req.body;

            /// update payload
            const updatePayload: any = {};

            /// find cart item
            const cartItem = await models.carts.findOne({
                where: {
                    [Op.and]: [
                        { user_id: userId },
                        { type },
                        diamond_id ? { diamond_id } : {},
                        jewellery_id ? { jewellery_id } : {},
                        melee_id ? { melee_id } : {}
                    ]
                }
            });

            if (!cartItem) {
                throw new Error(`Cart item not found`);
            }

            /// update cart item quantity
            if (quantity && type === cartItemType.jewellery) {
                updatePayload.quantity = quantity;
            }

            /// update cart item notes
            if (notes && type === cartItemType.melee) {
                updatePayload.data = { melee: { ...cartItem.data.melee, notes } };
            }

            /// update cart item quantity
            await models.carts.update(updatePayload, {
                where: {
                    [Op.and]: [{ id: cartItem.id }, { user_id: userId }]
                }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Item updated in cart successfully`
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {post} /v1/auth/user/remove-all-from-cart
     * @apiName RemoveAllCart
     * @apiGroup UserCart
     *
     *
     * @apiSuccess {Object} Cart.
     */
    async removeAllFromCart(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!Calling remove all from cart');
        try {
            /// get user id from request
            const userId = req[`id`];

            /// get type from request
            const { type } = req.body;

            /// check if type is all or not
            const isRemoveAllItems = String(req.body.type).toLocaleLowerCase() === 'all';

            /// find items by type
            const cartItems = await models.carts.findAll({
                where: {
                    [Op.and]: [{ user_id: userId }, isRemoveAllItems ? {} : { type }]
                }
            });

            /// check if cart items found or not
            if (!cartItems.length) {
                throw new Error(`Cart items not found`);
            }

            /// remove cart items
            await models.carts.destroy({
                where: {
                    [Op.and]: [{ user_id: userId }, isRemoveAllItems ? {} : { type }]
                }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Items removed from cart successfully`
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Cart();
