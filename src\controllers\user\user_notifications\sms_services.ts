import { NextFunction, Request, Response } from 'express';
import { logger } from '../../../utils/logger';
import axios from 'axios';
import { httpStatusCodes } from '../../../utils/constants';
import models from '../../../models';

class SMSServices {
    private static instance: SMSServices | null = null;

    constructor() {
        if (!SMSServices.instance) {
            SMSServices.instance = this;
        }

        return SMSServices.instance;
    }

    /// send OTP
    async sendOTP(req: Request, res: Response, next: NextFunction, isFunctionCall?: boolean) {
        try {
            const { mobile, OTP } = req.body; // Extract OTP from request body

            const { MSG91_AUTH_KEY, OTP_TEMPLATE_ID } = process.env;

            const url = 'https://control.msg91.com/api/v5/otp';

            if (!OTP_TEMPLATE_ID) {
                throw new Error('OTP_TEMPLATE_ID not found');
            }

            if (!MSG91_AUTH_KEY) {
                throw new Error('MSG91_AUTH_KEY not found');
            }

            if (!mobile) {
                throw new Error('Mobile number is required');
            }

            if (!OTP) {
                throw new Error('OTP is required');
            }

            logger.info(`OTP ${OTP}`);
            /// print params object
            logger.info(`params ${{
                otp_expiry: 10,
                template_id: OTP_TEMPLATE_ID,
                mobile: mobile?.replaceAll('-', '').replaceAll('+', ''),
                authkey: MSG91_AUTH_KEY,
                realTimeResponse: 1
            }}`);

            logger.info(`OTP ${OTP} ${req.ip}`);

            const response = await axios.request({
                method: 'post',
                url,
                params: {
                    otp_expiry: 10,
                    template_id: OTP_TEMPLATE_ID,
                    mobile: mobile?.replaceAll('-', '').replaceAll('+', ''),
                    authkey: MSG91_AUTH_KEY,
                    realTimeResponse: 1
                },
                headers: {
                    'Content-Type': 'application/JSON', // Match curl header exactly
                    'content-type': 'application/json' // Add duplicate header as in curl
                },
                data: {
                    OTP // Send custom OTP in request body
                }
            });

            if (response.data.type === 'error') {
                throw new Error(`send OTP error : ${JSON.stringify(response.data)}`);
            }

            logger.info(`OTP sent: ${JSON.stringify(response.data)}`);

            if (isFunctionCall) {
                return;
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `OTP Sent to ${mobile} Successfully`
            });
        } catch (error) {
            logger.error('Error:- OTP SENT:-', error);
            next(error);
        }
    }

    /// verify OTP
    async verifyOTP(req: Request, res: Response, next: NextFunction) {
        try {
            const { mobile, otp } = req.body;

            const { MSG91_AUTH_KEY } = process.env;

            const url = 'https://control.msg91.com/api/v5/otp/verify';

            const response = await axios.request({
                method: 'get',
                url,
                params: {
                    otp,
                    mobile: mobile?.replaceAll('-', '').replaceAll('+', '')
                },
                headers: {
                    authkey: MSG91_AUTH_KEY
                }
            });

            if (response.data.type === 'error') {
                throw new Error(`verify OTP error : ${JSON.stringify(response.data)}`);
            }

            const user = await models.users.findOne({
                where: { phone: mobile }
            });

            if (!user) {
                throw new Error('User not found');
            }

            await models.users.update({ is_phone_verified: true }, { where: { id: user.id } });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Phone Verified Successfully`
            });

            logger.info(`Verify OTP: ${JSON.stringify(response.data)}`);
        } catch (error) {
            logger.error(`Verify OTP Error: ${error}`);
            next(error);
        }
    }

    /// resend OTP
    async resendOTP(req: Request, res: Response, next: NextFunction) {
        try {
            const { mobile } = req.body;

            const { MSG91_AUTH_KEY } = process.env;

            const url = 'https://control.msg91.com/api/v5/otp/retry';

            const response = await axios.request({
                method: 'get',
                url,
                params: {
                    authkey: MSG91_AUTH_KEY,
                    retrytype: 'text',
                    mobile: mobile?.replaceAll('-', '').replaceAll('+', '')
                },
                headers: {
                    authkey: MSG91_AUTH_KEY
                }
            });

            if (response.data.type === 'error') {
                throw new Error(`Resend OTP error : ${JSON.stringify(response.data)}`);
            }

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `OPT resent to ${mobile}`
            });

            logger.info(`Resend OTP: ${JSON.stringify(response.data)}`);
        } catch (error) {
            logger.error(`Resend OTP Error: ${error}`);
            next(error);
        }
    }

    /// send SMS
    async sendSMS(req: Request, res: Response, next: NextFunction) {
        try {
            const { mobile, data } = req.body;

            const url = 'https://control.msg91.com/api/v5/flow';

            const { MSG91_AUTH_KEY } = process.env;

            const dataObj = {
                template_id: 'template_id', // SMS template ID
                short_url: '1', // 1 for enabling short URL, 0 for disabling (optional)
                realTimeResponse: '1', // Optional, if you need real-time response
                recipients: [
                    {
                        mobiles: mobile,
                        VAR1: data
                    }
                ]
            };
            const response = await axios.post(url, dataObj, {
                headers: {
                    authkey: MSG91_AUTH_KEY,
                    'Content-Type': 'application/json',
                    Accept: 'application/json'
                }
            });

            logger.info(`Send SMS: ${JSON.stringify(response.data)}`);
        } catch (error) {
            logger.error(`Send SMS Error: ${error}`);
        }
    }

    /// send whatsapp
    async sendWhatsAppMessage(req: Request, res: Response, next: NextFunction) {
        try {
            const { recipientNumber, data } = req.body;

            const url = 'https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/';

            const params = {
                integrated_number: '+919876543210',
                recipient_number: recipientNumber,
                content_type: 'text', // Example: "text"
                text: data
            };
            const response = await axios.post(url, null, {
                params,
                headers: {
                    accept: 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            logger.info(`SendWhatsApp ${JSON.stringify(response.data)}`);
        } catch (error) {
            logger.error(`Error: SendWhatsApp ${error}`);
        }
    }
}

export default new SMSServices();
