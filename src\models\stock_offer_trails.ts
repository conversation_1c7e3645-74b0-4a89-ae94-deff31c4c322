import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface StockOfferTrailsAttributes {
    id?: string; // id is an auto-generated UUID
    offer_id: string;
    user_id: string;
    stock_id: string;
    admin_id: string;
    vendor_id: string;
    offer_price: number;
    last_offer_price: number;
    updated_last_offer_price: number;
    updated_offer_price: number;
    buy_request_id: string;
    order_id: string;
    base_price: number;
    base_price_vendor: number;
    vendor_margin: number;
    status: string; // PENDING | ACCEPTED | REJECTED
    is_response_required: boolean;
    is_completed: boolean;
    last_offer_id: string;
    last_action_id: string;
    last_action_type: string; // USER | VENDOR | ADMIN
    payload: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    // Association Fields
}

interface StockOfferTrailsCreationAttributes extends Optional<StockOfferTrailsAttributes, 'id'> {}

interface StockOfferTrailsInstance
    extends Model<StockOfferTrailsAttributes, StockOfferTrailsCreationAttributes>,
        StockOfferTrailsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type StockOfferTrailsStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => StockOfferTrailsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const stock_offer_trails = sequelize.define<StockOfferTrailsInstance>(
        'stock_offer_trails',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            offer_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            last_offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            updated_last_offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            updated_offer_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            base_price: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            base_price_vendor: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            vendor_margin: {
                type: DataTypes.DOUBLE,
                defaultValue: 0,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            status: {
                type: DataTypes.ENUM('PENDING', 'ACCEPTED', 'REJECTED', 'REVISED'),
                defaultValue: 'PENDING',
                allowNull: false
            },
            is_response_required: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_completed: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            last_offer_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            last_action_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            last_action_type: {
                type: DataTypes.ENUM('USER', 'VENDOR', 'ADMIN'),
                allowNull: false
            },
            payload: {
                type: DataTypes.TEXT,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as StockOfferTrailsStatic;

    // TODO: make common function to sync
    // await stock_offer_trails.sync({ alter: true });

    return stock_offer_trails;
};
